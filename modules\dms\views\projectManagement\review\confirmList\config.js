// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        minWidth: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectNumber',
        label: '项目编号',
        minWidth: 160,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'orgName',
        label: '部门',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'poName',
        label: '产品经理',
        minWidth: 140,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectName',
        label: '项目名称',
        minWidth: 180,
        attrs: {
            'min-width': 180
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'pqaName',
        label: 'PPQA',
        minWidth: 180,
        attrs: {
            align: 'center'
        },
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectLevel',
        label: '项目级别',
        minWidth: 180,
        attrs: {
            align: 'center'
        },
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'startDate',
        label: '项目开始时间',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'planEndDate',
        label: '计划结束时间',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    }
];
