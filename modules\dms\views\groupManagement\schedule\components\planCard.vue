<!-- 基本信息组件 -->
<template>
    <div class="basic-info-form">
        <el-form :model="teamInfo" ref="basicForm" label-width="100px" class="basic-form">
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="计划名称:">
                        <span class="info-value">{{ teamInfo.planName }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="计划周期:">
                        <span class="info-value">{{ formatDateRange }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="可用工作日:">
                        <span class="info-value">{{ teamInfo.workDays }} 天</span>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item label="团队名称:">
                        <span class="info-value">{{ teamInfo.teamName }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="团队Leader:">
                        <span class="info-value">{{ teamInfo.teamLeader }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="提交日期:">
                        <span class="info-value">{{ teamInfo.submitDate || teamInfo.submissionDate }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script>
export default {
    name: 'PlanCard',
    props: {
        teamInfo: {
            type: Object,
            required: true
        }
    },
    computed: {
        // 格式化日期范围显示
        formatDateRange() {
            if (this.teamInfo.dateRange && this.teamInfo.dateRange.length === 2) {
                return `${this.teamInfo.dateRange[0]} 至 ${this.teamInfo.dateRange[1]}`;
            }
            return '';
        }
    }
};
</script>

<style lang="scss" scoped>
/* 基本信息样式 */
.basic-info-form {
    padding: 20px;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    margin-bottom: 20px;
}

.basic-form {
    .el-form-item {
        margin-bottom: 15px;
    }

    .el-form-item__label {
        color: #606266;
        font-weight: normal;
    }

    .info-value {
        color: #303133;
        font-size: 14px;
    }
}
</style>
