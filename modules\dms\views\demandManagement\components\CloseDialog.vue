<template>
    <div>
        <el-dialog title="关闭需求" :visible.sync="dialogVisible" width="600px" top="10vh">
            <div class="close-dialog-content">
                <!-- 确认信息 -->
                <!-- <div class="confirm-text">请确认是否关闭以下需求及其子需求：</div> -->

                <!-- 需求名称 -->
                <!-- <div v-if="!isMultiple" class="demand-name">
                    {{ rowData.demandName }}
                </div>
                <div v-else class="demand-list">
                    <div v-for="(item, index) in rowDataList" :key="index" class="demand-name">
                        {{ item.demandName }}
                    </div>
                </div> -->
                <!-- 查看需求详情链接 -->
                <!-- <el-button v-if="!isMultiple" type="text" class="detail-link" @click="viewDemandDetail">
                    查看需求详情
                </el-button> -->

                <!-- 统计信息 -->
                <div
                    v-if="
                        !isMultiple &&
                        (this.rowData.demandClass === '原始需求' || this.rowData.demandClass === '子原始需求')
                    "
                    class="stats-container"
                >
                    <div class="stat-item">
                        <div class="stat-label">预估工时</div>
                        <div class="stat-value">{{ rowData.estimateHour || 0 }} d</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">
                            实际工时 <span :class="getArrowClass()">{{ getArrowSymbol() }}</span>
                        </div>
                        <div>
                            <span :class="['stat-value', getArrowClass()]">
                                {{ rowData.actualHour || 0 }}
                            </span>
                            <span class="stat-value">d</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">成本偏差</div>
                        <div class="stat-value cost">{{ rowData.costDeviation || `0.0%` }}</div>
                    </div>
                </div>

                <!-- 关闭表单 -->
                <el-form :model="form" :rules="rules" ref="form" label-width="80px" class="close-form">
                    <el-form-item label="关闭原因" prop="closeReason" required>
                        <el-select v-model="form.closeReason" placeholder="请选择" style="width: 100%">
                            <el-option
                                v-for="item in closeReasonOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="备注">
                        <el-input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入备注信息">
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import CONSTANS from 'dms/constant/dict.js';

const { closeReason } = CONSTANS;
export default {
    name: 'CloseDialog',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        demandId: {
            type: String,
            default: ''
        },
        rowData: {
            type: Object,
            default: () => ({})
        },
        // 是否是多选
        isMultiple: {
            type: Boolean,
            default: false
        },
        // 多选选中的数据
        rowDataList: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            form: {
                closeReason: '',
                remark: ''
            },
            rules: {
                closeReason: [{ required: true, message: '请选择关闭原因', trigger: 'change' }]
            },
            closeReasonOptions: closeReason
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                // 重置表单
                this.form = {
                    closeReason: '',
                    remark: ''
                };
            }
        }
    },
    methods: {
        /**
         * 获取箭头符号
         * @returns {string} 箭头符号
         */
        getArrowSymbol() {
            const estimateHour = this.rowData.estimateHour || 0;
            const actualHour = this.rowData.actualHour || 0;

            if (actualHour === estimateHour) {
                // 相等显示对勾
                return '✓';
            } else if (actualHour > estimateHour) {
                // 大于显示向上箭头
                return '↑';
            }
            // 小于显示向下箭头
            return '↓';
        },

        /**
         * 获取箭头样式类
         * @returns {string} CSS类名
         */
        getArrowClass() {
            const estimateHour = this.rowData.estimateHour || 0;
            const actualHour = this.rowData.actualHour || 0;

            if (actualHour === estimateHour) {
                // 相等时的样式
                return 'arrow-equal';
            } else if (actualHour > estimateHour) {
                // 大于时的样式（红色）
                return 'arrow-up';
            }
            // 小于时的样式（绿色）
            return 'arrow-down';
        },

        /**
         * 查看需求详情
         */
        viewDemandDetail() {
            // 根据需求类型打开对应的详情弹窗
            const { demandClass } = this.rowData;

            this.$emit('view-detail', { demandId: this.demandId, demandClass });
        },

        /**
         * 确认提交
         */
        handleConfirm() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.save();
                } else {
                    return false;
                }
            });
        },

        /**
         * 保存关闭需求
         */
        async save() {
            try {
                await this.$confirm('确定要关闭吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const params = {
                    demandIdList: this.isMultiple ? this.rowDataList.map((i) => i.demandId) : [this.demandId],
                    closeReason: this.form.closeReason,
                    notes: this.form.remark,
                    storyClass: this.isMultiple ? this.rowDataList[0].storyClass : this.rowData.storyClass
                };

                const api = this.$service.dms.demand.closeDemand;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }

                this.$emit('success');
                this.$message.success('保存成功');
                this.closeDialog();
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('关闭需求失败:', error);
                }
            }
        },

        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$refs.form && this.$refs.form.resetFields();
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.close-dialog-content {
    padding: 20px 0 0 0;
    text-align: center;
}

.confirm-text {
    font-size: 16px;
    color: #303133;
    margin-bottom: 20px;
}

.demand-name {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 10px;
    word-break: break-all;
}

.detail-link {
    color: #409eff;
    text-decoration: underline;
    margin-bottom: 30px;
    font-size: 14px;
}

.stats-container {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    padding: 10px;
    border-radius: 4px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
}
.arrow-up {
    color: #d9001b;
    font-weight: bold;
}

.arrow-down {
    color: #70b603;
    font-weight: bold;
}

.arrow-equal {
    color: #6090ee;
    font-weight: bold;
}
.stat-value {
    font-size: 20px;
    font-weight: bold;
}

.close-form {
    text-align: left;
    margin-top: 20px;
}

.dialog-footer {
    text-align: center;
    padding-top: 20px;

    .el-button {
        min-width: 80px;
    }
}

.demand-list {
    max-height: 200px;
    overflow: auto;
}
::v-deep .el-form-item {
    margin-bottom: 20px;
}

::v-deep .el-form-item__label {
    color: #303133;
    font-weight: normal;
}

::v-deep .el-dialog__body {
    padding: 20px 30px;
}
</style>
