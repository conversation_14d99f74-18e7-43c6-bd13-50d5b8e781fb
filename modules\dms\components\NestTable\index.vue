<template>
    <el-form ref="form" :model="formData" :rules="formRules">
        <el-table
            ref="table"
            :data="data"
            style="width: 100%"
            class="dms-table"
            :header-cell-style="headerStyle"
            :show-header="showHeader"
            highlight-current-row
            @selection-change="handleRowClick"
        >
            <el-table-column type="selection" width="55" align="center"></el-table-column>
            <el-table-column v-if="nestedColumns && nestedColumns.length" type="expand">
                <template slot-scope="props">
                    <nest-table
                        :data="props.row[nestedDataKey]"
                        :columns="nestedColumns"
                        :nested-columns="[]"
                        :is-nested="true"
                        @selection-change="handleNestedSelectionChange"
                    ></nest-table>
                </template>
            </el-table-column>
            <el-table-column
                v-for="col in columns"
                :key="col.prop"
                :prop="col.prop"
                :label="col.label"
                :header-align="col.headerAlign || 'center'"
                :min-width="col.minWidth || col.width"
                :align="col.align || 'left'"
                :v-bind="$attrs"
            >
                <template v-if="col.required" slot="header"> <red-star></red-star>{{ col.label }} </template>
                <template slot-scope="scope">
                    <template v-if="col.editable">
                        <el-form-item
                            :prop="`data.${scope.$index}.${col.prop}`"
                            :rules="col.required ? getFieldRules(col) : []"
                            class="table-form-item"
                        >
                            <el-input
                                v-if="!col.type || col.type === 'text'"
                                v-model="scope.row[col.prop]"
                                size="small"
                                :disabled="col.disabled"
                                @change="handleFieldChange(scope.row, col)"
                            ></el-input>
                            <el-select
                                v-else-if="col.type === 'select'"
                                v-model="scope.row[col.prop]"
                                placeholder="请选择"
                                :disabled="col.disabled"
                                size="small"
                                @change="handleFieldChange(scope.row, col)"
                            >
                                <el-option
                                    v-for="item in col.options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                            <el-date-picker
                                v-else-if="col.type === 'date'"
                                v-model="scope.row[col.prop]"
                                type="date"
                                placeholder="选择日期"
                                size="small"
                                style="width: 100%"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                :disabled="col.disabled"
                                :clearable="false"
                                @change="handleFieldChange(scope.row, col)"
                            ></el-date-picker>
                            <el-input-number
                                v-else-if="col.type === 'number'"
                                v-model="scope.row[col.prop]"
                                size="small"
                                :min="col.min || 0"
                                :max="col.max"
                                :step="col.step || 1"
                                :controls="col.controls || false"
                                :disabled="col.disabled"
                                style="width: 100%"
                                @change="handleFieldChange(scope.row, col)"
                            ></el-input-number>
                            <people-selector
                                v-else-if="col.type === 'people'"
                                v-model="scope.row[col.prop]"
                                size="small"
                                :isMultipled="false"
                                :isRemote="false"
                                :disabled="col.disabled"
                                :options="col.options"
                                @change="handleFieldChange(scope.row, col)"
                            ></people-selector>
                        </el-form-item>
                    </template>
                    <span v-else>{{ scope.row[col.prop] }}</span>
                </template>
            </el-table-column>
        </el-table>
    </el-form>
</template>

<script>
import RedStar from 'dms/components/RedStar.vue';
import PeopleSelector from 'dms/components/PeopleSelector';

export default {
    name: 'NestTable',
    components: {
        RedStar,
        PeopleSelector
    },
    props: {
        data: {
            type: Array,
            default: () => []
        },
        columns: {
            type: Array,
            default: () => []
        },
        nestedColumns: {
            type: Array,
            default: () => []
        },
        nestedDataKey: {
            type: String,
            default: 'children'
        },
        headerStyle: {
            type: Object,
            default: () => ({ background: '#409EFF', color: '#fff' })
        },
        isNested: {
            type: Boolean,
            default: false
        },
        showHeader: {
            type: Boolean,
            default: true
        },
        isSelectable: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            // 主表格选中
            selectedMainRows: [],
            // 嵌套表格选中
            selectedNestedRows: [],
            // 存储所有层级的嵌套选择
            allNestedSelections: []
        };
    },
    computed: {
        // 表单数据，用于 el-form 校验
        formData() {
            return {
                data: this.data
            };
        },
        // 表单校验规则
        formRules() {
            const rules = {};
            this.data.forEach((row, index) => {
                this.columns.forEach((col) => {
                    if (col.required && col.editable) {
                        const fieldKey = `data.${index}.${col.prop}`;
                        rules[fieldKey] = this.getFieldRules(col);
                    }
                });
            });
            return rules;
        }
    },
    methods: {
        handleRowClick(selectedRows) {
            if (this.isNested) {
                this.selectedNestedRows = [...selectedRows];
                // 嵌套表格向上传递选择变化，让根组件处理
                this.$emit('selection-change', {
                    type: 'nested',
                    rows: selectedRows,
                    level: this.getNestedLevel()
                });
            } else {
                // 主表格选择变化
                this.selectedMainRows = [...selectedRows];
                this.emitSelectionChange();
            }
        },

        // 处理嵌套表格的选择变化
        handleNestedSelectionChange(selectionData) {
            if (!this.isNested) {
                // 只有根组件才处理和汇总所有选择
                this.updateNestedSelections(selectionData);
                this.emitSelectionChange();
            } else {
                // 中间层级继续向上传递
                this.$emit('selection-change', selectionData);
            }
        },

        // 更新嵌套选择数据
        updateNestedSelections(selectionData) {
            if (selectionData.type === 'nested') {
                // 更新对应层级的选择
                const existingIndex = this.allNestedSelections.findIndex((item) => item.level === selectionData.level);

                if (existingIndex >= 0) {
                    if (selectionData.rows.length > 0) {
                        this.allNestedSelections[existingIndex] = {
                            level: selectionData.level,
                            rows: [...selectionData.rows]
                        };
                    } else {
                        // 如果选择为空，移除该层级
                        this.allNestedSelections.splice(existingIndex, 1);
                    }
                } else if (selectionData.rows.length > 0) {
                    this.allNestedSelections.push({
                        level: selectionData.level,
                        rows: [...selectionData.rows]
                    });
                }

                // 更新汇总的嵌套选择
                this.selectedNestedRows = this.allNestedSelections.flatMap((item) => item.rows);
            }
        },

        // 发送统一格式的选择变化事件
        emitSelectionChange() {
            const allSelected = [...this.selectedMainRows, ...this.selectedNestedRows];
            this.$emit('selection-change', {
                mainRows: this.selectedMainRows,
                nestedRows: this.selectedNestedRows,
                allSelected
            });
        },

        // 获取当前嵌套层级
        getNestedLevel() {
            let level = 0;
            let parent = this.$parent;
            while (parent && parent.$options.name === 'NestTable') {
                level += 1;
                parent = parent.$parent;
            }
            return level;
        },

        // 清空选择
        clearSelection() {
            this.selectedMainRows = [];
            this.selectedNestedRows = [];
            this.allNestedSelections = [];
            this.$refs.table && this.$refs.table.clearSelection();

            // 递归清空所有嵌套表格的选择
            this.clearNestedSelections();

            // 发送清空后的选择状态
            if (!this.isNested) {
                this.emitSelectionChange();
            }
        },

        // 递归清空嵌套表格选择
        clearNestedSelections() {
            const nestedTables = this.$children.filter((child) => child.$options.name === 'NestTable');
            nestedTables.forEach((table) => {
                table.clearSelection();
            });
        },

        // 获取当前选中数据
        getSelectedData() {
            return {
                mainRows: this.selectedMainRows,
                nestedRows: this.selectedNestedRows,
                allSelected: [...this.selectedMainRows, ...this.selectedNestedRows]
            };
        },

        // 获取字段校验规则
        getFieldRules(col) {
            const rules = [];
            if (col.required) {
                rules.push({
                    required: true,
                    message: ` `,
                    trigger: col.type === 'select' ? 'change' : 'blur'
                });
            }
            // 可以根据 col.type 添加更多校验规则
            if (col.rules) {
                rules.push(...col.rules);
            }
            return rules;
        },

        // 公开的校验方法 - 使用 el-form 的校验
        validate() {
            return new Promise((resolve) => {
                this.$refs.form.validate((valid, invalidFields) => {
                    if (valid) {
                        // 如果有嵌套表格，也需要校验嵌套表格
                        this.validateNestedTables().then((nestedValid) => {
                            resolve({
                                valid: nestedValid,
                                invalidFields: nestedValid ? null : 'nested validation failed'
                            });
                        });
                    } else {
                        resolve({
                            valid: false,
                            invalidFields
                        });
                    }
                });
            });
        },

        // 校验嵌套表格
        validateNestedTables() {
            return new Promise((resolve) => {
                const nestedTables = this.$children.filter((child) => child.$options.name === 'NestTable');
                if (nestedTables.length === 0) {
                    resolve(true);
                    return;
                }

                const validationPromises = nestedTables.map((table) => table.validate());
                Promise.all(validationPromises).then((results) => {
                    const allValid = results.every((result) => result.valid);
                    resolve(allValid);
                });
            });
        },

        // 清除校验
        clearValidate() {
            this.$refs.form.clearValidate();
            // 清除嵌套表格的校验
            const nestedTables = this.$children.filter((child) => child.$options.name === 'NestTable');
            nestedTables.forEach((table) => {
                if (table.clearValidate) {
                    table.clearValidate();
                }
            });
        },

        // 处理字段变化事件
        handleFieldChange(row, col) {
            // 字段变化时触发数据更新事件
            this.$emit('field-change', {
                row,
                column: col,
                value: row[col.prop]
            });
        }
    }
};
</script>

<style scoped>
.table-form-item {
    margin-bottom: 0;
}

.table-form-item .el-form-item__content {
    line-height: normal;
}

.table-form-item .el-form-item__error {
    position: static;
    padding-top: 2px;
    font-size: 12px;
}

::v-deep .el-form-item__content {
    margin-left: 0 !important;
}
::v-deep .el-input--suffix .el-input__inner {
    padding-right: 0 !important;
}
</style>
