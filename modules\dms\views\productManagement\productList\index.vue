<template>
    <div class="project-management-list">
        <div class="content query-label-line2">
            <project-list
                v-model="activeNavTab"
                :query-params="queryParams"
                :query-config="queryConfig"
                :nav-items="navItems"
                :columns="columns"
                :data="productData"
                :load-data="query"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                :table-attrs="{ height: 'calc(100vh - 190px)' }"
                @search="handleSearch"
                @reset="handleReset"
                @pagination="query"
                :actions-width="110"
                config-section="productList"
                @nav-change="handleNavChange"
                @sort-change="handleSortChange"
            >
                <template #rightNav>
                    <div class="right-nav-container">
                        <el-button v-if="updateProductPermission" type="text" @click="handleAdd()"
                            ><i class="el-icon-plus"></i> 新建产品
                        </el-button>
                        <el-button type="text" @click="handleDownload()"
                            ><i class="el-icon-bottom"></i> 下载产品导入模板
                        </el-button>
                    </div>
                </template>
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button v-if="updateProductPermission" type="text" size="small" @click="handleEdit(row)"
                        >编辑</el-button
                    >
                    <el-button type="text" size="small" @click="handleDetails(row)">详情</el-button>
                </template>
            </project-list>
        </div>
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, productColumns, navItems } from './config.js';
import { saveParams, restoreParams, syncNavFromSearch } from 'dms/mixins/common.js';

export default {
    name: 'ProductList',
    components: {
        ProjectList
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '进行中',
            navItems,
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [],
            // 分页配置
            total: 0,
            page: 1,
            limit: 20,
            sortOrder: 'descending',
            sortKey: 'createDate',
            // 实际使用的查询参数
            params: {}
        };
    },
    computed: {
        // 新增/编辑权限
        updateProductPermission() {
            return this.$store.state.permission.btnDatas.includes('Button-updateProduct');
        }
    },
    mounted() {
        // 优先使用保存的参数
        restoreParams(this, 'productList_params', queryParams);
        this.query();
    },
    beforeDestroy() {
        // 页面卸载时保存参数
        saveParams(this, 'productList_params');
    },
    methods: {
        // 加载产品数据
        async query() {
            try {
                // 日期处理
                if (this.params.createDate && this.params.createDate.length > 0) {
                    this.params.createStartDate = this.params.createDate[0];
                    this.params.createEndDate = this.params.createDate[1];
                }
                if (this.params.closedDate && this.params.closedDate.length > 0) {
                    this.params.closedStartDate = this.params.closedDate[0];
                    this.params.closedEndDate = this.params.closedDate[1];
                }
                const params = {
                    currentPage: this.page,
                    pageSize: this.limit,
                    ...this.params,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder
                };
                const res = await this.$service.dms.product.getProductList(params);
                if (res.code === '0000') {
                    this.productData = res.data?.list || [];
                    // 添加序号
                    if (res?.data?.startRow) {
                        this.productData = this.productData.map((item, index) => {
                            item.index = res.data.startRow + index;
                            return item;
                        });
                    }
                    this.total = res.data?.total || 0;
                } else {
                    this.$message.error(res.message || '获取产品列表失败');
                }
            } catch (error) {
                console.error(error, '查询失败');
                this.$message.error('系统异常');
            }
        },
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            this.sortKey = prop;
            this.sortOrder = order;
            this.query();
        },
        // 处理搜索
        handleSearch() {
            this.params = this.$tools.cloneDeep(this.queryParams);
            // 搜索时重置到第一页
            this.page = 1;

            // 搜索时同步导航状态 - 根据搜索条件自动设置对应的导航标签
            syncNavFromSearch(this);

            this.query();
        },

        // 处理重置
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
            // 重置时重置到第一页
            this.page = 1;
            this.query();
        },
        // 新增产品
        handleAdd() {
            this.$router.push({
                name: 'AddProduct',
                query: { type: 'add', row: {} }
            });
        },

        // 编辑产品
        handleEdit(row) {
            this.$router.push({
                name: 'AddProduct',
                query: { type: 'edit', row }
            });
        },
        // 查看产品详情
        handleDetails(row) {
            this.$router.push({ name: 'DetailsProduct', query: { id: row.id } });
        },
        async handleDownload() {
            const params = {
                excelName: '产品创建模板',
                excelCode: 'createProduct'
            };
            const stream = await this.$service.dms.product.toTemplateExcel({ ...params });
            this.$tools.downloadExprotFile(stream, '模版数据', 'xlsx').catch((e) => {
                this.$tools.message.err(e || '导出失败');
            });
        },
        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.queryParams[queryField] = field;
            this.params = this.$tools.cloneDeep(this.queryParams);
            this.query();
        }
    }
};
</script>

<style lang="scss" scoped>
.project-management-list {
    padding: 20px;
}
.right-nav-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 10px;
}
</style>
