<template>
    <div class="group-management-list">
        <div class="content query-label-line2">
            <group-list
                v-model="activeNavTab"
                :nav-items="navItems"
                :query-params="queryParams"
                :query-config="queryConfig"
                :columns="columns"
                :data="productData"
                :load-data="query"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                :table-attrs="{ height: 'calc(100vh - 190px)' }"
                config-section="groupList"
                :actions-width="100"
                @search="handleSearch"
                @reset="handleReset"
                @nav-change="handleNavChange"
                @pagination="query"
            >
                <template #rightNav>
                    <el-button
                        v-if="updateGroupPermission"
                        type="text"
                        @click="createDemand"
                        class="create-demand-button"
                        ><i class="el-icon-plus"></i> 新增团队信息
                    </el-button>
                </template>
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button type="text" size="small" @click="handleView(row)">详情</el-button>
                    <el-button v-if="updateGroupPermission" type="text" size="small" @click="handleEdit(row)"
                        >编辑</el-button
                    >
                </template>
            </group-list>
        </div>
        <GroupUpdateDialog
            :visible.sync="groupUpdateDialogVisible"
            :groupId="currentId"
            :type="editType"
            @success="query"
        ></GroupUpdateDialog>
    </div>
</template>

<script>
import GroupList from 'dms/views/groupManagement/components/groupList/index.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';
import GroupUpdateDialog from './GroupUpdateDialog.vue';
import { syncNavFromSearch } from 'dms/mixins/common.js';

export default {
    name: 'GroupManagementList',
    components: {
        GroupList,
        GroupUpdateDialog
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '进行中',
            // 导航栏配置
            navItems,
            // 查询参数
            params: { projectStatus: '进行中' },
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 团队数据
            productData: [{}],
            // 分页配置
            total: 0,
            page: 1,
            limit: 20,
            groupUpdateDialogVisible: false,
            editType: 'view',
            currentId: null
        };
    },

    computed: {
        // 新增/编辑团队
        updateGroupPermission() {
            return this.$store.state.permission.btnDatas.includes('Button-updateGroup');
        }
    },
    mounted() {
        this.query();
    },
    methods: {
        createDemand() {
            this.currentId = null;
            this.editType = 'add';
            this.groupUpdateDialogVisible = true;
        },
        // 加载团队数据
        async query() {
            try {
                const params = {
                    currentPage: this.page,
                    pageSize: this.limit,
                    projectCategory: 'T',
                    ...this.params
                };

                // 日期处理
                if (params.startDate && params.startDate.length > 0) {
                    params.startDateStart = params.startDate[0];
                    params.startDateEnd = params.startDate[1];
                }
                if (params.endDate && params.endDate.length > 0) {
                    params.endDateStart = params.endDate[0];
                    params.endDateEnd = params.endDate[1];
                }
                // 团队和项目共用一个接口
                const res = await this.$service.dms.project.getProjectList(params);

                if (res.code === '0000') {
                    this.productData = res.data?.list || [];
                    this.total = res.data?.total || 0;
                    // 添加序号
                    if (res?.data?.startRow) {
                        this.productData = this.productData.map((item, index) => {
                            item.index = res.data.startRow + index;
                            return item;
                        });
                    }
                } else {
                    this.$message.error(res.message || '获取团队列表失败');
                }
            } catch (error) {
                console.error('加载团队数据失败:', error);
                this.$message.error('加载团队数据失败');
            }
        },

        /**
         * 处理搜索
         * @param {Object} searchData - 搜索表单数据
         */
        handleSearch(searchData) {
            const { searchParams } = searchData;
            this.params = {
                ...searchParams
            };
            this.page = 1;
            // 搜索时同步导航状态 - 根据搜索条件自动设置对应的导航标签
            syncNavFromSearch(this);
            this.query();
        },
        /**
         * 处理重置
         */
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
            // 重置时重置到第一页
            this.page = 1;

            this.query();
        },
        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.queryParams[queryField] = field;
            this.params = this.$tools.cloneDeep(this.queryParams);
            this.query();
        },

        // 编辑团队
        handleEdit(row) {
            this.currentId = row.projectId;
            this.editType = 'edit';
            this.groupUpdateDialogVisible = true;
        },

        // 查看团队详情
        handleView(row) {
            this.currentId = row.projectId;
            this.editType = 'view';
            this.groupUpdateDialogVisible = true;
        }
    }
};
</script>

<style lang="scss" scoped>
.group-management-list {
    padding: 20px;
    .el-descriptions {
        margin: 0px;
        ::v-deep .el-descriptions-item__label {
            width: 140px;
            font-size: 13px;
            font-weight: 600;
        }
        ::v-deep .el-descriptions-item__content {
            font-size: 13px;
        }
    }
}
.create-demand-button {
    margin-right: 20px;
}
</style>
