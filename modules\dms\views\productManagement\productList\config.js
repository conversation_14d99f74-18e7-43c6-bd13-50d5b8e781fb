import CommonItems from 'snbcCommon/common/form-items.js';
import dictData from 'dms/constant/dict.js';

const { select, input, dateRange, productLineSelector } = CommonItems;

// 产品线
const productLine = {
    ...productLineSelector,
    name: '产品线',
    modelKey: 'productLine'
};

const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName'
};

const productCode = {
    ...input,
    name: '产品编号',
    modelKey: 'productCode'
};

const productOwner = {
    ...input,
    name: 'Product Owner',
    modelKey: 'productOwner'
};

const productId = {
    ...input,
    name: '产品ID',
    modelKey: 'productId'
};

const status = {
    ...select,
    name: '产品状态',
    modelKey: 'status',
    elOptions: dictData.statusData
};

const createDate = {
    ...dateRange,
    name: '创建日期',
    modelKey: 'createDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

const closedDate = {
    ...dateRange,
    name: '关闭日期',
    modelKey: 'closedDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [productLine, productName, productCode, productOwner, productId, status, createDate, closedDate]
};

// 查询条件参数
export const queryParams = {
    productLine: '',
    productName: '',
    productCode: '',
    productOwner: '',
    productId: '',
    status: '',
    createDate: [],
    closedDate: []
};

// 导航栏配置
export const navItems = [
    { field: '', name: '所有', queryField: 'status' },
    { field: '进行中', name: '进行中', queryField: 'status' },
    { field: '已暂停', name: '已暂停', queryField: 'status' },
    { field: '已关闭', name: '已关闭', queryField: 'status' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        minWidth: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'id',
        label: 'ID',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productCode',
        label: '产品编号',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productName',
        label: '产品名称',
        minWidth: 200,
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productOwnerName',
        label: 'Product Owner',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'createDate',
        label: '创建日期',
        minWidth: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'productStatus',
        label: '产品状态',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'project',
        label: '负责团队/项目',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'closedDate',
        label: '关闭日期',
        minWidth: 160,
        attrs: {
            align: 'center'
        }
    }
];
