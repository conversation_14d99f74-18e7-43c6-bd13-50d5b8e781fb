<template>
    <div class="view-box">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button @click="cancel">返回</el-button>
        </div>
        <div>
            <!-- 产品结构 -->
            <formula-title :title="basicTitle"></formula-title>
            <div class="product-structure-container">
                <detail-tree :data="rootNode" :productName="productName"></detail-tree>
            </div>
            <!-- 操作记录 -->
            <formula-title :title="operationTitle"></formula-title>
            <operation-record :collapseItems="collapseItems"></operation-record>
        </div>
    </div>
</template>

<script>
import formulaTitle from '../components/formulaTitle.vue';
import operationRecord from '../components/operationRecord.vue';
import DetailTree from 'dms/views/productManagement/components/DetailTree.vue';

export default {
    components: { formulaTitle, operationRecord, DetailTree },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            basicTitle: '产品结构',
            operationTitle: '操作记录',
            checkId: '',
            productId: '',
            productName: '',
            collapseItems: [],
            // 产品子节点数据（从接口获取的子节点）
            productChildren: []
        };
    },
    computed: {
        // 根节点（产品节点），包含从接口获取的子节点
        rootNode() {
            return [
                {
                    productId: this.productId,
                    moduleName: this.productName,
                    type: 'product',
                    moduleType: 'P',
                    sort: 1,
                    children: this.productChildren
                }
            ];
        }
    },
    created() {
        this.checkId = this.$route.query.checkId;
        this.productId = this.$route.query.productId;
        this.productName = this.$route.query.productName;
        this.getActions(this.checkId);
        this.getProductTreedata();
    },
    methods: {
        /*
         * 获取产品结构数据
         */
        async getProductTreedata() {
            try {
                const params = { productId: this.productId, checkId: this.checkId };
                const res = await this.$service.dms.product.getProductChangeInfo(params);
                if (res.code !== '0000') {
                    this.$tools.message.err(res.message || '系统异常');
                    this.productChildren = [];
                    return;
                }
                // 将接口返回的子节点数据赋值给productChildren
                this.productChildren = res.data || [];
            } catch (error) {
                console.error('获取产品结构失败', error);
                this.$tools.message.err('系统异常');
                this.productChildren = [];
            }
        },
        async getActions(id) {
            try {
                const params = {
                    objectId: id
                };
                const res = await this.$service.dms.product.getActions(params);
                if (res.code === '0000') {
                    this.collapseItems = res.data || [];
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        },
        cancel() {
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped>
.sprint-btn {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    border-bottom: 1px solid #dcdfe6;
}
</style>
