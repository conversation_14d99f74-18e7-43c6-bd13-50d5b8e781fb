<template>
    <div class="view-box">
        <detail-tree :data="rootNode" :productName="productName"></detail-tree>
    </div>
</template>

<script>
import DetailTree from 'dms/views/productManagement/components/DetailTree.vue';

export default {
    components: {
        DetailTree
    },
    props: {
        id: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            productChildren: [],
            productName: ''
        };
    },
    computed: {
        // 根节点（产品节点），包含从接口获取的子节点
        rootNode() {
            return [
                {
                    productId: this.id,
                    moduleName: this.productName,
                    type: 'product',
                    moduleType: 'P',
                    sort: 1,
                    children: this.productChildren
                }
            ];
        }
    },
    created() {
        this.getModuleInfo();
        this.getProductInfo();
    },
    methods: {
        async initData() {
            try {
                // 1. 先获取产品基本信息
                await this.getProductInfo();
                // 2. 再获取产品结构子节点数据
                await this.getModuleInfo();
            } catch (error) {
                console.error('初始化数据失败：', error);
            }
        },
        async getModuleInfo() {
            try {
                const params = {
                    productId: this.id
                };
                const res = await this.$service.dms.product.getProductModuleInfo(params);
                if (res.code === '0000') {
                    // 接口返回的data就是子节点数组，直接赋值给children
                    this.productChildren = res.data || [];
                } else {
                    this.$message.error(res.message);
                    this.productChildren = [];
                }
            } catch (error) {
                this.$tools.message.err('系统异常');
                this.productChildren = [];
            }
        },
        async getProductInfo() {
            try {
                const params = {
                    productId: this.id
                };
                const res = await this.$service.dms.product.getProductInfo(params);
                if (res.code === '0000') {
                    // 从产品详情中提取名称
                    this.productName = res.data?.productName;
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$tools.message.err('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
