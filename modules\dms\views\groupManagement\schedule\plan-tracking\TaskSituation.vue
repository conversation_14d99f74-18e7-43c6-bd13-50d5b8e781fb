<!--任务情况管理 -->
<template>
    <div>
        <common-list
            v-model="activeTab"
            :nav-items="navItems"
            :query-config="queryConfig"
            :query-params="queryParams"
            :columns="tableColumns"
            :data="taskList"
            :total="total"
            :page.sync="page"
            :limit.sync="size"
            :actions-width="200"
            @sort-change="handleSortChange"
            @search="query"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @pagination="query"
        >
            <template #rightNav>
                <el-button type="text" @click="addTask" class="add-task-button">
                    <i class="el-icon-plus"></i> 新增任务
                </el-button>
            </template>

            <!-- 进度列自定义渲染 -->
            <template #progress="{ row }">
                <el-progress
                    :percentage="row.progress || 0"
                    :stroke-width="8"
                    :show-text="true"
                    :color="getProgressColor(row.progress)"
                ></el-progress>
            </template>
        </common-list>
    </div>
</template>

<script>
import CommonList from 'dms/components/CommonList/index.vue';
import { queryParams, tableColumns, navItems, queryConfig, sortMap } from './config.js';

export default {
    name: 'TaskSituation',
    components: { CommonList },
    data() {
        return {
            activeTab: '进行中',
            tableColumns,
            navItems,
            taskList: [],
            queryParams: this.$tools.cloneDeep(queryParams),
            total: 0,
            // 顶部导航栏的查询条件
            navQuery: { status: '进行中' },
            sortKey: '',
            sortOrder: '',
            page: 1,
            size: 20
        };
    },
    computed: {
        queryConfig() {
            const quickQueryItems = queryConfig.items.find((item) => item.modelKey === 'quickQuery');
            if (quickQueryItems) {
                quickQueryItems.changeHandler = this.handleRadio;
            }
            return queryConfig;
        },
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        },
        groupOptions() {
            return this.$store.state.dms.groupOption;
        }
    },
    watch: {
        groupValue(newVal) {
            if (newVal) {
                this.query();
            }
        }
    },
    mounted() {
        this.groupValue && this.query();
    },
    methods: {
        addTask() {
            if (!this.groupValue) {
                this.$message.warning('请选择团队');
                return;
            }
            this.$router.push({
                path: './addTask'
            });
        },

        /**
         * 查询
         */
        async query() {
            if (!this.groupValue) {
                this.$message.warning('请选择团队');
                return;
            }
            try {
                const params = {
                    ...this.queryParams,
                    ...this.navQuery,
                    projectId: this.groupValue,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder,
                    currentPage: this.page,
                    pageSize: this.size
                };

                // 这里需要替换为实际的API接口
                const api = this.$service.dms.task.getTaskList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.taskList = res.data.list;
                this.total = res.data.total || 0;
            } catch (error) {
                console.error('查询任务列表失败:', error);
            }
        },

        // 重置
        handleReset() {
            this.queryParams = this.$tools.cloneDeep(queryParams);
            this.query();
        },

        // 获取进度条颜色
        getProgressColor(progress) {
            if (progress >= 80) return '#67c23a';
            if (progress >= 50) return '#e6a23c';
            return '#f56c6c';
        },

        /**
         * 处理排序变化
         * @param {Object} sortInfo - 排序信息 { column, prop, order }
         */
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            const orderMap = {
                descending: 'DESC',
                ascending: 'ASC'
            };
            this.sortKey = sortMap[prop];
            this.sortOrder = orderMap[order];
            this.query();
        },

        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            this.navQuery = {};
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.navQuery[queryField] = field;
            this.query();
        }
    }
};
</script>

<style lang="scss" scoped>
.add-task-button {
    margin-right: 20px;
}

::v-deep .query-area {
    margin-top: 10px;
    .area-search {
        height: 50px;
        display: flex;
        justify-content: space-between;
    }
}

// 任务名称可点击样式
.task-name {
    color: #409eff;
    &:hover {
        text-decoration: underline;
    }
}

// 进度条样式调整
::v-deep .el-progress {
    .el-progress__text {
        font-size: 12px;
    }
}

// 标签样式调整
::v-deep .el-tag {
    border-radius: 4px;
}
</style>
