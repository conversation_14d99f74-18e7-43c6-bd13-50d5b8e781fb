<template>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="500px" height="300px" @close="closeDialog">
        <el-form ref="form" :model="form" label-width="80px" :rules="rules">
            <el-form-item label="小组名称" v-if="showFormItems.includes('小组名称')" prop="groupName">
                <el-input v-model="form.groupName" placeholder="请输入小组名称" />
            </el-form-item>
            <el-form-item label="小组组长" v-if="showFormItems.includes('小组组长')" prop="groupLeader">
                <DepartmentPeopleSelector
                    v-model="form.groupLeader"
                    :isMultipled="false"
                    style="width: 100%"
                    placeholder="请选择小组组长"
                    ref="groupLeader"
                />
            </el-form-item>
            <el-form-item label="成员姓名" v-if="showFormItems.includes('成员姓名')" prop="account">
                <DepartmentPeopleSelector
                    v-model="form.account"
                    style="width: 100%"
                    placeholder="请选择成员姓名"
                    ref="employee"
                />
            </el-form-item>
        </el-form>
        <div class="confirm-button">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">保 存</el-button>
        </div>
    </el-dialog>
</template>

<script>
import DepartmentPeopleSelector from 'dms/components/DepartmentPeopleSelector';
import { getSelectedLabel } from 'dms/mixins/common';

export default {
    name: 'OrgAddDialog',
    components: { DepartmentPeopleSelector },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        // 新增/编辑
        type: {
            type: String,
            default: 'add'
        },
        currentNodeData: {
            type: Object,
            default: () => ({})
        },
        orgType: {
            type: String,
            default: 'department'
        }
    },
    data() {
        return {
            form: {
                account: '',
                groupName: '',
                groupLeader: ''
            },
            rules: {
                groupName: [{ required: true, message: '请输入小组名称' }],
                groupLeader: [{ required: true, message: '请选择小组组长' }],
                account: [{ required: true, message: '请选择成员姓名' }]
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        },
        // 根据类型和组织类型显示不同的表单项
        showFormItems() {
            if (this.type === 'edit') {
                return ['小组名称', '小组组长'];
            }
            if (this.orgType === 'department') {
                return ['小组名称', '小组组长'];
            }
            if (this.orgType === 'team') {
                return ['成员姓名'];
            }
            return [];
        },
        title() {
            if (this.type === 'add') {
                if (this.orgType === 'department') {
                    return '新增团队';
                }
                if (this.orgType === 'team') {
                    return '新增团队成员';
                }
            }
            if (this.type === 'edit') {
                return '编辑团队';
            }
            return '';
        }
    },
    watch: {
        visible(newVal) {
            if (newVal) {
                if (this.type === 'edit') {
                    this.form = {
                        groupName: this.currentNodeData.teamName,
                        groupLeader: this.currentNodeData.teamLeaderAccount
                    };
                }
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.form = {
                account: '',
                groupName: '',
                groupLeader: ''
            };
            this.$refs.form.resetFields();
            this.dialogVisible = false;
        },
        /**
         * 验证表单
         * @return {Promise} 验证是否通过
         */
        validate() {
            return this.$refs.form.validate();
        },
        /**
         * 确认
         */
        async handleSubmit() {
            const isValidate = await this.validate();
            if (!isValidate) {
                return;
            }
            if (this.type === 'add') {
                this.add();
            } else {
                this.edit();
            }
        },
        /**
         * 新增
         */
        async add() {
            let params = {};
            // 新增小组和小组组长
            if (this.orgType === 'department') {
                params = {
                    // 小组名称
                    teamName: this.form.groupName,
                    // 小组组长域账号
                    teamLeaderAccount: this.form.groupLeader,
                    // 二级部门code
                    departmentCode: this.currentNodeData.id
                };
            } else {
                // 新增小组成员
                params = this.form.account.map((i) => ({
                    account: i,
                    teamId: this.currentNodeData.id
                }));
            }
            const api =
                this.orgType === 'department'
                    ? this.$service.dms.group.addGroupMembersAndGroupLeader
                    : this.$service.dms.group.addGroupMembers;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$message.success('保存成功');
                this.closeDialog();
                this.$emit('success');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 编辑
         */
        async edit() {
            const params = {
                id: this.currentNodeData.id,
                teamLeaderName: getSelectedLabel(this.$refs.groupLeader),
                teamLeaderAccount: this.form.groupLeader,
                teamName: this.form.groupName,
                departmentCode: this.currentNodeData.belongOrgCode
            };

            const api = this.$service.dms.group.editGroupInfo;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$message.success('保存成功');
                this.closeDialog();
                this.$emit('success');
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
// 预先定义表格行最小高度，避免布局抖动
::v-deep .el-table .el-table__row {
    min-height: 60px;
}

.progress-input-container {
    display: flex;
    align-items: center;
    justify-content: center;

    .percent-sign {
        margin-left: 2px;
        font-size: 14px;
    }
}
.confirm-button {
    display: flex;
    justify-content: flex-end;
    margin-top: 25px;
}

// 添加loading时的样式覆盖
::v-deep .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.5);
}
</style>
