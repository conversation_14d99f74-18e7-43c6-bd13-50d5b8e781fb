<!-- 计划管理——冲刺评价 -->
<template>
    <div class="add-plan-container">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button type="primary" @click="save('草稿')">保存</el-button>
            <el-button type="primary" @click="save('审核中')">提交</el-button>
            <el-button @click="handleReturn" type="primary">返回</el-button>
        </div>
        <div>
            <!-- 基本信息 -->
            <formula-title :title="basicTitle"></formula-title>
            <div class="basic-info-form">
                <el-form
                    :model="basicForm"
                    :rules="basicFormRules"
                    ref="basicForm"
                    label-width="100px"
                    class="basic-form"
                >
                    <el-row :gutter="240">
                        <el-col :span="24">
                            <el-form-item prop="dateRange" label="计划周期">
                                <div class="plan-period-container">
                                    <el-date-picker
                                        v-model="basicForm.dateRange"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 300px"
                                        :clearable="false"
                                        @change="handleDateRangeChange"
                                    >
                                    </el-date-picker>
                                    <div class="period-options">
                                        <el-radio-group
                                            v-model="basicForm.periodType"
                                            size="small"
                                            @change="handlePeriodChange"
                                        >
                                            <el-radio label="两周">两周</el-radio>
                                            <el-radio label="三周">三周</el-radio>
                                            <el-radio label="一个月">一个月</el-radio>
                                            <el-radio label="两个月">两个月</el-radio>
                                            <el-radio label="三个月">三个月</el-radio>
                                        </el-radio-group>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item prop="planName" label="计划名称">
                                {{ basicForm.planName }}
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="可用工作日">
                                <div class="work-days-display">
                                    <span class="work-days-number">{{ basicForm.workDays }}</span>
                                    <span class="work-days-unit">天</span>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>

            <!--  需求任务 -->
            <formula-title :title="planTitle"></formula-title>
            <NestedPlanList :list.sync="demandList" :saveFlag="saveFlag" @success="getPlanListData"></NestedPlanList>

            <!--  团队活动 -->
            <formula-title :title="teamActivityTitle"></formula-title>
            <div class="team-activity-container">
                <TeamActivity ref="teamActivityRef" :list="teamTaskList"></TeamActivity>
            </div>

            <!--  问题修复 -->
            <formula-title :title="problemFixTitle"></formula-title>
            <div class="problem-fix-container">
                <ProblemFix ref="problemFixRef" :list="problemTaskList"></ProblemFix>
            </div>

            <!--  工时负载率 -->
            <formula-title :title="worktimeTitle"></formula-title>
            <div class="chart-box">
                <el-card class="chart-right">
                    <worktime-chart ref="worktimeRef" :info="workTimeChartInfo"></worktime-chart>
                </el-card>
            </div>
        </div>
    </div>
</template>

<script>
import formulaTitle from '../components/formulaTitle.vue';
import worktimeChart from '../components/worktimeChart';
import moment from 'moment';
import NestedPlanList from '../../components/nestedPlanList';
import TeamActivity from './components/TeamActivity.vue';
import ProblemFix from './components/ProblemFix.vue';
import RedStar from 'dms/components/RedStar.vue';

export default {
    name: 'AddPlan',
    components: { formulaTitle, worktimeChart, NestedPlanList, TeamActivity, ProblemFix, RedStar },
    props: {},
    data() {
        return {
            // 内部维护的id，这个页面新建的时候一开始就需要id
            innerId: '',
            // 标题
            basicTitle: '基本信息',
            planTitle: '需求任务',
            teamActivityTitle: '团队活动',
            worktimeTitle: '工时负载率',
            problemFixTitle: '问题修复',
            // 基本信息表单数据
            basicForm: {
                dateRange: [],
                periodType: '两周',
                planName: '',
                workDays: 0
            },
            // 基本信息表单验证规则
            basicFormRules: {
                dateRange: [{ required: true, message: '请选择计划周期', trigger: 'change' }]
            },
            // 团队活动列表
            teamTaskList: [],
            // 问题修复列表
            problemTaskList: [],
            saveFlag: 0,
            demandList: [],
            isSaving: false,
            // 工时负载率echarts图
            workTimeChartInfo: {},
            currentStatus: ''
        };
    },
    computed: {
        startDate() {
            return this.basicForm.dateRange && this.basicForm.dateRange[0] ? this.basicForm.dateRange[0] : null;
        },
        endDate() {
            return this.basicForm.dateRange && this.basicForm.dateRange[1] ? this.basicForm.dateRange[1] : null;
        },
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        },
        groupOptions() {
            return this.$store.state.dms.groupOption;
        }
    },
    watch: {
        'basicForm.dateRange': function (newVal) {
            this.calculateWorkDays();
            if (newVal && newVal.length === 2) {
                const startDate = moment(newVal[0]).format('YYYY.MM.DD');
                const endDate = moment(newVal[1]).format('YYYY.MM.DD');
                let teamName = '';
                // 从现有的 planName 中提取团队名
                const match = this.basicForm.planName.match(/^(.*?)\(/);
                if (match && match[1]) {
                    teamName = match[1];
                }
                this.basicForm.planName = `${teamName}(${startDate}-${endDate})`;
            }
        }
    },
    async mounted() {
        // 编辑
        if (this.$route?.query?.id) {
            this.innerId = this.$route?.query?.id;
        } else {
            // 新建
            await this.addPlan();
        }
        this.getBaseInfo();
        this.getGroupMembers();
        this.getTableList();
        this.getResourceLoadData();
    },
    methods: {
        /**
         * 一开始就调用新增接口，拿到新建后的id
         */
        async addPlan() {
            try {
                const params = {
                    ...this.groupOptions,
                    pmAccount: this.groupOptions.pm
                };
                const api = this.$service.dms.plan.addPlan;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    this.$router.back();
                    return Promise.reject();
                }
                this.innerId = res.data;
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        },
        // 处理日期范围变化
        handleDateRangeChange(dateRange) {
            this.basicForm.periodType = '';
            if (dateRange && dateRange.length === 2) {
                this.calculateWorkDays();
            } else {
                this.basicForm.workDays = 0;
            }
        },

        // 处理周期类型变化
        handlePeriodChange(periodType) {
            if (!this.startDate) {
                this.$message.warning('请先选择开始日期');
                return;
            }

            const startMoment = moment(this.startDate);
            let endMoment;

            // 根据选择的周期类型计算结束日期
            switch (periodType) {
                case '两周':
                    endMoment = startMoment.clone().add(2, 'weeks');
                    break;
                case '三周':
                    endMoment = startMoment.clone().add(3, 'weeks');
                    break;
                case '一个月':
                    endMoment = startMoment.clone().add(1, 'month');
                    break;
                case '两个月':
                    endMoment = startMoment.clone().add(2, 'months');
                    break;
                case '三个月':
                    endMoment = startMoment.clone().add(3, 'months');
                    break;
                default:
                    return;
            }

            this.basicForm.dateRange = [this.startDate, endMoment.format('YYYY-MM-DD')];
        },

        // 计算工作日天数
        async calculateWorkDays() {
            if (!this.startDate || !this.endDate) return;
            try {
                const params = {
                    startDate: this.startDate,
                    endDate: this.endDate
                };
                const api = this.$service.dms.plan.getWorkDayInPeriod;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.basicForm.workDays = res.data;
            } catch (error) {
                console.error('获取可用工时失败:', error);
            }
        },
        handleReturn() {
            this.$router.back();
        },
        /**
         * 获取基本信息
         */
        async getBaseInfo() {
            try {
                const params = {
                    id: this.innerId
                };
                const api = this.$service.dms.plan.getPlanBaseInfo;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                const { startDate, endDate } = res.data;
                this.basicForm = { ...this.basicForm, ...res.data };
                this.basicForm.dateRange = [startDate, endDate];
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },
        /**
         * 获取基本信息
         */
        async saveBaseInfo() {
            try {
                const params = {
                    id: this.innerId,
                    status: this.currentStatus,
                    ...this.basicForm,
                    startDate: this.basicForm.dateRange[0],
                    endDate: this.basicForm.dateRange[1],
                    ...this.groupOptions,
                    pmAccount: this.groupOptions.pm
                };

                const api = this.$service.dms.plan.savePlanBaseInfo;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        },
        /**
         * 获取团队下的人员列表
         */
        async getGroupMembers() {
            try {
                const params = {
                    planId: this.innerId
                };
                const api = this.$service.dms.plan.getMemebersInGroup;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                const groupMembers = res.data.map((i) => ({ loginName: i.account, employeeName: i.name }));
                this.$store.dispatch('dms/setGroupMembers', groupMembers);
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },
        /**
         * 获取需求任务、团队活动、问题修复的表格数据
         */
        async getTableList() {
            try {
                const params = {
                    planId: this.innerId
                };
                const api = this.$service.dms.plan.getPlanTaskList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.demandList = res.data.storyTaskList;
                this.teamTaskList = res.data.teamTaskList;
                this.problemTaskList = res.data.problemTaskList || [];
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },
        save(status) {
            if (!this.innerId) return;
            this.currentStatus = status;
            // 设置保存标志
            this.isSaving = true;
            // 触发子组件发送数据
            this.saveFlag += 1;
        },
        getPlanListData(value) {
            this.demandList = value;

            // 如果是在保存过程中，执行保存逻辑
            if (this.isSaving) {
                this.isSaving = false;
                this.performSave();
            }
        },
        async performSave() {
            await this.saveBaseInfo();
            await this.saveTableList();
            if (this.currentStatus === '审核中') {
                this.submitReview();
            } else {
                this.$message.success('保存成功');
            }
        },
        async saveTableList() {
            try {
                // 获取 ProblemFix 组件的表格数据
                const problemTaskList = this.$refs.problemFixRef ? this.$refs.problemFixRef.getTableData() : [];

                const params = {
                    projectId: this.groupValue,
                    executionPlanId: this.innerId,
                    storyTaskList: this.demandList,
                    teamTaskList: this.teamTaskList,
                    problemTaskList,
                    leaveTaskList: []
                };
                console.log(params, 'params');

                const api = this.$service.dms.plan.updatePlanTaskList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        },
        async submitReview() {
            try {
                const params = {
                    planId: this.innerId,
                    projectId: this.groupValue
                };

                const api = this.$service.dms.plan.submitReview;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                this.$message.success('提交成功');
                this.$router.back();
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        },
        /**
         * 获取工时负载率数据
         */
        async getResourceLoadData() {
            try {
                const params = {
                    planId: this.innerId
                };

                const api = this.$service.dms.plan.getPlanWorkHourChart;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                console.log(res.data, 'res.data');

                this.workTimeChartInfo = res.data;
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.add-plan-container {
    padding: 10px 20px 15px 20px;
}
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
.chart-box {
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: space-between;
}
.clearfix {
    height: 30px;
}
.chart-right {
    width: 100%;
    height: 100%;
}
.el-card {
    padding: 10px !important;
}
::v-deep .el-card__header {
    height: 30px !important;
}
.load-rate-item {
    border-bottom: 1px solid #dcdfe6;
}

/* 基本信息样式 */
.basic-info-form {
    padding: 20px;
    background: #fff;
}

.basic-form {
    .el-form-item {
        margin-bottom: 20px;
    }
}

.plan-period-container {
    display: flex;
    align-items: center;
    gap: 20px;

    .period-options {
        .el-radio-group {
            .el-radio-button {
                margin-right: 0;
            }
        }
    }
}

.work-days-display {
    display: flex;
    align-items: center;

    .work-days-number {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-right: 4px;
    }

    .work-days-unit {
        color: #606266;
    }
}

/* 团队活动样式 */
.team-activity-container {
    margin-bottom: 20px;
}

/* 问题修复样式 */
.problem-fix-container {
    margin-bottom: 20px;
}
</style>
