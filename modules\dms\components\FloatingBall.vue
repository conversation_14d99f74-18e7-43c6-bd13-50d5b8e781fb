<template>
    <div
        :class="['floating-ball', getFloatingBallClass]"
        :style="{
            left: position.x + 'px',
            top: position.y + 'px'
        }"
        @mousedown="onMouseDown"
        @click.stop="toggleMenu"
        @mouseenter="showMenu"
        @mouseleave="hideMenu"
        :data-side="leftOrRight"
    >
        <!-- 主按钮 -->
        <div class="main-button">
            <slot name="default">
                <i class="el-icon-position"></i>
            </slot>
        </div>

        <!-- 子菜单 -->
        <transition name="flip">
            <div v-if="menuVisible" class="sub-menu" :style="submenuStyle">
                <div v-for="(item, index) in menuItems" :key="index" @click="handleItemClick(item)" class="menu-item">
                    <svg-icon v-if="item.icon" :icon-class="item.icon" style="padding: 0; margin-right: 5px"></svg-icon>
                    <i
                        v-if="item.type === 'delete'"
                        class="el-icon-delete"
                        style="padding: 0; margin-right: 5px; color: #f00"
                    ></i>
                    <span>{{ item.title }}</span>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
export default {
    name: 'FloatingBall',
    props: {
        // 菜单项配置
        menuItems: {
            type: Array,
            default: () => []
        },
        position: {
            type: Object,
            default: () => ({
                x: window.innerWidth - 60,
                y: window.innerHeight / 2
            })
        }
    },
    data() {
        return {
            menuVisible: false,
            isDragging: false,
            isDrag: false,
            offset: { x: 0, y: 0 },
            ticking: false,
            latestDragEvent: null,
            hideTimer: null,
            showTime: null
        };
    },
    computed: {
        getFloatingBallClass() {
            return this.menuVisible ? 'show-menu' : '';
        },
        leftOrRight() {
            return this.position.x < window.innerWidth / 2 ? 'left' : 'right';
        },
        submenuStyle() {
            // .floating-ball 容器的宽度
            const buttonWidth = 50;
            // 贴合间距
            const menuGap = 5;
            const positionStyle = {};

            if (this.leftOrRight === 'left') {
                // 菜单出现在右侧
                positionStyle.left = `${buttonWidth + menuGap}px`;
            } else {
                // 菜单出现在左侧
                positionStyle.right = `${buttonWidth + menuGap}px`;
            }
            return positionStyle;
        }
    },
    beforeDestroy() {
        // 组件销毁前清理定时器
        if (this.hideTimer) {
            clearTimeout(this.hideTimer);
            this.hideTimer = null;
        }
        // 清理全局事件监听
        document.removeEventListener('mousemove', this.onMouseMove);
        document.removeEventListener('mouseup', this.onMouseUp);
    },
    methods: {
        toggleMenu(e) {
            if (this.isDrag) {
                e.preventDefault();
                this.isDrag = false;
            } else {
                this.menuVisible = !this.menuVisible;
                this.$nextTick(() => {
                    this.snapToEdge();
                });
            }
        },
        handleItemClick(item) {
            if (item.handler && typeof item.handler === 'function') {
                item.handler();
            }
        },
        onMouseDown(e) {
            // 只响应左键点击
            if (e.button !== 0) return;

            this.isDragging = true;
            this.isDrag = false;
            // 拖拽开始时，立刻隐藏菜单，降低渲染开销
            this.menuVisible = false;
            this.offset.x = e.clientX - this.position.x;
            this.offset.y = e.clientY - this.position.y;

            // 添加全局鼠标事件监听
            document.addEventListener('mousemove', this.onMouseMove);
            document.addEventListener('mouseup', this.onMouseUp);

            // 阻止默认行为和事件冒泡
            e.preventDefault();
            e.stopPropagation();
        },
        onMouseMove(e) {
            if (!this.isDragging) return;

            this.isDrag = true;
            this.latestDragEvent = e;

            if (!this.ticking) {
                window.requestAnimationFrame(() => {
                    if (this.latestDragEvent) {
                        this.position.x = this.latestDragEvent.clientX - this.offset.x;
                        this.position.y = this.latestDragEvent.clientY - this.offset.y;
                    }
                    this.ticking = false;
                });
                this.ticking = true;
            }
        },
        onMouseUp() {
            if (!this.isDragging) return;

            this.isDragging = false;
            this.latestDragEvent = null;
            this.ticking = false;

            // 移除全局事件监听
            document.removeEventListener('mousemove', this.onMouseMove);
            document.removeEventListener('mouseup', this.onMouseUp);

            this.snapToEdge();
            // 等待一个事件循环再重置 isDrag，用于与 click 事件做区分
            this.$nextTick(() => {
                this.isDrag = false;
            });
        },
        snapToEdge() {
            // 吸附边距
            const buffer = 10;
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            // 处理水平方向吸附
            if (this.position.x < buffer) {
                this.position.x = 0;
            } else if (this.position.x > windowWidth - this.$el.offsetWidth - buffer) {
                this.position.x = windowWidth - this.$el.offsetWidth - buffer;
            }

            // 处理垂直方向吸附
            if (this.position.y < buffer) {
                this.position.y = 0;
            } else if (this.position.y > windowHeight - this.$el.offsetHeight - buffer) {
                this.position.y = windowHeight - this.$el.offsetHeight;
            }
        },
        showMenu() {
            // 仅当未进行拖动时才在悬浮时显示菜单
            if (!this.isDragging) {
                // 清除之前的隐藏定时器
                if (this.hideTimer) {
                    clearTimeout(this.hideTimer);
                    this.hideTimer = null;
                }
                this.menuVisible = true;
                this.showTime = Date.now();
            }
        },
        hideMenu() {
            // 清除之前的隐藏定时器
            if (this.hideTimer) {
                clearTimeout(this.hideTimer);
                this.hideTimer = null;
            }

            // 计算已显示的时间
            const elapsedTime = this.showTime ? Date.now() - this.showTime : 0;
            // 最少显示时间
            const minShowTime = 1200;

            if (elapsedTime < minShowTime) {
                // 如果显示时间不足，延迟隐藏
                const remainingTime = minShowTime - elapsedTime;
                this.hideTimer = setTimeout(() => {
                    this.menuVisible = false;
                    this.hideTimer = null;
                }, remainingTime);
            } else {
                // 已显示超过1秒，立即隐藏
                this.menuVisible = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.floating-ball {
    position: fixed;
    z-index: 9999;
    width: 50px;
    height: 50px;
    cursor: move;
    user-select: none;
    perspective: 1000px;

    .main-button {
        width: 100%;
        height: 100%;
        background-color: #3370ff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 20px;
        transition: all 0.3s ease;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
        &:hover {
            background-color: #3370ff;
        }
    }

    .sub-menu {
        position: absolute;
        bottom: 0;
        width: 120px;
        background-color: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        padding: 8px 0;
        .menu-item {
            width: 100%;
            height: 40px;
            padding: 0 5px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #606266;
            &:hover {
                background-color: #f5f7fa;
                color: #3370ff;
            }
        }
        .select-icon {
            width: 20px;
            height: 20px;
        }
    }

    .slide-enter-active,
    .slide-leave-active {
        transition: all 0.3s ease;
    }

    .slide-enter,
    .slide-leave-to {
        opacity: 0;
        transform: translateX(-10px);
    }
}
.floating-ball.show-menu {
    .main-button {
        background-color: #3370ff;
    }
}

// 根据展开方向调整子菜单位置
.floating-ball[data-side='left'] {
    .sub-menu {
        left: 55px;
    }
}
.floating-ball[data-side='right'] {
    .sub-menu {
        right: 55px;
    }
}

// Flip 过渡动画
.flip-enter-active,
.flip-leave-active {
    transition: all 0.3s ease;
}

.flip-enter,
.flip-leave-to {
    opacity: 0;
    transform: scale(0.8);
}
</style>
