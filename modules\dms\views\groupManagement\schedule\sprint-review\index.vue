<!-- 冲刺评价 -->
<template>
    <div>
        <common-list
            v-model="activeTab"
            :nav-items="navItems"
            :query-config="queryConfig"
            :query-params="queryParams"
            :columns="tableColumns"
            :data="data"
            :actions-width="150"
            :showPagination="false"
            @sort-change="handleSortChange"
            @search="query"
            @reset="handleReset"
            @nav-change="handleNavChange"
        >
            <!-- 操作列 -->
            <template #actions="{ row }">
                <el-button size="mini" type="text" @click="handleCheck(row)">审核</el-button>
                <el-button size="mini" type="text" @click="handleDetails(row)">详情</el-button>
            </template>
        </common-list>
    </div>
</template>

<script>
import CommonList from 'dms/components/CommonList/index.vue';
import { dateTypeOptions, tableColumns, navItems, queryParams, queryConfig, sortMap } from './config.js';

export default {
    name: 'SprintReview',
    components: { CommonList },
    data() {
        return {
            dateTypeOptions,
            activeTab: '结论待定',
            tableColumns,
            navItems,
            queryParams: this.$tools.cloneDeep(queryParams),
            data: [],
            // 顶部导航栏的查询条件
            navQuery: { status: '结论待定' },
            sortKey: '',
            sortOrder: ''
        };
    },
    computed: {
        queryConfig() {
            const quickQueryItems = queryConfig.items.find((item) => item.modelKey === 'quickQuery');
            quickQueryItems.changeHandler = this.handleRadio;

            return queryConfig;
        },
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        }
    },
    watch: {
        groupValue(newVal) {
            if (newVal) {
                this.query();
            }
        }
    },
    mounted() {
        this.handleRadio('最近一年');
        this.groupValue && this.query();
    },
    methods: {
        /**
         * 查询
         */
        async query() {
            if (!this.groupValue) {
                this.$message.warning('请选择团队');
                return;
            }
            let startDate = '';
            let endDate = '';
            const { dateRange } = this.queryParams;
            if (Array.isArray(dateRange) && dateRange[0]) {
                startDate = dateRange[0];
                endDate = dateRange[1];
            }

            try {
                const params = {
                    ...this.queryParams,
                    ...this.navQuery,
                    startDate,
                    endDate,
                    projectId: this.groupValue,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder
                };
                const api = this.$service.dms.plan.getEvaluationReviewList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.reviewList = res.data.list.map((i, index) => ({
                    ...i,
                    index: index + 1
                }));
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },

        // 重置
        handleReset() {
            this.queryParams = this.$tools.cloneDeep(queryParams);
            this.query();
        },

        handleRadio(dateType) {
            const target = this.dateTypeOptions.find((item) => item.dateType === dateType);
            this.queryParams.dateRange = target.dateRange;
        },

        /**
         * 处理排序变化
         * @param {Object} sortInfo - 排序信息 { column, prop, order }
         */
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            const orderMap = {
                descending: 'DESC',
                ascending: 'ASC'
            };
            this.sortKey = sortMap[prop] || prop;
            this.sortOrder = orderMap[order];
            this.query();
        },

        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            this.navQuery = {};
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.navQuery[queryField] = field;
            this.query();
        },

        // 审核
        handleCheck(row) {
            this.$router.push({
                path: './planEvaluteAction',
                query: { id: row.demandNo }
            });
        },

        // 详情
        handleDetails(row) {
            this.$router.push({
                path: './planDetail',
                query: { id: row.demandNo }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .query-area {
    margin-top: 10px;
    .area-search {
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
</style>
