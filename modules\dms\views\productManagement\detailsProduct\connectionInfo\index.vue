<!-- 干系人 -->
<template>
    <div class="view-box">
        <formula-title :title="title"></formula-title>
        <!-- 行业总监、产品经理、PQA 信息 -->
        <el-descriptions :column="3" border>
            <el-descriptions-item label="行业总监">{{ projectData.director || '-' }}</el-descriptions-item>
            <el-descriptions-item label="产品经理">{{ projectData.productOwner || '-' }}</el-descriptions-item>
            <el-descriptions-item label="PQA">{{ projectData.pqa || '-' }}</el-descriptions-item>
        </el-descriptions>
        <!-- 团队/项目列表 -->
        <div class="project-teams">
            <el-table :data="projectData.projectMembers" border style="width: 100%; margin-top: 15px">
                <el-table-column prop="projectName" label="团队/项目名称" width="350px"></el-table-column>
                <el-table-column prop="members" label="成员名称"></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';

export default {
    components: { formulaTitle },
    props: {
        id: {
            type: String,
            default: () => ''
        }
    },
    data() {
        return {
            title: '当前信息',
            projectData: {}
        };
    },
    created() {
        // 当组件创建时，如果 id 有值则查询产品信息
        if (this.id) {
            this.getStakeholder();
        }
    },
    methods: {
        // 查询干系人
        async getStakeholder() {
            try {
                const params = {
                    productId: this.id
                };
                const res = await this.$service.dms.product.getStakeholder(params);
                if (res.code === '0000') {
                    this.projectData = res.data;
                } else {
                    this.$message.error(res.message || '获取产品详情失败');
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';
.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 13px;
    }
}
</style>
