import CommonItems from 'snbcCommon/common/form-items.js';
import dictData from 'dms/constant/dict.js';

const { select, input, dateRange, peopleSelector, productLineSelector, number } = CommonItems;

// 原始需求名称
const originalDemandName = {
    ...input,
    name: '原始需求名称',
    modelKey: 'originalDemandName'
};
// 用户需求名称
const demandName = {
    ...input,
    name: '用户需求名称',
    modelKey: 'demandName'
};

// 需求ID
const demandId = {
    ...input,
    name: '需求ID',
    modelKey: 'demandId'
};
// 需求等级
const storyLevel = {
    ...select,
    name: '需求等级',
    modelKey: 'storyLevel',
    elOptions: dictData.storyLevelData
};
// 需求创建人
const proposer = {
    ...input,
    name: '需求创建人',
    modelKey: 'proposer'
};
// 创建时间
const proposalTime = {
    ...dateRange,
    name: '创建时间',
    modelKey: 'proposalTime'
};
// 需求负责人
const responsiblePerson = {
    ...peopleSelector,
    name: '需求负责人',
    modelKey: 'responsiblePerson',
    elSelectAttrs: {
        size: 'small',
        isMultipled: false
    }
};
// 期望交付日期
const expectedDate = {
    ...dateRange,
    name: '期望交付日期',
    modelKey: 'expectedDate'
};
// 产品线
const productLine = {
    ...productLineSelector,
    name: '产品线',
    modelKey: 'productLine'
};
// 产品
const assProductId = {
    ...select,
    name: '产品',
    modelKey: 'assProductId',
    elOptions: []
};
// 团队/项目
const assProjectId = {
    ...select,
    name: '团队/项目',
    modelKey: 'assProjectId',
    elOptions: []
};

// 优先级
const priority = {
    ...select,
    name: '优先级',
    modelKey: 'priority',
    elOptions: dictData.priorityData
};

// 变更次数
const changeTimes = {
    ...number,
    name: '变更次数',
    modelKey: 'changeTimes',
    elInputNumberAttrs: {
        controls: false,
        min: 0,
        class: 'change-time-input'
    }
};
// 需求进度
const demandProgress = {
    ...select,
    name: '需求进度',
    modelKey: 'demandProgress',
    elOptions: dictData.demandProgressData
};
// 部署情况
const deploy = {
    ...select,
    name: '部署情况',
    modelKey: 'deploy',
    elOptions: dictData.depolyStituation
};

// 发布情况
const publish = {
    ...select,
    name: '发布情况',
    modelKey: 'publish',
    elOptions: dictData.publistSituation
};

// 查询参数初始化
export const queryParams = {
    originalDemandName: '',
    demandName: '',
    demandId: '',
    storyLevel: '',
    proposer: '',
    proposalTime: [],
    responsiblePerson: '',
    expectedDate: [],
    productLine: '',
    assProductId: '',
    assProjectId: '',
    priority: '',
    changeTimes: undefined,
    demandProgress: '',
    deploy: '',
    publish: ''
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [
        originalDemandName,
        demandName,
        demandId,
        storyLevel,
        proposer,
        proposalTime,
        responsiblePerson,
        expectedDate,
        productLine,
        assProductId,
        assProjectId,
        priority,
        changeTimes,
        demandProgress,
        deploy,
        publish
    ]
};
export const navItems = [
    { field: '', name: '所有', queryField: 'demandStatus' },
    {
        field: '待审核',
        name: '待审核',
        queryField: 'demandStatus'
    },
    {
        field: '未开始',
        name: '未开始',
        queryField: 'demandStatus'
    },
    {
        field: '已计划',
        name: '已计划',
        queryField: 'demandStatus'
    },
    {
        field: '进行中',
        name: '进行中',
        queryField: 'demandStatus'
    },
    {
        field: '已完成',
        name: '已完成',
        queryField: 'demandStatus'
    },
    {
        field: '已关闭',
        name: '已关闭',
        queryField: 'demandStatus'
    }
];
