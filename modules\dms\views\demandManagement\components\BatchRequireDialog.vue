<template>
    <div>
        <el-dialog title="快速创建" :visible.sync="dialogVisible" width="1000px" top="10vh" @close="closeDialog">
            <el-input v-model="formData.textContent" type="textarea" :rows="26" :placeholder="computedPlaceholder" />
            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'CloseDialog',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        batchType: {
            type: String,
            default: 'user'
        }
    },

    data() {
        return {
            formData: {
                textContent: ''
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        computedPlaceholder() {
            if (this.batchType === 'user') {
                return `复制多条完整需求用例，系统自动识别，如：
需求编号    XXXXXXXX     需求名称     XXXXXXXXXXXXXXXXXXXXXX
需求描述     XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
验收标准     XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
需求编号    XXXXXXXX     需求名称     XXXXXXXXXXXXXXXXXXXXXX
需求描述     XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
验收标准     XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX`;
            }
            return `复制多条完整产品需求用例，系统自动识别，如：
产品需求编号    XXXXXXXX     对应用户需求编号   XXXXXXXXX
产品需求名称     XXXXXXXXXXXXXXXXXXXXXX
产品需求内容     XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
验收标准     XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
优先性    XXXXXXXX     
产品需求编号    XXXXXXXX     对应用户需求编号   XXXXXXXXX
产品需求名称     XXXXXXXXXXXXXXXXXXXXXX
产品需求内容     XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
验收标准     XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
优先性    XXXXXXXX`;
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.formData.textContent = '';
            this.dialogVisible = false;
        },
        async handleConfirm() {
            const api =
                this.batchType === 'user'
                    ? this.$service.dms.original.parseUserDemand
                    : this.$service.dms.original.parseProductDemand;

            const params = {
                textContent: this.formData.textContent
            };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                if (res.data.list.length === 0) {
                    this.$message.warning('无有效数据，请重新输入');
                    return;
                }
                this.$message.success('解析成功');
                this.$emit('success', res?.data?.list || []);
                this.closeDialog();
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
