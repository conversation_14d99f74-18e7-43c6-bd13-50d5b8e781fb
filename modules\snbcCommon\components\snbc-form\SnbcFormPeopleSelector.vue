<template>
    <el-form-item v-bind="elFormItemAttrs">
        <PeopleSelector
            class="select"
            v-model="config.modelObj[config.modelKey]"
            v-bind="elSelectAttrs"
            @input="handleChange"
            :clearable="true"
        >
        </PeopleSelector>
    </el-form-item>
</template>
<script>
import PeopleSelector from 'dms/components/PeopleSelector';

export default {
    name: 'SnbcFormPeopleSelector',
    components: { PeopleSelector },
    props: {
        /**
         * SnbcFormSelect组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elOptions: [],
                    elFormItemAttrs: {},
                    elSelectAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-select组件默认属性设置
            defaultElSelectAttrs: {
                'filterable': true,
                'clearable': true,
                'collapse-tags': true
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-select组件应用属性
        elSelectAttrs() {
            return {
                ...this.defaultElSelectAttrs,
                placeholder: `请选择${this.config.name}`,
                size: 'medium',
                ...(this.config.elSelectAttrs || {})
            };
        }
    },
    methods: {
        // 下拉数据切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        }
    }
};
</script>
<style scoped>
.select {
    width: 100%;
    font-weight: initial;
}
</style>
