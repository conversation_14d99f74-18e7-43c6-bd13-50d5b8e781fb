<template>
    <div class="example-container">
        <demand-list
            :key="demandListKey"
            ref="demandList"
            v-model="activeNavTab"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            :data="treeData"
            :actions-width="80"
            :default-search-open="false"
            :total="total"
            :page.sync="currentPage"
            :limit.sync="pageSize"
            :enable-lazy-load="true"
            :load-children="loadDemandChildren"
            :table-attrs="{ height: 'calc(100vh - 190px)' }"
            @pagination="query"
            @search="handleSearch"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @sort-change="handleSortChange"
            @selection-change="handleSelectionChange"
        >
            <template #rightNav>
                <el-button type="text" @click="createDemand" class="create-demand-button"
                    ><i class="el-icon-plus"></i> 提需求
                </el-button>
            </template>
            <template #demandName="{ row }">
                <div class="demand-name-wrapper">
                    <el-tooltip :content="row.demandName" placement="top" :disabled="!row.demandName">
                        <div class="demand-name-container">
                            <svg-icon :icon-class="computedDemandNameIcon(row)" class="svg-icon"></svg-icon>
                            <span class="demand-name-text">{{ row.demandName }}</span>
                            <span class="demand-name-suffix" v-if="row.demandClass !== '产品需求'">
                                <svg-icon
                                    icon-class="dms-demand-tree-numbers"
                                    class="svg-icon"
                                    style="width: 10px"
                                ></svg-icon>
                                {{ row.finishCount || 0 }}/{{ row.allCount || 0 }}
                            </span>
                        </div>
                    </el-tooltip>
                </div>
            </template>

            <!-- 操作列 -->
            <template #actions="{ row }">
                <div class="actions-container">
                    <el-button type="text" @click="getDetail(row)">详情</el-button>
                </div>
            </template>
        </demand-list>
        <OriginalDemand :visible.sync="originalDemandDialogVisible" :demandId="currentDemandId"></OriginalDemand>
        <SubOriginalDemand
            :visible.sync="subOriginalDemandDialogVisible"
            :demandId="currentDemandId"
        ></SubOriginalDemand>
        <ProductDemand :visible.sync="productDemandDialogVisible" :demandId="currentDemandId"></ProductDemand>
        <UserDemand :visible.sync="userDemandDialogVisible" :demandId="currentDemandId"></UserDemand>
    </div>
</template>

<script>
import DemandList from 'dms/views/demandManagement/components/demandList';
import OriginalDemand from 'dms/views/demandManagement/demandDetail/OriginalDemand.vue';
import SubOriginalDemand from 'dms/views/demandManagement/demandDetail/SubOriginalDemand.vue';
import ProductDemand from 'dms/views/demandManagement/demandDetail/ProductDemand.vue';
import UserDemand from 'dms/views/demandManagement/demandDetail/UserDemand.vue';
import { queryParams, queryConfig, navItems } from './formInit.js';
import { syncNavFromSearch } from 'dms/mixins/common.js';

export default {
    name: 'AduitDemand',
    components: {
        DemandList,
        OriginalDemand,
        SubOriginalDemand,
        ProductDemand,
        UserDemand
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '进行中',
            // 顶部查询栏配置
            navItems,
            // 查询参数
            params: { demandStatus: '进行中' },
            // 选中的行数据
            selectedRows: [],
            // 查询配置
            queryConfig,
            // 查询参数
            queryParams,
            // 表格数据
            treeData: [],
            sortOrder: 'descending',
            sortKey: 'proposalStartTime',
            currentPage: 1,
            pageSize: 20,
            total: 100,
            // 原始需求弹窗
            originalDemandDialogVisible: false,
            // 子原始需求弹窗
            subOriginalDemandDialogVisible: false,
            // 产品需求弹窗
            productDemandDialogVisible: false,
            // 用户需求弹窗
            userDemandDialogVisible: false,
            // 当前选中的需求
            currentDemandName: '',
            // 当前选中的提出人
            currentProposer: '',
            // 当前选中的需求ID
            currentDemandId: '',
            demandListKey: 0
        };
    },
    watch: {
        'queryParams.productLine': {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.getProductOptions(newVal);
                }
            }
        }
    },
    created() {
        this.getProjectOrGroup();
        this.query();
    },
    methods: {
        /**
         * 获取项目或团队
         */
        async getProjectOrGroup() {
            const api = this.$service.dms.common.getProjectOrGroupList;
            const params = {};
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.queryConfig.items.find((i) => i.modelKey === 'assProjectId').elOptions = res.data.map((i) => ({
                    label: i.projectName,
                    value: i.projectId
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取产品下拉选项
         * @param {String} productLine  产品线
         */
        async getProductOptions(productLine) {
            this.queryParams.assProductId = '';
            try {
                const params = {
                    statusList: ['进行中'],
                    productLine
                };
                const api = this.$service.dms.common.getProductList;
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.queryConfig.items.find((i) => i.modelKey === 'assProductId').elOptions = res.data.map((i) => ({
                    label: i.productName,
                    value: i.id
                }));
            } catch (error) {
                console.error('获取产品选项失败:', error);
            }
        },
        /**
         * 查询
         */
        async query() {
            try {
                const params = {
                    ...this.params,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder,
                    // 此项必填
                    storyClass: '原始需求',
                    currentPage: this.currentPage,
                    pageSize: this.pageSize
                };
                const api = this.$service.dms.demand.getDemandList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }

                // 处理数据，为懒加载准备
                this.treeData = this.processTreeData(res.data.list || []);
                this.total = res.data.total || 0;
                // 无法修改内部子节点懒加载状态，被迫使用key更新整个列表
                this.demandListKey += 1;
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },

        /**
         * 处理树状数据，为懒加载做准备
         * @param {Array} data - 原始数据
         * @returns {Array} 处理后的数据
         */
        processTreeData(data) {
            return data.map((item) => {
                const processedItem = { ...item };

                // 如果有子节点，设置hasChildren为true，并清空children（懒加载时再获取）
                if (item.allCount && item.allCount > 0) {
                    processedItem.hasChildren = true;
                } else {
                    processedItem.hasChildren = false;
                }

                return processedItem;
            });
        },

        /**
         * 懒加载子节点数据
         * @param {Object} tree - 当前节点数据
         * @param {Object} treeNode - 树节点对象
         * @returns {Promise<Array>} 子节点数据
         */
        async loadDemandChildren(tree, treeNode) {
            try {
                const params = {
                    demandId: tree.demandId,
                    storyClass: this.getNextDemandClass(tree.demandClass),
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder
                };

                // 调用API获取子节点数据
                const api = this.$service.dms.demand.getDemandChildren;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return [];
                }

                // 递归处理子节点数据
                return this.processTreeData(res.data || []);
            } catch (error) {
                console.error('加载子节点数据失败:', error);
            }
        },
        /**
         * 获取下一个级别的需求
         * @param {String} currentDemand 当前需求
         * @returns {String} 需求
         */
        getNextDemandClass(currentDemand) {
            if (currentDemand === '原始需求') {
                return '子原始需求';
            } else if (currentDemand === '子原始需求') {
                return '用户需求';
            }
            return '产品需求';
        },
        /**
         * 处理搜索
         * @param {Object} searchData - 搜索表单数据
         */
        handleSearch(searchData) {
            const { searchParams } = searchData;
            const { proposalTime, expectedDate, costDeviation, estimateHour, actualHour, responsiblePerson } =
                searchParams;
            this.params = {
                ...searchParams,
                // 需求提出/创建开始日期
                proposalStartTime: proposalTime[0] || '',
                // 需求提出/创建结束日期
                proposalEndTime: proposalTime[1] || '',
                // 期望交付开始日期
                expectedStartDate: expectedDate[0] || '',
                // 期望交付结束日期
                expectedEndDate: expectedDate[1] || '',
                // 成本起始偏差
                costStartDeviation: costDeviation[0] || '',
                // 成本结束偏差
                costEndDeviation: costDeviation[1] || '',
                // 预估开始工时
                estimateStartHour: estimateHour[0] || '',
                // 预估结束工时
                estimateEndHour: estimateHour[1] || '',
                // 实际开始工时
                actualStartHour: actualHour[0] || '',
                // 实际结束工时
                actualEndHour: actualHour[1] || '',
                // 提出人前后不加逗号，需求负责人要加
                responsiblePerson: responsiblePerson ? `,${responsiblePerson},` : ''
            };
            this.currentPage = 1;
            // 搜索时同步导航状态 - 根据搜索条件自动设置对应的导航标签
            syncNavFromSearch(this);
            this.query();
        },
        /**
         * 处理重置
         */
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
            // 重置时重置到第一页
            this.currentPage = 1;
            this.query();
        },

        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.queryParams[queryField] = field;
            this.params = this.$tools.cloneDeep(this.queryParams);
            this.query();
        },

        /**
         * 处理排序变化
         * @param {Object} sortInfo - 排序信息 { column, prop, order }
         */
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            this.sortKey = prop;
            this.sortOrder = order;
            this.query();
        },

        /**
         * 详情弹窗
         * @param {Object} row - 行数据
         */
        getDetail(row) {
            this.currentDemandId = row.demandId;
            if (row.demandClass === '原始需求') {
                this.originalDemandDialogVisible = true;
                return;
            } else if (row.demandClass === '子原始需求') {
                this.subOriginalDemandDialogVisible = true;
                return;
            } else if (row.demandClass === '用户需求') {
                this.userDemandDialogVisible = true;
                return;
            }
            this.productDemandDialogVisible = true;
        },
        /**
         * 选择项变化
         * @param {Array} selections 选中的行数据
         */
        handleSelectionChange(selections) {
            this.selectedRows = selections;
        },
        /**
         * 计算需求名称图标
         * @param {Object} row 行数据
         * @return {String} 图标名称
         */
        computedDemandNameIcon(row) {
            if (row.demandClass === '原始需求') {
                return 'dms-demand-original-demand';
            } else if (row.demandClass === '子原始需求') {
                return 'dms-demand-sub-original-demand';
            } else if (row.demandClass === '用户需求') {
                return 'dms-demand-user-demand';
            }
            return 'dms-demand-product-demand';
        },
        /**
         * 提需求
         */
        createDemand() {
            this.$router.push({ name: 'AddRequire' });
        }
    }
};
</script>

<style lang="scss" scoped>
.example-container {
    padding: 20px;
}
.svg-icon {
    padding: 0;
}

.demand-name-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 32px; // 确保最小高度，避免换行
}

.demand-name-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    overflow: hidden;
    flex: 1;

    .demand-name-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 40px;
    }

    .demand-name-suffix {
        font-size: 10px;
        position: absolute;
        right: 0;
        color: #999;
        flex-shrink: 0;
        white-space: nowrap; // 防止数字换行
    }
}
.create-demand-button {
    margin-right: 20px;
}
.actions-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

::v-deep .el-input-number.change-time-input {
    width: 100%;
}

// 确保树状表格的展开箭头和内容在同一行
::v-deep .el-table__row {
    .el-table__expand-column {
        .el-table__expand-icon {
            display: inline-flex;
            align-items: center;
            vertical-align: middle;
        }
    }
}

// 只对包含需求名称的列应用 flex 布局，避免影响其他列的 showOverflowTooltip
::v-deep td .cell:has(.demand-name-wrapper) {
    display: flex;
    align-items: center;
    line-height: 1.5;
    min-height: 32px;
}
</style>
