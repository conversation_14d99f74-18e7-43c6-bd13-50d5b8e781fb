import moment from 'moment';

/**
 * 起止日期取值
 *
 * @param {String} type 日期模式
 * @returns {Array} 起止日期 (格式: YYYY-MM-DD)
 */
export function getDateRange(type) {
    if (type === '前一天') {
        const day = moment().subtract(1, 'days');
        const start = day.format('YYYY-MM-DD');
        const end = day.format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '本周') {
        const start = moment().startOf('week').format('YYYY-MM-DD');
        const end = moment().endOf('week').format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '上周') {
        const day = moment().subtract(7, 'days');
        const start = day.startOf('week').format('YYYY-MM-DD');
        const end = day.endOf('week').format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '本月') {
        const start = moment().startOf('month').format('YYYY-MM-DD');
        const end = moment().endOf('month').format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '本季度') {
        const start = moment().startOf('quarter').format('YYYY-MM-DD');
        const end = moment().endOf('quarter').format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '前一月') {
        const month = moment().subtract(1, 'months');
        const start = month.startOf('month').format('YYYY-MM-DD');
        const end = month.endOf('month').format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '前一季度') {
        const quarter = moment().subtract(1, 'quarter');
        const start = quarter.startOf('quarter').format('YYYY-MM-DD');
        const end = quarter.endOf('quarter').format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '上半年') {
        const year = moment().year();
        const start = moment().year(year).month(0).date(1).format('YYYY-MM-DD');
        const end = moment().year(year).month(5).endOf('month').format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '下半年') {
        const year = moment().year();
        const start = moment().year(year).month(6).date(1).format('YYYY-MM-DD');
        const end = moment().year(year).month(11).endOf('month').format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '本年度') {
        const start = moment().startOf('year').format('YYYY-MM-DD');
        const end = moment().endOf('year').format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '上一年度') {
        const start = moment().subtract(1, 'year').startOf('year').format('YYYY-MM-DD');
        const end = moment().subtract(1, 'year').endOf('year').format('YYYY-MM-DD');
        return [start, end];
    }
    if (type === '最近一年') {
        const end = moment().format('YYYY-MM-DD');
        const start = moment().subtract(1, 'year').format('YYYY-MM-DD');
        return [start, end];
    }
}

export const lastMonthOption = {
    text: '前一月',
    onClick(picker) {
        picker.$emit('pick', getDateRange('前一月'));
    }
};
export const lastQuarterOption = {
    text: '前一季度',
    onClick(picker) {
        picker.$emit('pick', getDateRange('前一季度'));
    }
};
export const firstHalfYearOption = {
    text: '上半年',
    onClick(picker) {
        picker.$emit('pick', getDateRange('上半年'));
    }
};
export const secondHalfYearOption = {
    text: '下半年',
    onClick(picker) {
        picker.$emit('pick', getDateRange('下半年'));
    }
};
export const currentYearOption = {
    text: '本年度',
    onClick(picker) {
        picker.$emit('pick', getDateRange('本年度'));
    }
};
export const lastYearOption = {
    text: '上一年度',
    onClick(picker) {
        picker.$emit('pick', getDateRange('上一年度'));
    }
};
export const yesterdayOption = {
    text: '前一天',
    onClick(picker) {
        picker.$emit('pick', getDateRange('前一天'));
    }
};
export const currentWeekOption = {
    text: '本周',
    onClick(picker) {
        picker.$emit('pick', getDateRange('本周'));
    }
};
export const lastWeekOption = {
    text: '上周',
    onClick(picker) {
        picker.$emit('pick', getDateRange('上周'));
    }
};
export const currentMonthOption = {
    text: '本月',
    onClick(picker) {
        picker.$emit('pick', getDateRange('本月'));
    }
};
export const currentQuarterOption = {
    text: '本季度',
    onClick(picker) {
        picker.$emit('pick', getDateRange('本季度'));
    }
};
export const lastOneYearOption = {
    text: '最近一年',
    onClick(picker) {
        picker.$emit('pick', getDateRange('最近一年'));
    }
};

export default {
    getDateRange,
    lastMonthOption,
    lastQuarterOption,
    firstHalfYearOption,
    secondHalfYearOption,
    currentYearOption,
    lastYearOption,
    yesterdayOption,
    currentWeekOption,
    lastWeekOption,
    currentMonthOption,
    currentQuarterOption,
    lastOneYearOption
};
