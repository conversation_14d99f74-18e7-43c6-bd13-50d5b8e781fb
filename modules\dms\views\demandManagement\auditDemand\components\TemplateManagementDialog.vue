<template>
    <div>
        <el-dialog title="模板管理" :visible.sync="dialogVisible" width="900px" top="5vh">
            <div class="content">
                <div class="add-template-btn">
                    <el-button type="text" icon="el-icon-plus" @click="addTemplate"> 添加模板 </el-button>
                </div>
                <el-table
                    :data="flatTableData"
                    style="width: 100%"
                    :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
                    :span-method="spanMethod"
                    class="dms-table"
                >
                    <el-table-column prop="id" label="序号" width="80" align="center">
                        <template slot-scope="scope">
                            {{ getTemplateIndex(scope.row.templateId) }}
                        </template>
                    </el-table-column>

                    <el-table-column prop="templateName" label="模板名称" align="center">
                        <template slot-scope="scope">
                            <el-input
                                v-if="scope.row.isFirstRow"
                                type="textarea"
                                :value="getTemplateNameValue(scope.row.templateId)"
                                @input="updateTemplateNameValue(scope.row.templateId, $event)"
                                placeholder="请输入"
                                size="small"
                            />
                        </template>
                    </el-table-column>

                    <el-table-column label="关联产品" width="200" align="center">
                        <template slot-scope="scope">
                            <el-select
                                @change="handleProductChange($event, scope.row)"
                                :value="getRelationValue(scope.row.templateId, scope.row.relationIndex, 'productId')"
                                @input="
                                    updateRelationValue(
                                        scope.row.templateId,
                                        scope.row.relationIndex,
                                        'productId',
                                        $event
                                    )
                                "
                                placeholder="请选择"
                                size="small"
                                style="width: 100%"
                                filterable
                            >
                                <el-option
                                    v-for="option in productOptions"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value"
                                />
                            </el-select>
                        </template>
                    </el-table-column>

                    <el-table-column align="center" prop="projectId" label="关联团队/项目" width="200">
                        <template slot-scope="scope">
                            <el-select
                                @change="handleProjectChange($event, scope.row)"
                                :value="getRelationValue(scope.row.templateId, scope.row.relationIndex, 'projectId')"
                                @input="
                                    updateRelationValue(
                                        scope.row.templateId,
                                        scope.row.relationIndex,
                                        'projectId',
                                        $event
                                    )
                                "
                                filterable
                                placeholder="请选择"
                                size="small"
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="option in groupOrProjectOptions"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value"
                                />
                            </el-select>
                        </template>
                    </el-table-column>

                    <el-table-column label="操作" width="110" align="center">
                        <template slot-scope="scope">
                            <div class="operation-buttons">
                                <!-- 新增 -->
                                <el-button icon="el-icon-plus" type="text" @click="addRelation(scope.row.templateId)">
                                </el-button>
                                <!-- 删除 -->
                                <el-button
                                    type="text"
                                    icon="el-icon-delete"
                                    @click="removeRelation(scope.row.templateId, scope.row.relationIndex)"
                                >
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <div slot="footer">
                <div class="footer">
                    <el-button @click="handleCancel">取 消</el-button>
                    <el-button type="primary" @click="handleConfirm">确 定</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'ModelManagementDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        templateData: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            templates: [],
            nextTemplateId: 0,
            productOptions: [],
            groupOrProjectOptions: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },

        // 将嵌套的模板数据展平为表格行数据
        flatTableData() {
            const flatData = [];
            this.templates.forEach((template) => {
                template.relations.forEach((relation, relationIndex) => {
                    flatData.push({
                        templateId: template.id,
                        templateName: template.templateName,
                        productId: relation.productId,
                        projectId: relation.projectId,
                        relationIndex,
                        isFirstRow: relationIndex === 0
                    });
                });
            });
            return flatData;
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getProductOrGroupOptions();
                this.getProductOptions();
                this.getProductOrGroupOptions();
                this.getProductOptions();
                this.initData();
            }
        }
    },
    methods: {
        // 处理产品选择变化
        handleProductChange(productId, row) {
            // 更新产品ID
            this.updateRelationValue(row.templateId, row.relationIndex, 'productId', productId);

            // 更新产品名称
            if (productId) {
                const selectedProduct = this.productOptions.find((item) => item.value === productId);
                const productName = selectedProduct ? selectedProduct.label : '';
                const productLine = selectedProduct ? selectedProduct.productLine : '';
                this.updateRelationValue(row.templateId, row.relationIndex, 'productName', productName);
                this.updateRelationValue(row.templateId, row.relationIndex, 'productLine', productLine);
            } else {
                // 清空时的处理
                this.updateRelationValue(row.templateId, row.relationIndex, 'productName', '');
                this.updateRelationValue(row.templateId, row.relationIndex, 'productLine', '');
            }
        },
        // 处理团队或项目选择变化
        handleProjectChange(projectId, row) {
            // 更新项目ID
            this.updateRelationValue(row.templateId, row.relationIndex, 'projectId', projectId);

            // 更新项目名称
            if (projectId) {
                const selectedProject = this.groupOrProjectOptions.find((item) => item.value === projectId);
                const projectName = selectedProject ? selectedProject.label : '';
                this.updateRelationValue(row.templateId, row.relationIndex, 'projectName', projectName);
            } else {
                // 清空时的处理
                this.updateRelationValue(row.templateId, row.relationIndex, 'projectName', '');
            }
        },
        /**
         * 获取关联团队/项目下拉选项
         */
        async getProductOrGroupOptions() {
            const api = this.$service.dms.common.getProjectOrGroupList;
            const params = {};
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.groupOrProjectOptions = res.data.map((i) => ({
                    label: i.projectName,
                    value: i.projectId
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取产品下拉选项
         */
        async getProductOptions() {
            try {
                const params = {
                    statusList: ['进行中']
                };
                const api = this.$service.dms.common.getProductList;
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.productOptions = res.data.map((i) => ({
                    label: i.productName,
                    value: i.id
                }));
            } catch (error) {
                console.error('获取产品选项失败:', error);
            }
        },
        /**
         * 初始化数据
         */
        initData() {
            this.templates = this.templateData;
            this.nextTemplateId = this.templates.length + 1;
            this.templates = this.templateData;
            this.nextTemplateId = this.templates.length + 1;
        },

        /**
         * 表格合并单元格方法
         * @param {Object} { row, columnIndex }
         * @returns {Object} { rowspan, colspan }
         */
        spanMethod({ row, columnIndex }) {
            if (columnIndex === 0 || columnIndex === 1) {
                // 序号和模板名称列
                const { templateId } = row;
                const templateRelations = this.getTemplateRelations(templateId);
                const relationCount = templateRelations.length;

                if (row.isFirstRow) {
                    return {
                        rowspan: relationCount,
                        colspan: 1
                    };
                }
                return {
                    rowspan: 0,
                    colspan: 0
                };
            }
        },

        /**
         * 获取模板序号
         * @param {number} templateId 模板id
         * @returns {number} 模板序号
         */
        getTemplateIndex(templateId) {
            return this.templates.findIndex((t) => t.id === templateId) + 1;
        },

        /**
         * 获取模板的关联关系
         * @param {number} templateId 模板id
         * @returns {Array} 关联关系
         */
        getTemplateRelations(templateId) {
            const template = this.templates.find((t) => t.id === templateId);
            return template ? template.relations : [];
        },

        /**
         * 获取模板的关联关系数量
         * @param {number} templateId 模板id
         * @returns {number} 关联关系数量
         */
        getTemplateRelationCount(templateId) {
            return this.getTemplateRelations(templateId).length;
        },

        /**
         * 获取关联关系的值
         * @param {number} templateId 模板id
         * @param {number} relationIndex 关联关系索引
         * @param {string} field 字段名
         * @returns {string} 字段值
         */
        getRelationValue(templateId, relationIndex, field) {
            const template = this.templates.find((t) => t.id === templateId);
            if (template && template.relations[relationIndex]) {
                return template.relations[relationIndex][field];
            }
            return '';
        },

        /**
         * 更新关联关系的值
         * @param {number} templateId 模板id
         * @param {number} relationIndex 关联关系索引
         * @param {string} field 字段名
         * @param {string} value 新值
         */
        updateRelationValue(templateId, relationIndex, field, value) {
            const template = this.templates.find((t) => t.id === templateId);
            if (template && template.relations[relationIndex]) {
                this.$set(template.relations[relationIndex], field, value);
            }
        },

        /**
         * 获取模板名称的值
         * @param {number} templateId 模板id
         * @returns {string} 模板名称
         */
        getTemplateNameValue(templateId) {
            const template = this.templates.find((t) => t.id === templateId);
            return template ? template.templateName : '';
        },

        /**
         * 更新模板名称的值
         * @param {number} templateId 模板id
         * @param {string} value 新值
         */
        updateTemplateNameValue(templateId, value) {
            const template = this.templates.find((t) => t.id === templateId);
            if (template) {
                this.$set(template, 'templateName', value);
            }
        },

        /**
         * 添加模板
         */
        addTemplate() {
            this.nextTemplateId += 1;
            this.templates.push({
                id: this.nextTemplateId,
                templateName: '',
                relations: [
                    {
                        productId: '',
                        projectId: '',
                        productName: '',
                        projectName: ''
                    }
                ]
            });
        },

        /**
         * 添加关联关系
         * @param {number} templateId 模板id
         */
        addRelation(templateId) {
            const template = this.templates.find((t) => t.id === templateId);
            if (template) {
                template.relations.push({
                    productId: '',
                    projectId: '',
                    productName: '',
                    projectName: ''
                });
            }
        },

        /**
         * 删除关联关系
         * @param {number} templateId 模板id
         * @param {number} relationIndex 关联关系索引
         */
        removeRelation(templateId, relationIndex) {
            const template = this.templates.find((t) => t.id === templateId);
            if (!template) return;

            if (template.relations.length > 1) {
                // 如果模板有多个关联关系，只删除当前关联关系
                template.relations.splice(relationIndex, 1);
            } else if (template.relations.length === 1) {
                // 如果模板只有一个关联关系且不是最后一个模板，删除整个模板
                const templateIndex = this.templates.findIndex((t) => t.id === templateId);
                this.templates.splice(templateIndex, 1);
            }
        },

        /**
         * 取消
         */
        handleCancel() {
            this.dialogVisible = false;
        },

        /**
         * 确定
         */
        async handleConfirm() {
            // 验证数据
            const isValid = this.validateData();
            if (!isValid) {
                this.$message.warning('请输入模板名称');
                return;
            }
            const params = this.templates.map((i) => ({
                module: 'custom',
                section: 'requirementProduct',
                key: String(i.id),
                value: JSON.stringify(i)
            }));
            const api = this.$service.dms.common.batchUpdateConfigInfo;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$emit('success');
                this.$message.success('保存成功');
                this.dialogVisible = false;
            } catch (error) {
                console.error('Error:', error);
            }
        },

        /**
         * 验证数据
         * @returns {boolean} 是否验证通过
         */
        validateData() {
            return this.flatTableData.every((i) => !!i.templateName);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
        color: #409eff;
        font-size: 16px;
    }
}

.content {
    .operation-buttons {
        display: flex;
        justify-content: center;
        gap: 5px;

        .el-button {
            padding: 5px;
        }
    }

    .add-template-btn {
        width: 100%;
        text-align: right;
    }
}

.footer {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 10px;
}

// 表格样式优化
::v-deep .el-table {
    .el-table__header-wrapper {
        .el-table__header {
            th {
                font-weight: 600;
            }
        }
    }

    .el-table__body-wrapper {
        .el-table__body {
            td {
                padding: 8px 0;

                .cell {
                    padding: 0 10px;
                }
            }
        }
    }
}

// 输入框样式
::v-deep .el-input--small {
    .el-input__inner {
        height: 32px;
        line-height: 32px;
    }
}

// 选择器样式
::v-deep .el-select {
    .el-input__inner {
        height: 32px;
        line-height: 32px;
    }
}
</style>
