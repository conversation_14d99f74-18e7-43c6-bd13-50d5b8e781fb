<template>
    <div>
        <el-dialog title="异常说明" :visible.sync="dialogVisible" width="800px" top="5vh" @close="closeDialog">
            <el-form ref="exceptionForm" :model="formData" :rules="rules" label-width="120px">
                <div v-for="(person, index) in under80Persons" :key="index" class="person-form-item">
                    <div style="display: flex">
                        <div style="width: 120px; height: 28px; line-height: 28px; font-weight: 700">
                            {{ person.name }}
                        </div>
                        <el-form-item
                            :label="'异常说明'"
                            :prop="`exceptions.${index}.loadDesc`"
                            :rules="rules.loadDesc"
                            style="width: 100%"
                        >
                            <el-input
                                v-model="formData.exceptions[index].loadDesc"
                                type="textarea"
                                placeholder="请输入异常说明"
                            />
                        </el-form-item>
                    </div>
                </div>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'ExceptionDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        under80Persons: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            formData: {
                exceptions: []
            },
            rules: {
                loadDesc: [{ required: true, message: '请输入异常说明', trigger: 'blur' }]
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        visible(val) {
            if (val) {
                this.initFormData();
            }
        },
        under80Persons: {
            handler() {
                this.initFormData();
            },
            deep: true
        }
    },

    methods: {
        /**
         * 初始化表单数据
         */
        initFormData() {
            this.formData.exceptions = this.under80Persons.map((person) => ({
                planId: person.id,
                account: person.account,
                // 使用传入的异常说明，如果没有则为空字符串
                loadDesc: person.loadDesc || '' 
            }));
        },

        /**
         * 确认提交
         */
        handleConfirm() {
            this.$refs.exceptionForm.validate((valid) => {
                if (valid) {
                    this.save();
                } else {
                    this.$message.warning('请完善异常说明信息');
                }
            });
        },

        async save() {
            try {
                const params = this.formData.exceptions;

                const api = this.$service.dms.plan.addWorkLoadExtraInfo;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$message.success('保存成功');
                // 发出保存成功事件，传递更新后的异常说明数据
                this.$emit('save-success', this.formData.exceptions);
                this.closeDialog();
            } catch (error) {
                console.error('保存异常说明失败:', error);
                this.$message.error('保存失败，请重试');
            }
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.person-form-item {
    margin-bottom: 20px;
}

.dialog-footer {
    text-align: right;

    .el-button {
        margin-left: 10px;
    }
}

::v-deep .el-dialog__body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

::v-deep .el-form-item__label {
    font-weight: 500;
}

::v-deep .el-textarea__inner {
    resize: vertical;
}
</style>
