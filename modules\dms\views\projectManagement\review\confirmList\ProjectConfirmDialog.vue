<template>
    <div>
        <el-dialog title="项目确认" :visible.sync="dialogVisible" width="550px" :before-close="closeDialog">
            <div class="confirm-container">
                <div class="description">请确认将该项目作为一个新项目导入，或与一个临时项目合并：</div>
                <el-select v-model="importType" placeholder="">
                    <el-option v-for="item in importOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select
                    class="project-select"
                    v-if="importType === '合并项目'"
                    v-model="tmpProjectId"
                    placeholder=""
                >
                    <el-option v-for="item in projectOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <div class="detail-button">
                    <el-button type="text" @click="handleView"> 查看项目详情 </el-button>
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="handleConfirm">确 认</el-button>
            </div>
        </el-dialog>
        <FormalProjectDialog
            :visible.sync="formalProjectDialogVisible"
            :projectId="projectId"
            type="view"
        ></FormalProjectDialog>
    </div>
</template>

<script>
import FormalProjectDialog from 'dms/views/projectManagement/projectList/components/FormalProjectDialog.vue';

export default {
    name: 'ProjectConfirmDialog',
    components: { FormalProjectDialog },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        projectId: {
            type: [String, Number],
            default: null
        }
    },

    data() {
        return {
            // 导入类型
            importType: '导入项目',
            importOptions: [
                { label: '导入项目', value: '导入项目' },
                { label: '合并项目', value: '合并项目' }
            ],
            projectOptions: [],
            // 项目详情弹窗
            formalProjectDialogVisible: false,
            tmpProjectId: null
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(val) {
            if (val) {
                this.getProjectList();
            }
        }
    },
    methods: {
        async handleConfirm() {
            if (this.importType === '合并项目' && !this.projectId) {
                this.$message.warning('请选择项目');
            }
            const api = this.$service.dms.project.confirmProject;
            const params = {
                actionType: this.importType,
                projectId: this.projectId,
                tmpProjectId: this.tmpProjectId || null
            };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$message.success('保存成功');
                this.$emit('success');
                this.closeDialog();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        handleView() {
            this.formalProjectDialogVisible = true;
        },
        async getProjectList() {
            const api = this.$service.dms.project.getTemporaryProjectList;
            const params = {};
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.projectOptions = res.data.map((item) => {
                    return {
                        label: item.projectName,
                        value: item.projectId
                    };
                });
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.importType = '导入项目';
            this.tmpProjectId = null;

            this.dialogVisible = false;
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.project-detail {
    .title {
        @include section-title;
        margin-bottom: 16px;
        margin-top: 32px;

        &:first-child {
            margin-top: 0;
        }
    }
}
.confirm-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    .description {
        margin-bottom: 22px;
    }
    .project-select {
        margin-top: 12px;
        width: 400px;
    }
    // TODO:加长
    .detail-button {
        margin-top: 12px;
    }
}
</style>
