<template>
    <div>
        <el-dialog title="关联需求" :visible.sync="dialogVisible" width="1000px" top="5vh" @close="closeDialog">
            <!-- 搜索条件 -->
            <div class="search-form">
                <el-form :model="searchForm">
                    <el-form-item label="产品名称">
                        <el-select
                            v-model="searchForm.assProductId"
                            placeholder="请选择产品"
                            clearable
                            style="width: 100%"
                        >
                            <el-option
                                v-for="item in productList"
                                :key="item.id"
                                :label="item.productName"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="需求类型">
                        <el-select
                            v-model="searchForm.storyClass"
                            placeholder="请选择需求类型"
                            clearable
                            style="width: 200px"
                        >
                            <el-option
                                v-for="item in dictData.demandClassification"
                                :label="item.label"
                                :key="item.value"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="需求名称">
                        <div style="display: flex">
                            <el-input
                                v-model="searchForm.demandName"
                                style="width: 100%; flex: 1"
                                placeholder="请输入需求名称"
                                clearable
                            />
                            <el-button style="margin-left: 10px" type="primary" @click="handleSearch">查询</el-button>
                            <el-button @click="handleReset">重置</el-button>
                        </div>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 需求列表表格 -->
            <div class="demand-table">
                <el-table
                    ref="demandTable"
                    :data="demandList"
                    class="dms-table"
                    height="400"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column header-align="center" prop="id" label="ID" width="170" />
                    <el-table-column
                        header-align="center"
                        prop="storyName"
                        label="需求名称"
                        min-width="200"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        header-align="center"
                        prop="product"
                        label="所属产品"
                        min-width="150"
                        show-overflow-tooltip
                    />
                    <el-table-column header-align="center" prop="module" label="所属模块" width="120" />
                </el-table>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import dictData from 'dms/constant/dict.js';

export default {
    name: 'RelateDemandDialog',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            dictData,
            // 搜索表单
            searchForm: {
                assProductId: '',
                storyClass: '',
                demandName: ''
            },
            // 产品列表
            productList: [],
            // 需求列表
            demandList: [],
            // 选中的需求
            selectedDemands: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        }
    },
    watch: {
        dialogVisible(val) {
            if (val) {
                this.initDialog();
            }
        }
    },
    methods: {
        /**
         * 初始化对话框
         */
        async initDialog() {
            this.getProductList();
            this.handleSearch();
        },

        /**
         * 获取产品列表
         */
        async getProductList() {
            const api = this.$service.dms.plan.getRelatedProduct;
            const params = {
                projectId: this.groupValue,
                status: ''
            };
            try {
                const res = await api(params);
                if (res.code === '0000') {
                    this.productList = res.data || [];
                } else {
                    this.$message.error(res.message || '获取产品列表失败');
                }
            } catch (error) {
                console.error('获取产品列表失败:', error);
            }
        },

        /**
         * 搜索需求
         */
        async handleSearch() {
            try {
                const params = {
                    productId: this.searchForm.assProductId ? [this.searchForm.assProductId] : [],
                    storyType: this.searchForm.storyClass ? [this.searchForm.storyClass] : [],
                    storyName: this.searchForm.demandName,
                    projectId: this.groupValue
                };
                const api = this.$service.dms.plan.getRelatedDemandList;
                const res = await api(params);
                if (res.code === '0000') {
                    this.demandList = res.data || [];
                } else {
                    this.$message.error(res.message || '获取需求列表失败');
                }
            } catch (error) {
                console.error('获取需求列表失败:', error);
            }
        },

        /**
         * 重置搜索条件
         */
        handleReset() {
            this.searchForm = {
                assProductId: '',
                storyClass: '',
                demandName: ''
            };
            this.handleSearch();
        },

        /**
         * 表格选择变更
         * @param {Array} selection 选择项
         */
        handleSelectionChange(selection) {
            this.selectedDemands = selection;
        },

        /**
         * 确认关联
         */
        handleConfirm() {
            if (this.selectedDemands.length === 0) {
                this.$message.warning('请选择要关联的需求');
                return;
            }

            this.$emit('confirm', this.selectedDemands);
            this.closeDialog();
        },

        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.resetData();
        },

        /**
         * 重置数据
         */
        resetData() {
            this.searchForm = {
                assProductId: '',
                storyClass: '',
                demandName: ''
            };
            this.demandList = [];
            this.selectedDemands = [];
        }
    }
};
</script>

<style lang="scss" scoped>
.search-form {
    margin-bottom: 20px;

    .el-form-item {
        margin-bottom: 10px;
    }

    .search-form-row {
        display: flex;
        width: 100%;
        margin-top: 10px;
    }
}

.dialog-footer {
    text-align: right;

    .el-button {
        margin-left: 10px;
    }
}

::v-deep .el-dialog__body {
    padding: 20px;
}
</style>
