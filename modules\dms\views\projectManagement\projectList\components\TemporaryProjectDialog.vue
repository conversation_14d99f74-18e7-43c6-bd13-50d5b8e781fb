<template>
    <div>
        <el-dialog :title="title" :visible.sync="dialogVisible" width="85%" :before-close="closeDialog" top="5vh">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="form">
                <!-- 基本信息 -->
                <div class="title">基本信息</div>
                <el-row :gutter="24">
                    <el-col :span="16">
                        <el-form-item label="项目名称" prop="projectName">
                            <el-input
                                v-model="form.projectName"
                                placeholder="请输入项目名称"
                                clearable
                                :disabled="isDisabled"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="项目经理" prop="pmAccount">
                            <PeopleSelector
                                v-model="form.pmAccount"
                                placeholder="请选择项目经理"
                                :is-multipled="false"
                                ref="projectManagerRef"
                                clearable
                                :disabled="isDisabled"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="24">
                    <el-col :span="8">
                        <el-form-item label="开始时间" prop="startDate">
                            <el-date-picker
                                v-model="form.startDate"
                                type="date"
                                placeholder="请选择开始时间"
                                value-format="yyyy-MM-dd"
                                style="width: 100%"
                                :disabled="isDisabled"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="计划结束时间" prop="planEndDate">
                            <el-date-picker
                                v-model="form.planEndDate"
                                type="date"
                                placeholder="请选择计划结束时间"
                                value-format="yyyy-MM-dd"
                                style="width: 100%"
                                :disabled="isDisabled"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="项目状态" prop="projectStatus">
                            <el-select
                                v-model="form.projectStatus"
                                type="date"
                                placeholder="请选择项目状态"
                                value-format="yyyy-MM-dd"
                                style="width: 100%"
                                :disabled="isDisabled"
                            >
                                <el-option
                                    v-for="item in dictData.projectStatusData"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 关联信息 -->
                <div class="title">关联信息</div>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="归属部门" prop="orgCode">
                            <DepartmentSelector
                                style="width: 100%"
                                v-model="form.orgCode"
                                :disabled="isDisabled"
                            ></DepartmentSelector>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="产品线" prop="productLine">
                            <el-select
                                v-model="form.productLine"
                                placeholder="请选择"
                                clearable
                                @change="handleProductLineChange"
                                style="width: 100%"
                                :disabled="isDisabled"
                            >
                                <el-option
                                    v-for="item in productLineOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="产品经理" prop="productManager">
                            <PeopleSelector
                                :is-multipled="false"
                                v-model="form.productManager"
                                placeholder="请输入产品经理"
                                ref="productManagerRef"
                                clearable
                                :disabled="isDisabled"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item v-if="type !== 'review'" label="审核PO" prop="higherUp">
                            <PeopleSelector
                                :is-multipled="false"
                                v-model="form.higherUp"
                                placeholder="请输入审核PO"
                                clearable
                                :disabled="isDisabled"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="20">
                        <el-form-item label="涉及产品" prop="products">
                            <el-select
                                v-model="form.products"
                                placeholder="请选择"
                                multiple
                                clearable
                                style="width: 100%"
                                :disabled="isDisabled"
                            >
                                <el-option
                                    v-for="item in productOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 项目描述 -->
                <div class="title">项目描述</div>
                <el-form-item label="项目描述" prop="taskNote">
                    <el-input
                        v-model="form.taskNote"
                        type="textarea"
                        :rows="6"
                        placeholder="建议说明：任务来源、任务描述、任务分析、技术要求等"
                        resize="vertical"
                        :disabled="isDisabled"
                    />
                </el-form-item>
            </el-form>
            <template v-if="type === 'view' || type === 'edit'">
                <!-- 操作记录 -->
                <div class="title">操作记录</div>
                <operation-record :collapseItems="collapseItems"></operation-record>
            </template>
            <template v-if="type === 'review'">
                <!-- 审批意见 -->
                <div class="title">审批意见</div>
                <el-input
                    v-model="checkOpinion"
                    type="textarea"
                    :rows="6"
                    placeholder="请填写您的审批意见，如您拒绝该审批，审批意见需必填"
                    resize="vertical"
                />
            </template>
            <div slot="footer" class="dialog-footer">
                <template v-if="type === 'review'">
                    <el-button @click="closeDialog">取 消</el-button>
                    <el-button @click="handleReview('已拒绝')" type="danger">拒 绝</el-button>
                    <el-button @click="handleReview('已通过')" type="primary">批 准</el-button>
                </template>
                <template v-else>
                    <el-button @click="closeDialog">{{ isDisabled ? '关闭' : '取消' }}</el-button>
                    <el-button v-if="!isDisabled" type="primary" @click="handleSubmit">确定</el-button>
                </template>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import PeopleSelector from 'dms/components/PeopleSelector';
import DepartmentSelector from 'dms/components/DepartmentSelector';
import { getSelectedLabel } from 'dms/mixins/common';
import OperationRecord from 'dms/views/productManagement/components/operationRecord.vue';
import dictData from 'dms/constant/dict.js';

export default {
    name: 'TemporaryProjectDialog',
    components: { PeopleSelector, DepartmentSelector, OperationRecord },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        projectId: {
            type: [String, Number],
            default: null
        },
        // view/add/edit/review
        // 详情/新增/编辑/审核
        type: {
            type: String,
            default: 'add'
        },
        // 审核ID
        checkId: {
            type: [Number, String],
            default: null
        }
    },

    data() {
        return {
            form: {
                projectName: '',
                pmAccount: '',
                startDate: '',
                planEndDate: '',
                orgCode: '',
                productLine: '',
                productManager: '',
                products: [],
                taskNote: '',
                higherUp: '',
                projectStatus: '进行中'
            },
            rules: {
                projectName: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
                pmAccount: [{ required: true, message: '请选择项目经理', trigger: 'change' }],
                startDate: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
                orgCode: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
                productLine: [{ required: true, message: '请选择产品线', trigger: 'change' }],
                productManager: [{ required: true, message: '请选择产品经理', trigger: 'change' }],
                products: [{ required: true, message: '请选择涉及产品', trigger: 'change' }],
                higherUp: [{ required: true, message: '请选择审核PO', trigger: 'change' }],
                taskNote: [{ required: true, message: '请输入项目描述', trigger: 'change' }],
                planEndDate: [{ required: true, message: '请输入计划结束时间', trigger: 'change' }],
                projectStatus: [{ required: true, message: '请选择项目状态', trigger: 'change' }]
            },
            // 部门选项
            departmentOptions: [],
            // 产品线选项
            productLineOptions: [],
            // 产品选项
            productOptions: [],
            // 操作记录列表
            collapseItems: [],
            // 审批意见
            checkOpinion: '',
            // 下拉列表选项
            dictData
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        title() {
            if (this.type === 'view') {
                return '临时项目详情';
            } else if (this.type === 'edit') {
                return '编辑临时项目';
            } else if (this.type === 'add') {
                return '新增临时项目';
            } else if (this.type === 'review') {
                return '项目审核';
            }
            return '';
        },
        isDisabled() {
            return !['add', 'edit'].includes(this.type);
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.initData();
            }
        }
    },
    methods: {
        /**
         * 初始化数据
         */
        async initData() {
            await this.getProductLineOptions();

            // 如果有项目ID，加载项目数据
            if (this.projectId) {
                this.getActions();
                await this.loadProjectData();
            }

            await this.getProductOptions();
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.form = {
                projectName: '',
                pmAccount: '',
                startDate: '',
                planEndDate: '',
                orgCode: '',
                productLine: '',
                productManager: '',
                products: [],
                taskNote: '',
                higherUp: '',
                projectStatus: '进行中'
            };
            this.checkOpinion = '';
            this.$nextTick(() => {
                this.$refs.form && this.$refs.form.resetFields();
            });
        },
        /**
         * 获取产品线选项
         */
        async getProductLineOptions() {
            try {
                const res = await this.$service.dms.product.getProductLine();
                if (res.code !== '0000') {
                    this.$tools.message.err(res.message || '系统异常');
                    return;
                }
                this.productLineOptions = res.data.map((item) => {
                    return {
                        value: item,
                        label: item
                    };
                });
            } catch (error) {
                console.error('获取产品线列表失败:', error);
            }
        },
        /**
         * 处理产品线变化
         * @param {String} productLine 选中的产品线
         */
        async handleProductLineChange(productLine) {
            // 清空已选择的产品，因为产品线变了，产品列表也会变
            this.form.responsibleProduct = [];
            // 获取新的产品选项
            await this.getProductOptions(productLine);
        },

        /**
         * 获取产品下拉选项
         * @param {String} productLine  产品线
         */
        async getProductOptions(productLine) {
            // 如果没有传入产品线参数，使用表单中的产品线
            const selectedProductLine = productLine || this.form.productLine;

            if (!selectedProductLine) {
                this.productOptions = [];
                return;
            }

            try {
                const params = {
                    statusList: ['进行中'],
                    productLine: selectedProductLine
                };
                const api = this.$service.dms.common.getProductList;
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.productOptions = res.data.map((i) => ({
                    label: i.productName,
                    value: i.id
                }));
            } catch (error) {
                console.error('获取产品选项失败:', error);
                this.productOptions = [];
            }
        },
        /**
         * 提交表单
         */
        handleSubmit() {
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    try {
                        const submitData = {
                            project: {
                                projectId: this.projectId ? this.projectId : null,
                                projectName: this.form.projectName,
                                pmAccount: this.form.pmAccount,
                                projectManager: getSelectedLabel(this.$refs.projectManagerRef),
                                productManager: this.form.productManager,
                                productManagerName: getSelectedLabel(this.$refs.productManagerRef),
                                startDate: this.form.startDate,
                                planEndDate: this.form.planEndDate,
                                orgCode: this.form.orgCode,
                                productLine: this.form.productLine,
                                higherUp: this.form.higherUp,
                                projectStatus: this.form.projectStatus
                            },
                            extend: {
                                taskNote: this.form.taskNote
                            },
                            products: this.form.products || []
                        };
                        const res = await this.$service.dms.project.createTemporaryProject(submitData);
                        if (res.code === '0000') {
                            this.$message.success('创建临时项目成功');
                            this.closeDialog();
                            this.$emit('success');
                        } else {
                            this.$message.error(res.message || '创建临时项目失败');
                        }
                    } catch (error) {
                        console.error('创建临时项目失败:', error);
                        this.$message.error('创建临时项目失败');
                    }
                }
            });
        },
        /**
         * 加载项目数据
         */
        async loadProjectData() {
            if (!this.projectId) return;

            try {
                const params = { projectId: this.projectId };
                const res = await this.$service.dms.project.getProjectDetail(params);
                if (res.code === '0000' && res.data) {
                    const { project, extend, products } = res.data;

                    this.form = {
                        projectName: project?.projectName || '',
                        pmAccount: project?.pmAccount || '',
                        startDate: project?.startDate || '',
                        planEndDate: project?.planEndDate || '',
                        orgCode: project?.orgCode || '',
                        productLine: project?.productLine || '',
                        productManager: project?.productManager || '',
                        products: products || [],
                        taskNote: extend?.taskNote || '',
                        higherUp: project?.higherUp || '',
                        projectStatus: project?.projectStatus || ''
                    };
                    this.$refs.form.resetFields();
                } else {
                    this.$message.error(res.message || '获取项目详情失败');
                }
            } catch (error) {
                console.error('加载项目数据失败:', error);
                this.$message.error('加载项目数据失败');
            }
        },
        /**
         * 获取操作记录
         */
        async getActions() {
            try {
                const params = {
                    objectId: this.checkId
                };
                const res = await this.$service.dms.product.getActions(params);
                if (res.code === '0000') {
                    this.collapseItems = res.data || [];
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        },
        /**
         * 审核
         * @param {String} actionType 类型
         */
        async handleReview(actionType) {
            if (actionType === '拒绝' && !this.checkOpinion) {
                this.$message.warning('请填写审批意见');
                return;
            }
            try {
                const params = {
                    checkId: this.checkId,
                    checkOpinion: this.checkOpinion,
                    actionType,
                    projectId: this.projectId
                };
                const api = this.$service.dms.project.reviewTemporaryProject;
                const res = await api(params);
                if (res.code === '0000') {
                    this.collapseItems = res.data || [];
                    this.$message.success('保存成功');
                    this.$emit('success');
                    this.closeDialog();
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error(error.message || '系统异常');
            }
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.resetForm();
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    margin-bottom: 16px;
    margin-top: 32px;

    &:first-child {
        margin-top: 0;
    }
}

.flex {
    display: flex;
}

.info-card {
    margin-bottom: 16px;

    .card-header {
        .card-title {
            font-weight: bold;
            font-size: 14px;
            color: #303133;
        }
    }

    .text-content {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        min-height: 60px;
        color: #303133;
    }
}

::v-deep .el-card__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

::v-deep .el-card__body {
    padding: 20px;
}

::v-deep .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

::v-deep .form .el-form-item__label {
    font-weight: bold;
}

.dialog-footer {
    text-align: right;
}

::v-deep .el-select {
    width: 100%;
}

::v-deep .el-textarea__inner {
    resize: vertical;
}

// 表格样式
::v-deep .el-table {
    .el-table__header-wrapper {
        th {
            background-color: #fafbfc;
            color: #606266;
            font-weight: 600;
        }
    }

    .el-table__body-wrapper {
        td {
            color: #303133;
        }
    }

    .el-table__border {
        border-color: #ebeef5;
    }

    th,
    td {
        border-color: #ebeef5;
    }
}
.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 14px;
    }
}
</style>
