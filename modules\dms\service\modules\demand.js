/**
 * 需求管理相关服务接口
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 需求管理接口
        demand: {
            // 获取需求列表（支持分页和筛选）
            getDemandList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getDemandList',
                    method: 'post',
                    data
                });
            },
            // 获取需求子节点（懒加载）
            getDemandChildren(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getSubDemandList',
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 获取需求详情
            getDemandDetail(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getStoryInfo',
                    method: 'get',
                    params: data
                });
            },
            // 获取需求审核列表
            getAuditDemandList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getDemandCheckList',
                    method: 'post',
                    data
                });
            },
            // 拆分需求
            demandDecomposition(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/insertSubOrDemandList',
                    method: 'post',
                    data
                });
            },
            // 获取子原始需求列表
            getSubDemandList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getSubOrDemandList',
                    method: 'get',
                    params: data
                });
            },
            // 获取变更审核详情内容
            getDamandCheckDetail(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getStoryCheckContent',
                    method: 'get',
                    params: data
                });
            },
            // 获取用户需求或产品需求变更审核详情内容
            getUserOrProductDamandCheckDetail(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getStoryCheckInfo',
                    method: 'get',
                    params: data
                });
            },
            // 子需求需要跟着原始需求一同变更
            subDemandCheckChange(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/subOrDemandChangeList',
                    method: 'post',
                    data
                });
            },
            // 变更需求的审核状态
            demandCheckStatusChange(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/updateStoryCheck',
                    method: 'put',
                    data
                });
            },
            // 编辑子需求
            editSubDemand(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/editStory',
                    method: 'post',
                    data
                });
            },
            // 关闭需求
            closeDemand(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/closeDemand',
                    method: 'post',
                    data
                });
            },
            // 获取我提出的需求
            getMyProposalList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/story/getDemandProposeList',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
