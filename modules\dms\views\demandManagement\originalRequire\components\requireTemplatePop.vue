<template>
    <div>
        <!-- 需求拆分模板管理弹窗 -->
        <el-dialog :title="title" :visible.sync="localVisible" width="70%" class="custom-dialog" @close="cancel">
            <!-- 新增模板按钮 -->
            <el-button type="text" icon="el-icon-plus" @click="addTemplateRow"> 新增模板 </el-button>
            <el-table :data="templateRows" border style="width: 100%">
                <el-table-column label="序号" width="80" align="center">
                    <template #default="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>

                <el-table-column label="模板名称" align="center" width="200">
                    <template #default="scope">
                        <el-input v-model="scope.row.templateName" placeholder="请输入模板名称" />
                    </template>
                </el-table-column>
                <el-table-column label="关联产品" align="center">
                    <template #default="scope">
                        <div
                            v-for="(productItem, productIndex) in scope.row.productRelations"
                            :key="productIndex"
                            class="product-box"
                        >
                            <el-select v-model="productItem.product" placeholder="请选择产品" clearable>
                                <el-option
                                    v-for="item in productOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>

                            <el-select v-model="productItem.team" placeholder="请选择团队/项目" clearable>
                                <el-option
                                    v-for="item in teamOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                            <!-- 删除当前产品-团队关联 -->
                            <el-button
                                type="text"
                                icon="el-icon-close"
                                class="delete-btn"
                                @click="deleteProductRelation(scope.$index, productIndex)"
                            />
                        </div>
                        <!-- 新增产品-团队关联按钮 -->
                        <el-button
                            type="text"
                            icon="el-icon-plus"
                            @click="addProductRelation(scope.$index)"
                            class="add-btn"
                        >
                        </el-button>
                    </template>
                </el-table-column>

                <el-table-column label="操作" width="80" align="center" fixed="right">
                    <template #default="scope">
                        <el-button
                            type="danger"
                            icon="el-icon-delete-solid"
                            size="mini"
                            @click="deleteTemplateRow(scope.$index)"
                        />
                    </template>
                </el-table-column>
            </el-table>

            <!-- 弹窗底部按钮 -->
            <template #footer>
                <el-button @click="cancel">取消</el-button>
                <el-button type="primary" @click="saveTemplates">确认</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>

export default {
    name: 'DemandSplitTemplate',
    data() {
        return {
            title: '需求拆分模板管理',
            localVisible: false,
            templateRows: [],

            // 下拉选项（可替换为接口数据）
            productOptions: [
                { label: 'A产品', value: 'A产品' },
                { label: 'B产品', value: 'B产品' }
            ],
            teamOptions: [
                { label: '甲团队', value: '甲团队' },
                { label: '乙团队', value: '乙团队' }
            ]
        };
    },
    methods: {
        // 打开弹窗（外部调用控制显示）
        open() {
            this.localVisible = true;
            // 初始化：至少有一行模板
            if (this.templateRows.length === 0) {
                this.addTemplateRow();
            }
        },

        // 新增模板行（每行默认带一组“产品-团队”关联）
        addTemplateRow() {
            if (this.templateRows.length < 5) {
                // 限制最多10行
                this.templateRows.push({
                    templateName: '',
                    productRelations: [
                        // 每行的产品-团队关联（可动态新增）
                        { product: '', team: '' }
                    ]
                });
            } else {
                this.$message.warning('最多可创建5个模版!');
            }
        },

        // 新增产品-团队关联
        addProductRelation(rowIndex) {
            this.$set(
                this.templateRows[rowIndex].productRelations,
                this.templateRows[rowIndex].productRelations.length,
                { product: '', team: '' }
            );
        },

        // 产出产品-团队关联
        deleteProductRelation(rowIndex, relationIndex) {
            this.templateRows[rowIndex].productRelations.splice(relationIndex, 1);
            // 如果删除后关联组为空，自动新增一组（避免无法选择）
            if (this.templateRows[rowIndex].productRelations.length === 0) {
                this.addProductRelation(rowIndex);
            }
        },

        // 删除整行模板
        deleteTemplateRow(rowIndex) {
            this.templateRows.splice(rowIndex, 1);
            // 如果删除后无数据，自动新增一行
            if (this.templateRows.length === 0) {
                this.addTemplateRow();
            }
        },

        // 确认保存（可对接接口）
        saveTemplates() {
            // 简单校验：模板名称、产品、团队必填
            const hasEmpty = this.templateRows.some((row) => {
                if (!row.templateName) return true;
                return row.productRelations.some((rel) => !rel.product || !rel.team);
            });
            if (hasEmpty) {
                this.$message.error('模板名称、关联产品、关联团队为必填项，请完善后提交');
                return;
            }
            console.log('提交模板数据：', this.templateRows);
            this.$message.success('模板保存成功（模拟）');
            this.localVisible = false;
        },

        // 关闭弹窗
        cancel() {
            this.localVisible = false;
            this.templateRows = [];
        }
    }
};
</script>

<style lang="scss" scoped>
.product-box {
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.add-btn {
    float: right;
}
.add-btn,
.delete-btn {
    font-size: 20px;
    font-weight: bolder;
}
</style>
