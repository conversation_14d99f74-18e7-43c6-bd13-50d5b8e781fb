<template>
    <div class="view-box">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button @click="confirm('已通过')" type="primary" v-if="type === '审核'">批准</el-button>
            <el-button @click="confirm('已拒绝')" type="danger" v-if="type === '审核'">拒绝</el-button>
            <el-button @click="cancel">返回</el-button>
        </div>
        <div>
            <!-- 产品结构 -->
            <formula-title :title="basicTitle"></formula-title>
            <div class="product-structure-container">
                <detail-tree :data="rootNode" :productName="productName"></detail-tree>
            </div>
            <!-- 审批意见 -->
            <formula-title :title="checkTitle" v-if="type === '审核'"></formula-title>
            <el-form :model="addForm" ref="dataForm" label-width="80px" v-if="type === '审核'">
                <el-form-item label="审批意见" prop="checkOpinion">
                    <el-input
                        type="textarea"
                        v-model="addForm.checkOpinion"
                        maxlength="500"
                        :rows="4"
                        placeholder="请填写您的审批意见"
                    ></el-input>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import formulaTitle from '../components/formulaTitle.vue';
import DetailTree from 'dms/views/productManagement/components/DetailTree.vue';

export default {
    components: { formulaTitle, DetailTree },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            basicTitle: '产品结构',
            checkTitle: '审批意见',
            type: '',
            productId: '',
            productName: '',
            checkId: '',
            addForm: {
                checkOpinion: ''
            },
            // 产品子节点数据（从接口获取的子节点）
            productChildren: []
        };
    },
    computed: {
        // 根节点（产品节点），包含从接口获取的子节点
        rootNode() {
            return [
                {
                    productId: this.productId,
                    moduleName: this.productName,
                    type: 'product',
                    moduleType: 'P',
                    sort: 1,
                    children: this.productChildren
                }
            ];
        }
    },
    created() {
        this.type = this.$route.query.type;
        this.productId = this.$route.query.productId;
        this.checkId = this.$route.query.checkId;
        this.productName = this.$route.query.productName;
        this.$nextTick(() => {
            this.getProductTreedata();
        });
    },
    methods: {
        /**
         * 处理树形结构变化
         * @param {Object} param0 树形结构数据
         */
        handleTreeDataChange({ treeData }) {
            // 只保存根节点下的子节点
            this.productChildren = treeData[0]?.children || [];
        },

        /*
         * 获取产品结构数据
         */
        async getProductTreedata() {
            try {
                const params = { productId: this.productId, checkId: this.checkId };
                const res = await this.$service.dms.product.getProductChangeInfo(params);
                if (res.code !== '0000') {
                    this.$tools.message.err(res.message || '系统异常');
                    this.productChildren = [];
                    return;
                }
                // 将接口返回的子节点数据赋值给productChildren
                this.productChildren = res.data || [];
            } catch (error) {
                console.error('获取产品结构失败', error);
                this.$tools.message.err('系统异常');
                this.productChildren = [];
            }
        },
        async confirm(val) {
            try {
                const params = this.addForm;
                params.checkId = this.checkId;
                params.checkResult = val;
                if (val === '已拒绝') {
                    if (this.addForm.checkOpinion === '' || this.addForm.checkOpinion === null) {
                        this.$message.error('请填写您的审批意见');
                        return;
                    }
                }
                // 4. 调用后端接口提交数据
                const res = await this.$service.dms.product.productCheck(params);
                if (res.code === '0000') {
                    this.$message.success(res.message);
                    this.$router.back();
                } else {
                    this.$message.error(res.message || '审核失败，请重试');
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        },
        // 关闭弹窗
        cancel() {
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped>
.sprint-btn {
    height: 50px;
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin-left: auto;
    padding: 10px;
    border-bottom: 1px solid #dcdfe6;
}
</style>
