<!-- 工时负载率echarts图 -->
<template>
    <div class="worktime-chart-container">
        <el-card class="chart-left" shadow="never">
            <div slot="header" class="clearfix">
                <span>汇总</span>
            </div>
            <div class="load-rate-item">
                <p>{{ loadRateData.over120.label }}</p>
                <p>人数: {{ loadRateData.over120.count }}</p>
                <p>具体人员: {{ loadRateData.over120.persons }}</p>
            </div>
            <div class="load-rate-item">
                <div style="display: flex; justify-content: space-between; align-items: center">
                    <p>{{ loadRateData.under80.label }}</p>
                    <el-button type="primary" @click="showExceptionDialog">异常说明</el-button>
                </div>
                <p>人数: {{ loadRateData.under80.count }}</p>
                <p>具体人员: {{ loadRateData.under80.persons }}</p>
            </div>
        </el-card>

        <!-- 异常说明弹窗 -->
        <ExceptionDialog
            :visible.sync="exceptionDialogVisible"
            :under80-persons="under80PersonsList"
            @save-success="handleExceptionSaveSuccess"
        />
        <div class="chartTwo" ref="chartMain"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import ExceptionDialog from './ExceptionDialog.vue';

export default {
    components: {
        ExceptionDialog
    },
    props: {
        view: {
            type: String,
            default: 'day'
        },
        info: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            loadRateData: {
                over120: {
                    label: '负载率>120%',
                    count: 0,
                    persons: ''
                },
                under80: {
                    label: '负载率<80%',
                    count: 0,
                    persons: ''
                }
            },
            exceptionDialogVisible: false,
            under80PersonsList: []
        };
    },
    watch: {
        info: {
            handler(newVal) {
                if (newVal) {
                    this.twoChart();
                }
            },
            deep: true
        }
    },
    methods: {
        /**
         * 显示异常说明弹窗
         */
        showExceptionDialog() {
            if (this.under80PersonsList.length === 0) {
                this.$message.warning('当前没有负载率<80%的人员');
                return;
            }
            this.exceptionDialogVisible = true;
        },

        /**
         * 处理异常说明保存成功事件
         * @param {Array} updatedExceptions 更新后的异常说明数据
         */
        handleExceptionSaveSuccess(updatedExceptions) {
            // 更新本地数据中的异常说明
            updatedExceptions.forEach((exception) => {
                const person = this.under80PersonsList.find((p) => p.id === exception.id);
                if (person) {
                    person.loadDesc = exception.loadDesc;
                }

                // 同时更新info.loadList中的数据，确保图表重新渲染时能显示最新的异常说明
                if (this.info && this.info.loadList) {
                    const loadListPerson = this.info.loadList.find(
                        (p) => (p.id && p.id === exception.id) || p.memberName === exception.personName
                    );
                    if (loadListPerson) {
                        loadListPerson.loadDesc = exception.loadDesc;
                    }
                }
            });

            // 重新渲染图表以显示最新的异常说明
            this.twoChart();

            // 触发父组件重新获取数据或更新显示
            this.$emit('exception-updated');
        },

        // 计算负载率汇总数据
        calculateLoadRateSummary(dataList) {
            const over120 = dataList.filter((item) => item.loadRate > 120);
            const under80 = dataList.filter((item) => item.loadRate < 80);

            // 保存负载率<80%的人员列表，用于异常说明弹窗，包含已有的异常说明
            this.under80PersonsList = under80.map((item) => ({
                id: item.executionPlanId,
                name: item.memberName,
                account: item.memberAccount,
                load: item.load,
                // 将已有的异常说明传递给弹窗
                loadDesc: item.loadDesc || ''
            }));

            this.loadRateData = {
                over120: {
                    label: '负载率>120%',
                    count: over120.length,
                    persons: over120.map((item) => item.memberName).join('、') || '无'
                },
                under80: {
                    label: '负载率<80%',
                    count: under80.length,
                    persons: under80.map((item) => item.memberName).join('、') || '无'
                }
            };
        },

        // eslint-disable-next-line max-lines-per-function
        async twoChart() {
            if (Object.keys(this.info).length === 0) return;
            const chartDom = this.$refs.chartMain;
            const myChart = echarts.init(chartDom);
            myChart.resize();

            const { oneTheoryTime, twoTheoryTime, loadList } = this.info;

            // 根据新的数据结构处理数据
            const dataWithLoadRate = loadList.map((item) => ({
                ...item,
                // 使用接口返回的 load 字段作为负载率（已经是百分比形式）
                loadRate: item.load,
                // 保留异常说明信息
                loadDesc: item.loadDesc || ''
            }));

            // 计算汇总数据
            this.calculateLoadRateSummary(dataWithLoadRate);

            // 提取排序后的数据，使用新的字段名
            const sortedNames = dataWithLoadRate.map((item) => item.memberName);
            // 预估工时数据
            const sortedTeamTimes = dataWithLoadRate.map((item) => item.estimate);
            const sortedExternalTimes = dataWithLoadRate.map((item) => item.otherEstimate);
            const sortedLeaveTimes = dataWithLoadRate.map((item) => item.leaveTime);
            const sortedIdleTimes = dataWithLoadRate.map((item) => item.idleTime);
            const sortedLoadRates = dataWithLoadRate.map((item) => `${Math.round(item.load)}`);

            // 实际消耗工时数据
            const sortedConsumedTimes = dataWithLoadRate.map((item) => item.consumed);
            const sortedOtherConsumedTimes = dataWithLoadRate.map((item) => item.otherConsumed);
            const sortedRealIdleTimes = dataWithLoadRate.map((item) => item.realIdleTime);
            const sortedRealLoadRates = dataWithLoadRate.map((item) => `${Math.round(item.realLoad)}`);

            // 设置图表选项
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                    // formatter: (params) => {
                    // }
                },
                grid: {
                    x: 20,
                    y: 40,
                    x2: 80,
                    y2: 0,
                    containLabel: true
                },
                dataZoom: [
                    // 滑动条
                    {
                        yAxisIndex: 0,
                        show: false,
                        startValue: 0,
                        endValue: 5
                    }
                ],

                legend: {
                    data: [
                        '本团队预估工时',
                        '团队外预估工时',
                        '请假时间',
                        '预估空闲工时',
                        '本团队实际工时',
                        '团队外实际工时',
                        '实际空闲工时',
                        '负载率',
                        '实际负载率'
                    ],
                    textStyle: {
                        fontSize: 12
                    }
                },
                xAxis: {
                    type: 'value',
                    show: true,
                    axisTick: {
                        show: false,
                        lineStyle: {
                            color: '#ECEDF0',
                            width: 1,
                            type: 'solid'
                        }
                    }
                },
                yAxis: {
                    type: 'category',
                    data: sortedNames,
                    axisLabel: {
                        fontSize: 12,
                        textStyle: {
                            color: '#333'
                        }
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#ECEDF0',
                            width: 1,
                            type: 'solid'
                        }
                    }
                },
                series: [
                    // 预估工时系列
                    {
                        name: '本团队预估工时',
                        type: 'bar',
                        stack: 'estimate',
                        barGap: '20%',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#1E90FF'
                        },
                        barWidth: 18,
                        data: sortedTeamTimes,
                        markLine: {
                            symbol: 'none',
                            data: (() => {
                                const lines = [];
                                // 只有当理论值存在时才显示
                                if (oneTheoryTime) {
                                    lines.push({
                                        xAxis: oneTheoryTime,
                                        lineStyle: {
                                            color: 'red',
                                            type: 'solid',
                                            width: 2
                                        },
                                        label: {
                                            show: true,
                                            position: 'end',
                                            formatter: `理论 ${oneTheoryTime}`,
                                            offset: [0, -15]
                                        }
                                    });
                                }

                                // 只有当两个理论值都存在且不相等时才显示第二条
                                if (twoTheoryTime && twoTheoryTime !== oneTheoryTime) {
                                    lines.push({
                                        xAxis: twoTheoryTime,
                                        lineStyle: {
                                            color: 'orange',
                                            type: 'dashed',
                                            width: 2
                                        },
                                        label: {
                                            show: true,
                                            position: 'end',
                                            formatter: `理论 ${twoTheoryTime}`,
                                            offset: [0, 15]
                                        }
                                    });
                                }

                                return lines;
                            })()
                        }
                    },
                    {
                        name: '团队外预估工时',
                        type: 'bar',
                        stack: 'estimate',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        itemStyle: {
                            color: '#87CEFA'
                        },
                        barWidth: 18,
                        data: sortedExternalTimes
                    },
                    {
                        name: '请假时间',
                        type: 'bar',
                        stack: 'estimate',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        itemStyle: {
                            color: '#00ff00'
                        },
                        barWidth: 18,
                        data: sortedLeaveTimes
                    },
                    {
                        name: '预估空闲工时',
                        type: 'bar',
                        stack: 'estimate',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        itemStyle: {
                            color: '#FF8C00'
                        },
                        barWidth: 18,
                        data: sortedIdleTimes
                    },
                    // 实际消耗工时系列
                    {
                        name: '本团队实际工时',
                        type: 'bar',
                        stack: 'actual',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#4169E1'
                        },
                        barWidth: 18,
                        data: sortedConsumedTimes
                    },
                    {
                        name: '团队外实际工时',
                        type: 'bar',
                        stack: 'actual',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        itemStyle: {
                            color: '#6495ED'
                        },
                        barWidth: 18,
                        data: sortedOtherConsumedTimes
                    },
                    {
                        name: '实际空闲工时',
                        type: 'bar',
                        stack: 'actual',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        itemStyle: {
                            color: '#FF6347'
                        },
                        barWidth: 18,
                        data: sortedRealIdleTimes
                    },
                    {
                        name: '负载率',
                        type: 'bar',
                        barWidth: 18,
                        barGap: '-100%',
                        label: {
                            show: true,
                            position: ['100%', '10%'],
                            color: 'red',
                            fontWeight: 'bold',
                            formatter: (params) => `${sortedLoadRates[params.dataIndex]}%`
                        },
                        itemStyle: {
                            color: 'transparent'
                        },
                        data: sortedTeamTimes.map(() => 0)
                    },
                    // 实际消耗负载率
                    {
                        name: '',
                        type: 'bar',
                        barWidth: 18,
                        barGap: '-100%',
                        label: {
                            show: true,
                            position: ['100%', '120%'],
                            color: 'blue',
                            fontWeight: 'bold',
                            formatter: (params) => `${sortedRealLoadRates[params.dataIndex]}%`
                        },
                        itemStyle: {
                            color: 'transparent'
                        },
                        data: sortedTeamTimes.map(() => 0)
                    }
                ]
            };
            myChart.setOption(option);
        }
    }
};
</script>

<style scoped lang="scss">
.worktime-chart-container {
    display: flex;
    gap: 16px;
    height: 100%;
}
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
.chart-left {
    width: 30%;
    height: 100%;
}
.load-rate-item {
    font-weight: bold;
    border-bottom: 1px solid #dcdfe6;
}

.chartTwo {
    flex: 1;
    height: 100%;
}
</style>
