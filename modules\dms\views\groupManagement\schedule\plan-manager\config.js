import { getDateRange } from 'snbcCommon/common/picker-options.js';
import CommonItems from 'snbcCommon/common/form-items.js';
import dictData from 'dms/constant/dict';

const { radio, dateRange } = CommonItems;

// 部门
const quickQuery = {
    ...radio,
    name: '快捷查询',
    modelKey: 'quickQuery',
    type: 'button',
    elRadios: dictData.planQuickQuery
};

// 创建时间
const startDate = {
    ...dateRange,
    name: '统计周期',
    modelKey: 'dateRange',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd',
        'clearable': false
    }
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [quickQuery, startDate]
};

// 查询条件参数
export const queryParams = {
    dateRange: [],
    quickQuery: '最近一年'
};

// 日期区间选择配置
export const dateTypeOptions = [
    { dateType: '上一年度', dateRange: getDateRange('上一年度') },
    { dateType: '本年度', dateRange: getDateRange('本年度') },
    { dateType: '最近一年', dateRange: getDateRange('最近一年') }
];

// 表格列配置
export const tableColumns = [
    {
        label: '计划名称',
        prop: 'planName',
        minWidth: 260,
        attrs: { align: 'center' }
    },
    {
        label: '计划开始日期',
        prop: 'startDate',
        minWidth: 130,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '计划完成日期',
        prop: 'endDate',
        minWidth: 120,
        attrs: { align: 'center' }
    },
    {
        label: '实际完成日期',
        prop: 'realEnd',
        minWidth: 120,
        attrs: { align: 'center' }
    },
    {
        label: '关闭日期',
        prop: 'closeDate',
        minWidth: 120,
        attrs: { align: 'center' }
    },
    {
        label: '状态',
        prop: 'status',
        minWidth: 100,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '冲刺结果',
        prop: 'sprintResult',
        minWidth: 100,
        attrs: { align: 'center' }
    },
    {
        label: '变更次数',
        prop: 'changeTimes',
        minWidth: 120,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '需求情况',
        prop: 'storySchedule',
        minWidth: 200,
        attrs: { align: 'center' }
    },
    {
        label: '任务情况',
        prop: 'taskSchedule',
        minWidth: 200,
        attrs: { align: 'center' }
    }
];

// 导航标签配置
export const navItems = [
    { field: '', name: '所有', queryField: 'status' },
    { field: '进行中', name: '进行中', queryField: 'status' },
    { field: '草稿', name: '草稿', queryField: 'status' },
    { field: '审核中', name: '审核中', queryField: 'status' },
    { field: '待修改', name: '待修改', queryField: 'status' },
    { field: '已关闭', name: '已关闭', queryField: 'status' },
    { field: '冲刺成功', name: '冲刺成功', queryField: 'sprintResult' },
    { field: '冲刺失败', name: '冲刺失败', queryField: 'sprintResult' },
    { field: '结论待定', name: '结论待定', queryField: 'sprintResult' }
];

// 其他常量配置
export const segmentLabels = ['已按时完成', '已延期完成', '正常进行中', '延期进行中'];

export const segmentColorMap = [
    '#70b603',
    {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 0,
        colorStops: [
            { offset: 0, color: '#70b603' },
            { offset: 1, color: '#FF0000' }
        ]
    },
    '#d7d7d7',
    {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 0,
        colorStops: [
            { offset: 0, color: '#d7d7d7' },
            { offset: 1, color: '#FF0000' }
        ]
    }
];

// 表格字段与对应向后端接口字段的传值
export const sortMap = {
    startDate: 'start_date',
    status: 'status',
    changeTimes: 'version'
};

// ECharts配置选项
export const getChartOption = (workData) => ({
    grid: {
        left: '10%',
        right: '10%',
        top: '10%',
        bottom: '10%'
    },
    xAxis: {
        type: 'category',
        data: workData.labels,
        axisLabel: {
            show: false
        },
        axisTick: {
            show: false
        },
        splitLine: {
            show: false
        }
    },
    yAxis: {
        type: 'value',
        axisLabel: {
            show: false
        },
        axisTick: {
            show: false
        },
        splitLine: {
            show: false
        }
    },
    series: [
        {
            name: '计划工时',
            data: workData.planned,
            type: 'line',
            smooth: true,
            color: '#32CD32',
            lineStyle: {
                type: 'dashed'
            },
            showSymbol: false
        },
        {
            name: '实际工时',
            data: workData.actual,
            type: 'line',
            smooth: true,
            color: '#3370ff',
            showSymbol: false
        }
    ]
});
