<template>
    <div class="group-overview">
        <div>
            <div v-show="!isEmpty">
                <el-divider>待确认正式项目</el-divider>
                <confirm-list @query-complete="handleQueryComplete"></confirm-list>
                <el-divider>待审核临时项目</el-divider>
            </div>
            <review-list></review-list>
        </div>
    </div>
</template>

<script>
import ReviewList from './reviewList/index.vue';
import ConfirmList from './confirmList/index.vue';

export default {
    name: 'Review',
    components: {
        ReviewList,
        ConfirmList
    },
    data() {
        return {
            isEmpty: true
        };
    },
    methods: {
        handleQueryComplete(isEmpty) {
            this.isEmpty = isEmpty;
        }
    }
};
</script>

<style lang="scss" scoped>
.group-overview {
    position: relative;
    padding: 10px 20px;
    ::v-deep .el-tabs__nav-wrap {
        display: flex;
        .el-tabs__item {
            color: #4377ee;
        }
        .el-tabs__item.is-active {
            background-color: #4377ee;
            color: #fff;
        }
    }
    ::v-deep .el-tabs--card > .el-tabs__header {
        border-bottom: 1px solid #4377ee;
    }
}
.group-list {
    position: absolute;
    display: flex;
    align-items: center;
    .group-list-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-right: 15px;
    }
}
</style>
