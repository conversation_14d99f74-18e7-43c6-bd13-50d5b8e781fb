<template>
    <div class="project-management-list">
        <div class="content query-label-line2">
            <project-list
                v-model="activeNavTab"
                :nav-items="navItems"
                :query-params="queryParams"
                :query-config="queryConfig"
                :columns="columns"
                :data="productData"
                :load-data="query"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                :table-attrs="{ height: 'calc(100vh - 190px)' }"
                :actions-width="160"
                config-section="myProductList"
                @search="handleSearch"
                @reset="handleReset"
                @nav-change="handleNavChange"
                @pagination="query"
                @sort-change="handleSortChange"
            >
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button type="text" size="small" @click="handleDetails(row)">详情</el-button>
                    <el-button
                        type="text"
                        size="small"
                        @click="handleAudit(row)"
                        v-if="changeProductPermission && activeNavTab !== '已暂停' && activeNavTab !== '已关闭'"
                        >变更</el-button
                    >
                    <el-button
                        type="text"
                        size="small"
                        @click="handleStop(row)"
                        v-if="
                            shutDownAndCloseProductPermission && activeNavTab !== '已暂停' && activeNavTab !== '已关闭'
                        "
                        >暂停</el-button
                    >
                    <el-button
                        type="text"
                        size="small"
                        style="color: #f56c6c"
                        @click="handleClose(row)"
                        v-if="shutDownAndCloseProductPermission && activeNavTab !== '已关闭'"
                        >关闭</el-button
                    >
                </template>
            </project-list>
        </div>
        <change-content ref="changeContentRef"></change-content>
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';
import changeContent from './components/changeContent.vue';
import { saveParams, restoreParams, syncNavFromSearch } from 'dms/mixins/common.js';

export default {
    name: 'ChangeList',
    components: {
        ProjectList,
        changeContent
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '',
            // 导航栏配置
            navItems,
            // 查询参数
            queryParams: {
                productLine: '',
                productName: '',
                productCode: '',
                productOwner: '',
                productId: '',
                project: '',
                createDate: [],
                closeDate: []
            },
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [],
            // 分页配置
            sortOrder: 'descending',
            sortKey: 'createDate',
            total: 0,
            page: 1,
            limit: 20,
            // 实际使用的查询参数
            params: {}
        };
    },
    computed: {
        // 变更权限
        changeProductPermission() {
            return this.$store.state.permission.btnDatas.includes('Button-changeProduct');
        },
        // 暂停与关闭权限
        shutDownAndCloseProductPermission() {
            return this.$store.state.permission.btnDatas.includes('Button-shutDownAndCloseProduct');
        }
    },
    created() {
        // 优先使用保存的参数
        restoreParams(this, 'myProduct_params', queryParams);
        this.query();
    },
    beforeDestroy() {
        // 页面卸载时保存参数
        saveParams(this, 'myProduct_params');
    },
    methods: {
        // 加载产品数据
        async query() {
            try {
                // 日期处理
                if (this.params.createDate && this.params.createDate.length > 0) {
                    this.params.createStartDate = this.params.createDate[0];
                    this.params.createEndDate = this.params.createDate[1];
                }
                if (this.params.closeDate && this.params.closeDate.length > 0) {
                    this.params.closedStartDate = this.params.closeDate[0];
                    this.params.closedEndDate = this.params.closeDate[1];
                }
                const params = {
                    currentPage: this.page,
                    pageSize: this.limit,
                    ...this.params,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder
                };
                const res = await this.$service.dms.product.getMyProductList(params);
                if (res.code === '0000') {
                    this.productData = res.data?.list || [];
                    // 添加序号
                    if (res?.data?.startRow) {
                        this.productData = this.productData.map((item, index) => {
                            item.index = res.data.startRow + index;
                            return item;
                        });
                    }
                    this.total = res.data?.total || 0;
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        },
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            this.sortKey = prop;
            this.sortOrder = order;
            this.query();
        },
        // 处理搜索
        handleSearch() {
            this.params = this.$tools.cloneDeep(this.queryParams);
            // 搜索时重置到第一页
            this.page = 1;
            // 搜索时同步导航状态 - 根据搜索条件自动设置对应的导航标签
            syncNavFromSearch(this);
            this.query();
        },
        // 处理重置
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
            // 重置时重置到第一页
            this.page = 1;
            this.query();
        },
        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.queryParams[queryField] = field;
            this.params = this.$tools.cloneDeep(this.queryParams);
            this.query();
        },
        // 查看详情
        handleDetails(row) {
            this.$router.push({ name: 'DetailsProduct', query: { id: row.id } });
        },
        // 变更
        handleAudit(row) {
            this.$refs.changeContentRef.open(row);
        },
        // 暂停
        handleStop(row) {
            this.$confirm('确定暂停该产品?', '暂停', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const params = {
                        productId: row.id,
                        type: '已暂停'
                    };
                    this.$service.dms.product.closedProduct(params).then((res) => {
                        if (res.code === '0000') {
                            this.$message.success('暂停成功!');
                            this.query();
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                })
                .catch(() => {
                    this.$message.info('已取消该操作');
                });
        },
        // 关闭
        async handleClose(row) {
            this.$confirm('确定关闭该产品?', '关闭', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const params = {
                        productId: row.id,
                        type: '已关闭'
                    };
                    this.$service.dms.product.closedProduct(params).then((res) => {
                        if (res.code === '0000') {
                            this.$message.success('关闭成功!');
                            this.query();
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                })
                .catch(() => {
                    this.$message.info('已取消该操作');
                });
        }
    }
};
</script>

<style lang="scss" scoped>
.project-management-list {
    padding: 20px;
}
</style>
