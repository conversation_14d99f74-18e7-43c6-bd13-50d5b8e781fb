<!-- 计划跟踪 -->
<template>
    <div>
        <div class="query-container">
            <div class="plan-list">
                <span class="plan-list-title">计划列表</span>
                <el-select v-model="planId">
                    <el-option
                        v-for="i in planListOption"
                        :key="i.planId"
                        :value="i.planId"
                        :label="i.planName"
                    ></el-option>
                </el-select>
            </div>
            <el-tabs v-model="tabsViewsConfig.activeName">
                <el-tab-pane
                    v-for="tab in tabsViewsConfig.tabItems"
                    :key="tab.value"
                    :name="tab.value"
                    :label="tab.label"
                ></el-tab-pane>
            </el-tabs>
        </div>
        <component :is="tabsViewsConfig.activeName"></component>
    </div>
</template>
<script>
import TaskSituation from './TaskSituation.vue';
import DemandSituation from './DemandSituation.vue';

export default {
    name: 'PlanTracking',
    components: {
        TaskSituation,
        DemandSituation
    },
    data() {
        return {
            planId: '',
            planListOption: '',
            tabsViewsConfig: {
                activeName: '需求视图',
                tabItems: [
                    { label: '需求视图', value: 'DemandSituation' },
                    {
                        label: '任务视图',
                        value: 'TaskSituation'
                    }
                ]
            },
        };
    },
    computed: {
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        }
    },
    watch: {
        groupValue() {
            this.getChangeReason();
        }
    },
    mounted() {
        this.getChangeReason();
    },
    methods: {
        /**
         * 获取变更原因
         */
        async getChangeReason() {
            try {
                const params = {
                    projectId: this.groupValue
                };
                const res = await this.$service.dms.plan.getPlanOptions(params);
                if (res.code === '0000') {
                    console.log(res.data, 'res');
                    this.planListOption = res.data;
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.query-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2vw 0 0;
    .plan-list {
        display: flex;
        align-items: center;
        width: 30vw;
        .plan-list-title {
            width: 80px;
            padding-right: 8px;
            text-align: left;
            font-weight: 600;
        }
        .el-select {
            width: calc(85% - 80px);
        }
    }
    ::v-deep .el-tabs__item {
        padding: 0 20px !important;
        text-align: center;
        background-color: #fff;
        border: 1px solid #dddddd;
    }
    ::v-deep .el-tabs__item.is-active {
        background-color: #4377ee;
        color: #fff;
        border: none;
    }
}
</style>
