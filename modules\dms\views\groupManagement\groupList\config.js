import CommonItems from 'snbcCommon/common/form-items.js';
import dictData from 'dms/constant/dict';

const { select, input, dateRange, productLineSelector, departmentSelector } = CommonItems;

// 部门
const orgCode = {
    ...departmentSelector,
    name: '部门',
    modelKey: 'orgCode'
};

// 产品线
const productLine = {
    ...productLineSelector,
    name: '产品线',
    modelKey: 'productLine'
};

// 团队名称
const projectName = {
    ...input,
    name: '团队名称',
    modelKey: 'projectName'
};

// Team Leader
const pmName = {
    ...input,
    name: 'Team Leader',
    modelKey: 'pmName'
};

// 直属上级
const higherUp = {
    ...input,
    name: '直属上级',
    modelKey: 'higherUp'
};

// 负责产品
const projectId = {
    ...input,
    name: '负责产品',
    modelKey: 'projectId'
};

// 创建时间
const startDate = {
    ...dateRange,
    name: '创建时间',
    modelKey: 'startDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 关闭时间
const endDate = {
    ...dateRange,
    name: '关闭时间',
    modelKey: 'endDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 团队级别
const projectLevel = {
    ...select,
    name: '团队级别',
    modelKey: 'projectLevel',
    elOptions: dictData.groupLevelData
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [orgCode, productLine, projectName, pmName, higherUp, projectId, startDate, endDate, projectLevel]
};

// 查询条件参数
export const queryParams = {
    orgCode: '',
    productLine: '',
    projectName: '',
    pmName: '',
    higherUp: '',
    projectId: '',
    startDate: [],
    endDate: [],
    projectLevel: ''
};

// 导航栏配置
export const navItems = [
    { field: '', name: '所有', queryField: 'projectStatus' },
    { field: '进行中', name: '进行中', queryField: 'projectStatus' },
    { field: '关闭', name: '关闭', queryField: 'projectStatus' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        minWidth: 70,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'orgName',
        label: '部门',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'higherUp',
        label: '直属上级',
        minWidth: 140,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectName',
        label: '团队名称',
        minWidth: 200,
        showOverflowTooltip: true,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'pmName',
        label: 'Team Leader',
        minWidth: 140,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectLevel',
        label: '团队级别',
        minWidth: 120,
        sortable: 'custom',
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productName',
        label: '负责产品',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectStatus',
        label: '团队状态',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'startDate',
        label: '创建时间',
        minWidth: 140,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'endDate',
        label: '关闭时间',
        minWidth: 140,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    }
];
