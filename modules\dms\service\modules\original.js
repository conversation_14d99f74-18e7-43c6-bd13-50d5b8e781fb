/**
 * 需求管理相关服务接口
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 需求管理接口
        original: {
            // 获取列表（支持分页和筛选）
            getOriginalList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getDemandResponsibleList',
                    method: 'post',
                    data
                });
            },
            // 提需求
            addOriginal(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/addOrStory',
                    method: 'post',
                    data
                });
            },
            // 上传文件
            uploadFile() {
                return `${basePath.systemApi.system}/dms/file/upload`;
            },

            // 查询需求负责人
            getEmployees(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/employee/getEmployees',
                    method: 'get',
                    params
                });
            },
            // 需求变更接口
            changeRequire(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/updateStory',
                    method: 'post',
                    data
                });
            },
            // 替换需求
            replacementDemand(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/changeStory',
                    method: 'get',
                    params
                });
            },
            // 刪除文件
            deleteFile(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/file/delete',
                    method: 'get',
                    params
                });
            },
            // 下载文件
            xzFile(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/file/download',
                    method: 'get',
                    responseType: 'blob',
                    params
                });
            },
            // 批量创建
            batchCreat(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/addStories',
                    method: 'post',
                    data
                });
            },
            // 查询产品模块
            getProductModule(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/product/getProductModule',
                    method: 'get',
                    params
                });
            },
            // 编辑产品需求
            editStory(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/editStory',
                    method: 'post',
                    data
                });
            },
            // 用户产品需求变更
            changeConfirm(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/story/demandChangeConfirm',
                    method: 'post',
                    data
                });
            },
            updateStoryCheck(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/story/updateStoryCheck',
                    method: 'put',
                    data
                });
            },
            // 移除需求
            removeDemand(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/story/removeDemand',
                    method: 'post',
                    params
                });
            },
            // 查询需求详情接口
            getStoryInfo(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getStoryInfo',
                    method: 'get',
                    params
                });
            },
            // 查询文件
            getFile(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/file/getFile',
                    method: 'get',
                    params
                });
            },
            // 查询要替换需求
            getStoryName(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getStoryName',
                    method: 'get',
                    params
                });
            },
            // 替换
            changeStory(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/changeStory',
                    method: 'get',
                    params
                });
            },
            // 查询权限
            updatePermission(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/story/updatePermission',
                    method: 'get',
                    params
                });
            },
            // 解析用户需求（批量创建）
            parseUserDemand(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/story/parseUserStories',
                    method: 'post',
                    data: params
                });
            },
            // 解析产品需求（批量创建）
            parseProductDemand(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/story/parseProductStories',
                    method: 'post',
                    data: params
                });
            }
        }
    };

    return service;
};
