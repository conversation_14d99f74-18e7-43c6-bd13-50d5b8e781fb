<template>
    <div>
        <el-dialog
            :title="seeDetailstitle"
            :visible.sync="localVisible"
            width="60%"
            @close="handleClose()"
            class="custom-dialog"
        >
            <div class="product-structure-container">
                <DraggableTree
                    style="width: 600px"
                    :data="rootNode"
                    :isTeamLeader="isTeamLeader"
                    :productName="row.productName"
                    @add-node="handleTreeDataChange"
                    @remove-node="handleTreeDataChange"
                    @node-drop="handleTreeDataChange"
                    @label-change="handleTreeDataChange"
                ></DraggableTree>
            </div>
            <span slot="footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="checkRecordConfirm">提 交</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import DraggableTree from 'dms/views/productManagement/components/DraggableTree.vue';

export default {
    name: 'ChangeContent',
    components: { DraggableTree },
    data() {
        return {
            // 弹窗信息
            localVisible: false,
            seeDetailstitle: '修改产品结构',
            row: {},
            // 是否是团队leader
            isTeamLeader: false,
            // 产品子节点数据（从接口获取的子节点）
            productChildren: []
        };
    },
    computed: {
        // 根节点（产品节点），包含从接口获取的子节点
        rootNode() {
            return [
                {
                    id: this.row.id,
                    moduleName: this.row.productName,
                    type: 'product',
                    moduleType: 'P',
                    sort: 1,
                    children: this.productChildren
                }
            ];
        }
    },
    methods: {
        // 打开查看详情弹窗
        open(row) {
            this.localVisible = true;
            this.row = row;
            this.getProductTreedata();
        },

        /**
         * 处理树形结构变化
         * @param {Object} param0 树形结构数据
         */
        handleTreeDataChange({ treeData }) {
            // 只保存根节点下的子节点
            this.productChildren = treeData[0]?.children || [];
        },

        /*
         * 获取产品结构数据
         */
        async getProductTreedata() {
            try {
                const params = { productId: this.row.id };
                const res = await this.$service.dms.product.getProductModuleInfo(params);
                if (res.code !== '0000') {
                    this.$tools.message.err(res.message || '系统异常');
                    this.productChildren = [];
                    return;
                }
                // 将接口返回的子节点数据赋值给productChildren
                this.productChildren = res.data || [];
            } catch (error) {
                this.$tools.message.err('系统异常');
                this.productChildren = [];
            }
        },
        async checkRecordConfirm() {
            // 构建完整的提交数据（包含根节点和子节点）
            const submitData = {
                ...this.rootNode[0],
                // 补充产品ID
                productId: this.row.id
            };
            try {
                const res = await this.$service.dms.product.changeProduct(submitData);
                if (res.code === '0000') {
                    this.$message.success(res.message || '修改成功');
                    this.localVisible = false;
                    this.$parent.handleSearch();
                } else {
                    this.$message.error(res.message || '修改失败，请重试');
                }
            } catch (error) {
                console.error('提交产品结构失败', error);
                this.$message.error('系统异常，提交失败');
            }
        },
        // 弹窗关闭
        handleClose() {
            this.localVisible = false;
            // 重置数据
            this.productChildren = [];
            this.row = {};
        }
    }
};
</script>
<style lang="scss" scoped></style>
