<template>
    <div style="padding: 20px">
        <formula-title :title="title"></formula-title>
        <el-form :model="addForm" ref="dataForm" label-width="140px">
            <div class="add-flex">
                <el-form-item label="产品名称" prop="productName">
                    <el-input
                        v-model="addForm.productName"
                        maxlength="64"
                        style="width: 300px"
                        :disabled="true"
                    ></el-input>
                </el-form-item>
                <el-form-item label="产品编号" prop="productCode">
                    <el-input v-model="addForm.productCode" style="width: 300px" :disabled="true"></el-input>
                </el-form-item>
            </div>
            <div class="add-flex">
                <el-form-item label="产品线" prop="productLine">
                    <el-input v-model="addForm.productLine" style="width: 300px" :disabled="true"></el-input>
                </el-form-item>
                <el-form-item label="Product Owner" prop="productOwner">
                    <PeopleSelector
                        v-model="addForm.productOwner"
                        :isMultipled="false"
                        style="width: 300px"
                        :disabled="true"
                    />
                </el-form-item>
            </div>
            <div class="add-flex">
                <el-form-item label="产品状态" prop="productStatus">
                    <el-select v-model="addForm.productStatus" style="width: 300px" :disabled="true">
                        <el-option v-for="item in statusData" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
            </div>
            <el-form-item label="目标用户" prop="targetUsers">
                <el-input
                    type="textarea"
                    v-model="addForm.targetUsers"
                    maxlength="500"
                    :rows="4"
                    placeholder="建议参考的模板：<行业/身份>中面临<具体痛点>的<角色>"
                    :disabled="true"
                ></el-input>
            </el-form-item>
            <el-form-item label="产品定位" prop="productPosition">
                <el-input
                    type="textarea"
                    v-model="addForm.productPosition"
                    placeholder="建议参考的模板：针对<细分市场>的<产品类型>，以<差异化特性>区别于<竞品类别>"
                    maxlength="500"
                    :rows="4"
                    :disabled="true"
                ></el-input>
            </el-form-item>
            <el-form-item label="核心价值" prop="coreValue">
                <el-input
                    type="textarea"
                    v-model="addForm.coreValue"
                    placeholder="建议参考的模板：功能价值：通过<核心技术/功能>实现<用户获益>，如<使用场景>。情感价值：帮助用户<心理诉求>。量化价值：将<原有指标>从X提升至Y。"
                    maxlength="500"
                    :rows="4"
                    :disabled="true"
                ></el-input>
            </el-form-item>
            <el-form-item label="竞争策略" prop="competitiveStrategy">
                <el-input
                    type="textarea"
                    v-model="addForm.competitiveStrategy"
                    placeholder="建议参考的模板：差异化路径：在<维度>上超越竞品或保持同等水平（如：<具体指标>）。防御策略：通过<壁垒类型>构建护城河，如<专利/生态等>。替代方案转化：针对<竞品用户>的<迁移诱因>。"
                    maxlength="500"
                    :rows="4"
                    :disabled="true"
                ></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';
import PeopleSelector from 'dms/components/PeopleSelector';

export default {
    components: { formulaTitle, PeopleSelector },
    props: {
        id: {
            type: String,
            default: () => ''
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            title: '产品详情',
            addForm: {
                productName: '',
                productCode: '',
                productLine: '',
                productOwner: '',
                targetUsers: '',
                productPosition: '',
                coreValue: '',
                competitiveStrategy: '',
                productStatus: ''
            },
            statusData: [
                {
                    label: '进行中',
                    value: '进行中'
                },
                {
                    label: '已暂停',
                    value: '已暂停'
                },
                {
                    label: '已关闭',
                    value: '已关闭'
                }
            ]
        };
    },
    created() {
        // 当组件创建时，如果 id 有值则查询产品信息
        if (this.id) {
            this.getProductInfo();
        }
    },
    methods: {
        async getProductInfo() {
            try {
                const params = {
                    productId: this.id
                };
                const res = await this.$service.dms.product.getProductInfo(params);
                if (res.code === '0000') {
                    this.addForm = res.data;
                } else {
                    this.$message.error(res.message || '获取产品详情失败');
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.add-flex {
    width: 100%;
    display: flex;
    justify-content: flex-start;
}
@import 'dms/views/demandManagement/common/common.scss';

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 13px;
    }
}
</style>
