<template>
    <div>
        <el-dialog title="需求详情" :visible.sync="dialogVisible" width="600px" top="5vh">
            <template #title>
                <div class="title">
                    <span>需求审核</span>
                    <span class="split-title">认领需求</span>
                </div>
            </template>
            <div class="content">
                <div>
                    请确认是否接受 <span class="font-weight-800">{{ proposal }}</span> 提出的以下需求：
                </div>
                <div class="font-weight-800 mt-10">{{ demandName }}</div>
                <el-button type="text" class="detail-btn" @click="OriginalDemandDialogVisible = true"
                    >查看需求详情</el-button
                >
            </div>

            <div slot="footer">
                <div class="footer">
                    <el-button type="danger" @click="handleReject">拒 绝</el-button>
                    <el-button type="primary" @click="handleAccept">接 受</el-button>
                </div>
            </div>
        </el-dialog>
        <OriginalDemand :visible.sync="OriginalDemandDialogVisible" :demandId="demandId"></OriginalDemand>
        <DecompositionDialog
            :visible.sync="decompositionDialogVisible"
            :demandId="demandId"
            @lastStep="handleLastStep"
            :hasLastStep="true"
            :rowData="rowData"
            :historySubDemandData="historySubDemandData"
            @success="() => this.$emit('success')"
        ></DecompositionDialog>
    </div>
</template>

<script>
import OriginalDemand from 'dms/views/demandManagement/demandDetail/OriginalDemand.vue';
import DecompositionDialog from './DecompositionDialog.vue';

export default {
    name: 'ClaimingDialog',
    components: {
        OriginalDemand,
        DecompositionDialog
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        proposal: {
            type: String,
            default: ''
        },
        demandName: {
            type: String,
            default: ''
        },
        demandId: {
            type: String,
            default: ''
        },
        rowData: {
            type: Object,
            default: () => ({})
        },
        // 历史子需求列表
        historySubDemandData: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            // 原始需求详情弹窗
            OriginalDemandDialogVisible: false,
            // 拆分需求弹窗
            decompositionDialogVisible: false
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.OriginalDemandDialogVisible = false;
            this.decompositionDialogVisible = false;
            this.dialogVisible = false;
            this.$emit('update');
        },
        /**
         * 拒绝需求
         */
        async handleReject() {
            await this.$confirm('确定要拒绝此需求吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            });
            const api = this.$service.dms.demand.demandCheckStatusChange;
            const params = {
                storyId: this.demandId,
                storyVersion: this.rowData.changeTimes + 1,
                checkId: this.rowData.checkId,
                checkAction: '拒绝',
                reason: '原始需求已提出，经确认，拒绝接受此需求',
                checkType: this.rowData.checkType,
                status: '已拒绝'
            };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$emit('success');
                this.$message.success('保存成功');
                this.closeDialog();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        handleAccept() {
            this.dialogVisible = false;
            this.decompositionDialogVisible = true;
        },
        handleLastStep() {
            this.dialogVisible = true;
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    display: flex;
    gap: 4px;
    .split-title {
        font-size: 12px;
        align-self: flex-end;
    }
}

.flex {
    display: flex;
}
.font-weight-800 {
    font-weight: 800;
}
.content {
    width: 100%;
    height: 100%;
    padding-top: 10px;
    font-size: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.mt-10 {
    margin-top: 10px;
}

.detail-btn {
    margin-top: 20px;
    text-decoration: underline;
}
.footer {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 10px;
}
</style>
