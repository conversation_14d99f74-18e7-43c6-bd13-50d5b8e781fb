/**
 * 项目管理相关服务接口
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 计划管理接口
        plan: {
            // 获取项目列表
            getPlanList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getExecutionPlanList',
                    method: 'post',
                    data
                });
            },
            // 获取计划基本信息
            getPlanBaseInfo(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getExecutionPlanInfo',
                    method: 'get',
                    params: data
                });
            },
            // 保存计划基本信息
            savePlanBaseInfo(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/updatePlan',
                    method: 'post',
                    data
                });
            },
            // 获取计划工时负载率视图
            getPlanWorkHourChart(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getExecutionPlanLoad',
                    method: 'get',
                    params: data
                });
            },
            // 获取计划下拉列表选项
            getPlanOptions(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getPlans',
                    method: 'get',
                    params: data
                });
            },
            // 获取计划任务信息
            getPlanTaskList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getTask',
                    method: 'get',
                    params: data
                });
            },
            // 更新任务信息
            updatePlanTaskList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/updateTask',
                    method: 'post',
                    data
                });
            },
            // 新增计划（获取计划id）
            addPlan(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/addPlan',
                    method: 'post',
                    data
                });
            },
            // 关闭计划
            closePlan(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/closePlan',
                    method: 'get',
                    params: data
                });
            },
            // 获取时间段的工作日天数
            getWorkDayInPeriod(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'workDays/getWorkDays',
                    method: 'get',
                    loading: false,
                    params: data
                });
            },
            // 获取项目/团队关联的需求列表
            getRelatedDemandList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/story/getProjectStory',
                    method: 'post',
                    data
                });
            },
            // 获取项目/团队关联的产品
            getRelatedProduct(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/product/getProjectProductList',
                    method: 'get',
                    params: data
                });
            },
            // 获取项目/团队成员
            getMemebersInGroup(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getMembers',
                    method: 'get',
                    params: data
                });
            },
            // 获取遗留bug列表
            getUnsolvedBugList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'ztBug/getLeaveBug',
                    method: 'get',
                    params: data
                });
            },
            // 更新异常负载说明
            addWorkLoadExtraInfo(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/updateLoadDesc',
                    method: 'post',
                    data
                });
            },
            // 提交审核（新建）
            submitReview(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/addAuditPlan',
                    method: 'get',
                    params: data
                });
            },
            // 删除计划
            deletePlan(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/deleteExecutionPlan',
                    method: 'get',
                    params: data
                });
            },
            // 取消审核
            cancelReview(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/deleteCheck',
                    method: 'get',
                    params: data
                });
            },
            // 保存冲刺评价
            saveFinalEvaluation(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/addSprintPlan',
                    method: 'post',
                    data
                });
            },
            // 提交冲刺评价
            submitFinalEvaluation(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/addAuditSprint',
                    method: 'post',
                    data
                });
            },
            // 保存变更信息
            savePlanChangeInfo(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/addChangePlanInfo ',
                    method: 'post',
                    data
                });
            },
            // 克隆一份计划
            cloneNewPlan(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/cloneExecutionPlan',
                    method: 'get',
                    params: data
                });
            },
            // 查询计划审核结果
            getPlanReviewResult(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getAuditResult',
                    method: 'get',
                    params: data
                });
            },
            // 查询计划变更原因
            getChangeReason(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getPlanChangeInfo',
                    method: 'get',
                    params: data
                });
            },
            // 查询计划审核列表
            getPlanReviewList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getAuditList',
                    method: 'post',
                    data
                });
            },
            // 计划审核提交（批准/拒绝）
            reviewPlan(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/auditPlan',
                    method: 'post',
                    data
                });
            },
            // 获取冲刺审核列表
            getEvaluationReviewList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getSprintList',
                    method: 'post',
                    data
                });
            },
            // 进行冲刺审核
            reviewEvaluation(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/auditSprint',
                    method: 'post',
                    data
                });
            },
            // 获取计划跟踪-需求视图列表
            getPlanTrackDemandList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'executionPlan/getPlanTrackTask',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
