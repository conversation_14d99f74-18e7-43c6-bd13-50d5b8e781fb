<template>
    <div>
        <el-dialog :title="getTitle()" :visible.sync="localVisible" width="60%" class="custom-dialog" @close="cancel">
            <el-form :model="addForm" ref="dataForm" label-width="120px" :rules="userRules">
                <el-form-item label="需求名称" prop="demandName">
                    <el-input v-model="addForm.demandName" placeholder="请输入需求名称" maxlength="64"></el-input>
                </el-form-item>
                <el-form-item label="优先级" prop="priority">
                    <el-select v-model="addForm.priority" placeholder="请选择优先级" clearable>
                        <el-option
                            v-for="item in priorityData"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="需求描述" prop="description">
                    <el-input type="textarea" v-model="addForm.description" maxlength="500" :rows="4"></el-input>
                </el-form-item>
                <el-form-item label="验收标准" prop="verify">
                    <el-input type="textarea" v-model="addForm.verify" maxlength="500" :rows="4"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="cancel()">取消</el-button>
                <el-button @click="goback()">上一步</el-button>
                <el-button type="primary" @click="confirm()">确认</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
// 这个页面是变更确认的弹窗
import Constant from 'dms/constant/dict.js';

const { priorityData } = Constant;
export default {
    name: 'ChangeConfirmPop',
    data() {
        return {
            // 弹窗信息
            localVisible: false,
            addForm: {
                demandName: '',
                priority: '',
                description: '',
                verify: '',
                demandId: null
            },
            // 传参数据
            type: '',
            row: {},
            // 优先级
            priorityData,
            userRules: {
                demandName: [
                    {
                        required: true,
                        message: '请输入需求名称',
                        trigger: 'change'
                    }
                ],
                priority: [
                    {
                        required: true,
                        message: '请选择优先级',
                        trigger: 'change'
                    }
                ],
                description: [
                    {
                        required: true,
                        message: '请输入需求描述',
                        trigger: 'blur'
                    }
                ]
            }
        };
    },
    methods: {
        // 打开弹窗
        open(row, type) {
            this.row = row;
            this.type = type;
            this.$nextTick(() => {
                this.addForm = row;
                this.$refs.dataForm && this.$refs.dataForm.resetFields();
            });
            this.localVisible = true;
        },
        getTitle() {
            return '变更确认';
        },
        confirm() {
            this.$refs.dataForm.validate((valid) => {
                if (!valid) return;
                const params = this.addForm;
                this.$service.dms.original.changeConfirm(params).then((res) => {
                    if (res.code === '0000') {
                        this.$message.success(res.message);
                        this.localVisible = false;
                        this.$parent.query();
                    } else {
                        this.$message.error(res.message);
                    }
                });
            });
        },
        // 返回上一步
        goback() {
            this.$nextTick(() => {
                this.localVisible = false;
                this.$parent.handlechangeConfirm(this.row, this.type);
            });
        },
        // 关闭弹窗
        cancel() {
            this.$nextTick(() => {
                this.localVisible = false;
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.add-flex {
    display: flex;
    justify-content: space-between;
}
</style>
