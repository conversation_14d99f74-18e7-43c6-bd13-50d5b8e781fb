<template>
    <div>
        <el-dialog title="需求详情" :visible.sync="dialogVisible" width="85%" top="5vh">
            <!-- 基本信息 -->
            <div class="title">基本信息</div>
            <el-descriptions :column="4">
                <el-descriptions-item label="需求ID">{{ form.id || '-' }}</el-descriptions-item>
                <el-descriptions-item label="需求名称">{{ form.storyName || '-' }}</el-descriptions-item>
                <el-descriptions-item label="创建时间">{{ form.proposalDate || '-' }}</el-descriptions-item>
                <el-descriptions-item label="创建人">{{ form.proposer || '-' }}</el-descriptions-item>
                <el-descriptions-item label="期望交付日期">{{ form.deliveryDate || '-' }}</el-descriptions-item>
                <el-descriptions-item label="负责人">{{ form.ownerName || '-' }}</el-descriptions-item>
                <el-descriptions-item label="需求等级">{{ form.storyLevel || '-' }}</el-descriptions-item>
                <el-descriptions-item label="优先级">{{ form.priority || '-' }}</el-descriptions-item>
                <el-descriptions-item label="产品线">{{ form.productLine || '-' }}</el-descriptions-item>
                <el-descriptions-item label="产品">{{ form.productName || '-' }}</el-descriptions-item>
                <el-descriptions-item label="团队/项目">{{ form.projectName || '-' }}</el-descriptions-item>
                <el-descriptions-item label="变更次数">{{ form.changeNo || '-' }}</el-descriptions-item>
                <el-descriptions-item label="需求状态">{{ form.status || '-' }}</el-descriptions-item>
                <el-descriptions-item label="需求进度">{{ form.storyProgress || '-' }}</el-descriptions-item>
                <el-descriptions-item label="发布情况">{{ form.publish || '-' }}</el-descriptions-item>
                <el-descriptions-item label="部署情况">{{ form.deploy || '-' }}</el-descriptions-item>
            </el-descriptions>

            <!-- 需求详情 -->
            <div class="title">需求详情</div>
            <el-descriptions :column="1">
                <el-descriptions-item label="变更原因" v-if="form.reason">{{
                    form.reason || '-'
                }}</el-descriptions-item>
                <el-descriptions-item label="需求描述">{{ form.description || '-' }}</el-descriptions-item>
                <el-descriptions-item label="验收标准">{{ form.verify || '-' }}</el-descriptions-item>
            </el-descriptions>

            <!-- 历史版本 -->
            <div class="title">历史版本</div>

            <el-select
                v-model="selectedVersion"
                placeholder="请选择版本"
                style="margin-bottom: 16px"
                @change="onVersionChange"
            >
                <el-option
                    v-for="item in form.versionVoList"
                    :key="item.version"
                    :label="`V${item.version}`"
                    :value="item.version"
                ></el-option>
            </el-select>
            <el-descriptions v-if="selectedVersion" :column="4">
                <el-descriptions-item label="创建时间">{{
                    currentVersionInfo.proposalDate || '-'
                }}</el-descriptions-item>
                <el-descriptions-item label="需求名称">{{ currentVersionInfo.storyName || '-' }}</el-descriptions-item>
                <el-descriptions-item label="期望交付日期">{{
                    currentVersionInfo.deliveryDate || '-'
                }}</el-descriptions-item>
                <el-descriptions-item label="产品模块">{{ currentVersionInfo.moduleName || '-' }}</el-descriptions-item>
            </el-descriptions>
            <el-descriptions v-if="selectedVersion" :column="4">
                <el-descriptions-item label="需求等级">{{ currentVersionInfo.storyLevel || '-' }}</el-descriptions-item>
                <el-descriptions-item label="优先级">{{ currentVersionInfo.priority || '-' }}</el-descriptions-item>
            </el-descriptions>
            <el-descriptions v-if="selectedVersion" :column="1">
                <el-descriptions-item v-if="currentVersionInfo.reason" label="变更原因">{{
                    currentVersionInfo.reason || '-'
                }}</el-descriptions-item>
                <el-descriptions-item label="需求描述">{{
                    currentVersionInfo.description || '-'
                }}</el-descriptions-item>
                <el-descriptions-item label="验收标准">{{ currentVersionInfo.verify || '-' }}</el-descriptions-item>
            </el-descriptions>

            <div slot="footer"></div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'ProductDemand',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        demandId: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            form: {
                id: '',
                storyName: '',
                storyClass: '',
                source: '',
                sourceNo: '',
                proposalDate: '',
                proposer: '',
                deliveryDate: '',
                owner: '',
                ownerName: '',
                storyType: '',
                productLine: '',
                priority: '',
                status: '',
                changeNo: '',
                version: '',
                estimateCost: '',
                realCost: '',
                costDeviation: '',
                storyProgress: '',
                publish: '',
                deploy: '',
                storyLevel: '',
                moduleName: '',
                scene: '',
                verify: '',
                description: '',
                businessValue: '',
                earnings: '',
                reason: '',
                // 里程碑数据
                projectTaskList: [],
                // 关联产品信息
                productVoList: [],
                // 历史版本数据
                versionVoList: [],
                // 附件信息
                fileVoList: []
            },
            selectedVersion: null
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        currentVersionInfo() {
            return this.form.versionVoList.find((i) => i.version === this.selectedVersion);
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getFromData();
            }
        }
    },
    methods: {
        async getFromData() {
            if (!this.demandId) {
                this.$message.error('需求ID不能为空');
                return;
            }

            try {
                const params = {
                    id: this.demandId
                };
                const api = this.$service.dms.demand.getDemandDetail;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message || '获取需求详情失败');
                    return;
                }

                const { data } = res;
                if (!data) {
                    this.$message.error('获取需求详情失败：数据为空');
                    return;
                }

                // 赋值基本信息
                if (data.baseInfo) {
                    const { baseInfo } = data;
                    this.form.id = baseInfo.id || '';
                    this.form.storyName = baseInfo.storyName || '';
                    this.form.storyClass = baseInfo.storyClass || '';
                    this.form.source = baseInfo.source || '';
                    this.form.sourceNo = baseInfo.sourceNo || '';
                    this.form.proposalDate = baseInfo.proposalDate || '';
                    this.form.proposer = baseInfo.proposer || '';
                    this.form.deliveryDate = baseInfo.deliveryDate || '';
                    this.form.owner = baseInfo.owner || '';
                    this.form.ownerName = baseInfo.ownerName || '';
                    this.form.storyType = baseInfo.storyType || '';
                    this.form.productLine = baseInfo.productLine || '';
                    this.form.priority = baseInfo.priority || '';
                    this.form.status = baseInfo.status || '';
                    this.form.changeNo = baseInfo.changeNo;
                    this.form.version = baseInfo.version || '';
                    this.form.estimateCost = baseInfo.estimateCost || '';
                    this.form.realCost = baseInfo.realCost;
                    this.form.costDeviation = baseInfo.costDeviation;
                    this.form.storyProgress = baseInfo.storyProgress || '';
                    this.form.publish = baseInfo.publish || '';
                    this.form.deploy = baseInfo.deploy || '';
                    this.form.storyLevel = baseInfo.storyLevel || '';
                    this.form.moduleName = baseInfo.moduleName || '';
                }

                // 赋值扩展信息
                if (data.extendInfo) {
                    const { extendInfo } = data;
                    this.form.description = extendInfo.description || '';
                    this.form.verify = extendInfo.verify || '';
                    this.form.scene = extendInfo.scene || '';
                    this.form.businessValue = extendInfo.businessValue || '';
                    this.form.earnings = extendInfo.earnings || '';
                    this.form.reason = extendInfo.reason || '';
                }

                // 赋值里程碑数据
                this.form.projectTaskList = data.projectTaskList || [];

                // 赋值关联产品信息
                if (data.productVoList?.length > 0) {
                    this.form.productName = data.productVoList[0].productName;
                    this.form.projectName = data.productVoList[0].projectName;
                }

                // 赋值历史版本数据
                this.form.versionVoList = data.versionVoList || [];

                // 赋值附件信息
                this.form.fileVoList = data.fileVoList || [];
            } catch (error) {
                console.error('获取需求详情失败:', error);
            }
        },
        /**
         * 版本选择变更
         * @param {Object} version 版本对象
         */
        onVersionChange(version) {
            this.selectedVersion = version;
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.selectedVersion = null;
            this.$emit('update');
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    margin-bottom: 16px;
    margin-top: 32px;

    &:first-child {
        margin-top: 0;
    }
}

.flex {
    display: flex;
}

.info-card {
    margin-bottom: 16px;

    .card-header {
        .card-title {
            font-weight: bold;
            font-size: 14px;
            color: #303133;
        }
    }

    .text-content {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        min-height: 60px;
        color: #303133;
    }
}

::v-deep .el-card__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

::v-deep .el-card__body {
    padding: 20px;
}

::v-deep .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

::v-deep.form .el-form-item__label {
    font-weight: bold;
}

// 表格样式
::v-deep .el-table {
    .el-table__header-wrapper {
        th {
            background-color: #fafbfc;
            color: #606266;
            font-weight: 600;
        }
    }

    .el-table__body-wrapper {
        td {
            color: #303133;
        }
    }

    .el-table__border {
        border-color: #ebeef5;
    }

    th,
    td {
        border-color: #ebeef5;
    }
}
.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 14px;
    }
}
</style>
