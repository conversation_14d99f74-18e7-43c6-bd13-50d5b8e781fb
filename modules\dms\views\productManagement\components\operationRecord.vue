<template>
    <div>
        <div v-for="(item, index) in collapseItems" :key="index" class="collapse-item">
            <div class="collapse-title">
                <div class="approval-note">{{ index + 1 }}、 {{ item.action }}</div>
                <div class="approval-comments">{{ item.comment }}</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        collapseItems: {
            type: Array,
            required: true
        }
    },
    methods: {}
};
</script>

<style scoped lang="scss">
.collapse-item {
    width: 100%;
    height: 80px;
    padding: 10px;
}

.collapse-title {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.approval-note,
.approval-comments {
    height: 50%;
    display: flex;
    align-items: center;
    color: #333;
}

.approval-comments {
    font-weight: bolder;
}
</style>
