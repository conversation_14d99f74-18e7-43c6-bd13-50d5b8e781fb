<template>
    <div>
        <el-dialog title="变更审核" :visible.sync="dialogVisible" width="1000px" top="5vh">
            <div class="review-change-content">
                <!-- 提示信息 -->
                <div class="notice-text">
                    原版本发生以下变更，请确认子需求的<span class="highlight">内容</span
                    >是否需要进行变更；如需变更，请在下方选择要变更内容的所有子需求：
                </div>

                <!-- 对比描述列表 -->
                <el-descriptions class="change-descriptions" :column="1" border>
                    <el-descriptions-item label="需求来源" labelClassName="desc-label">
                        <div class="source-content">
                            <div class="source-left">{{ rowData.demandSource }}</div>
                            <div class="source-right">{{ rowData.sourceNo }}</div>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="变更项" labelClassName="desc-label">
                        <div class="change-content">
                            <div class="change-row">
                                <div class="change-before">
                                    <div class="change-label bold">变更前</div>
                                </div>
                                <div class="change-after">
                                    <div class="change-label bold">变更后</div>
                                </div>
                            </div>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="需求名称" labelClassName="desc-label">
                        <div class="change-content">
                            <div class="change-row">
                                <div class="change-before">
                                    <div class="change-label">{{ beforeChangeData.demandNameBefore }}</div>
                                </div>
                                <div class="change-after">
                                    <div class="change-label">{{ rowData.demandName }}</div>
                                </div>
                            </div>
                        </div>
                    </el-descriptions-item>

                    <el-descriptions-item label="需求描述" labelClassName="desc-label">
                        <div class="change-content">
                            <div class="change-row">
                                <div class="change-before">
                                    <div class="change-label">{{ beforeChangeData.descriptionBefore }}</div>
                                </div>
                                <div class="change-after">
                                    <div class="change-label">{{ rowData.description }}</div>
                                </div>
                            </div>
                        </div>
                    </el-descriptions-item>

                    <el-descriptions-item label="期望交付日期" labelClassName="desc-label">
                        <div class="change-content">
                            <div class="change-row">
                                <div class="change-before">
                                    <div class="change-label">{{ beforeChangeData.deliveryDateBefore }}</div>
                                </div>
                                <div class="change-after">
                                    <div class="change-label">{{ rowData.expectedDate }}</div>
                                </div>
                            </div>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item
                        v-if="rowData.demandSource !== '内部需求'"
                        label="分析人"
                        labelClassName="desc-label"
                    >
                        <div class="change-content">
                            <div class="change-row">
                                <div class="change-before">
                                    <div class="change-label">{{ beforeChangeData.ownerNameBefore }}</div>
                                </div>
                                <div class="change-after">
                                    <div class="change-label">{{ rowData.ownerName }}</div>
                                </div>
                            </div>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <el-form ref="form" :model="form">
                <el-form-item label="子需求">
                    <el-select
                        v-model="form.subDemand"
                        multiple
                        @change="changeSelect"
                        placeholder="请选择子需求"
                        style="width: 100%"
                    >
                        <el-checkbox v-model="checked" @change="selectAll">全选</el-checkbox>
                        <el-option
                            v-for="item in subDemandOptions"
                            :key="item.id"
                            :label="item.storyName"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <div class="footer">
                    <el-button @click="handleReject">内容不需要变更</el-button>
                    <el-button type="primary" @click="handleAccept">内容需要变更</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'ReviewChange',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        demandId: {
            type: String,
            default: ''
        },
        // 当行数据
        rowData: {
            type: Object,
            default: () => ({})
        }
    },

    data() {
        return {
            form: {
                subDemand: []
            },
            // 子原始需求下拉选项
            subDemandOptions: [],
            // 变更前数据
            beforeChangeData: {},
            checked: false
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getSubDemandList();
                this.getDamandCheckDetail();
            }
        }
    },
    methods: {
        /**
         * 获取子需求列表
         */
        async getSubDemandList() {
            const params = {
                demandId: this.demandId
            };
            const api = this.$service.dms.demand.getSubDemandList;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.subDemandOptions = res.data;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取变更前内容
         */
        async getDamandCheckDetail() {
            const params = {
                demandId: this.demandId,
                // 这里是上个版本的版本号，即变更次数
                version: this.rowData.changeTimes
            };
            const api = this.$service.dms.demand.getDamandCheckDetail;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.beforeChangeData = res.data || {};
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('update');
        },

        /**
         * 处理拒绝操作
         */
        async handleReject() {
            const params = {
                storyId: this.rowData.demandId,
                // 当前需求版本：变更次数
                storyVersion: this.rowData.changeTimes + 1,
                checkId: this.rowData.checkId,
                checkType: this.rowData.checkType,
                status: '已接受',
                checkAction: '内容不需要变更',
                reason: '原始需求发生变更，经确认，接受此变更，且子原始需求内容不需要变更'
            };
            const api = this.$service.dms.demand.demandCheckStatusChange;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$emit('success');
                this.$message.success('保存成功');
            } catch (error) {
                console.error('Error:', error);
            }
            this.closeDialog();
        },

        /**
         * 处理接受操作
         */
        async handleAccept() {
            if (this.form.subDemand.length === 0) {
                this.$message.warning('请选择子需求');
                return;
            }
            const params = {
                storyId: this.rowData.demandId,
                // 当前需求版本：变更次数
                storyVersion: this.rowData.changeTimes + 1,
                checkId: this.rowData.checkId,
                checkType: this.rowData.checkType,
                status: '已接受',
                checkAction: '内容需要变更',
                reason: '原始需求发生变更，经确认，接受此变更，且子原始需求内容需要变更'
            };
            const api = this.$service.dms.demand.demandCheckStatusChange;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.subDemandCheckChange();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 子原始需求变更
         */
        async subDemandCheckChange() {
            const params = {
                checkType: this.rowData.checkType,
                subOrDemandId: this.form.subDemand
            };

            const api = this.$service.dms.demand.subDemandCheckChange;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$emit('success');
                this.$message.success('保存成功');
                this.closeDialog();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        selectAll() {
            this.form.subDemand = [];

            if (this.checked) {
                this.subDemandOptions.forEach((item) => {
                    this.form.subDemand.push(item.id);
                });
            } else {
                this.form.subDemand = [];
            }
        },
        changeSelect(val) {
            if (val.length === this.subDemandOptions.length) {
                this.checked = true;
            } else {
                this.checked = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.review-change-content {
    padding: 20px 0;
}

.notice-text {
    font-size: 14px;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.5;

    .highlight {
        color: #e74c3c;
        font-weight: bold;
    }
}

.change-descriptions {
    width: 100%;

    ::v-deep .el-descriptions__label {
        background-color: #f9f9f9;
        font-weight: bold;
        text-align: center;
        width: 120px;
        vertical-align: middle;
    }

    ::v-deep .el-descriptions__content {
        padding: 12px;
    }

    .title-content {
        text-align: center;
        font-weight: bold;
    }

    .source-content {
        display: flex;
        justify-content: center;
        align-items: center;

        .source-left {
            flex: 1;
            text-align: center;
        }

        .source-right {
            flex: 1;
            text-align: center;
        }
    }

    .change-content {
        .change-row {
            display: flex;

            .change-before,
            .change-after {
                flex: 1;

                .change-label {
                    text-align: center;
                }
                .bold {
                    font-weight: bold;
                }
                .bold {
                    font-weight: bold;
                }
                .change-text {
                    line-height: 1.4;
                    word-break: break-all;
                }
            }

            .change-before {
                border-right: 1px solid #ddd;
            }
        }
    }

    .normal-content {
        line-height: 1.4;
        word-break: break-all;
    }

    .date-content {
        text-align: center;
        line-height: 1.4;
    }

    .analyst-content {
        text-align: center;
        line-height: 1.4;
    }
}

.footer {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 10px;
}

// 保留原有样式
.title {
    @include section-title;
    display: flex;
    gap: 4px;
    .split-title {
        font-size: 12px;
        align-self: flex-end;
    }
}

.flex {
    display: flex;
}
.font-weight-800 {
    font-weight: 800;
}
.content {
    width: 100%;
    height: 100%;
    padding-top: 10px;
    font-size: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.mt-10 {
    margin-top: 10px;
}

.detail-btn {
    margin-top: 20px;
    text-decoration: underline;
}

::v-deep .desc-label {
    background-color: #f9f9f9;
    font-weight: bold;
    text-align: center;
    width: 120px;
    vertical-align: middle;
}

.el-checkbox {
    text-align: right;
    width: 100%;
    padding-right: 10px;
}
</style>
