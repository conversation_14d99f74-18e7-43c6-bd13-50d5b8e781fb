<template>
    <div>
        <el-dialog :visible.sync="dialogVisible" width="80%" class="custom-dialog" @close="cancel">
            <template #title>
                <div class="title">
                    <span>需求审核</span>
                    <span class="split-title">拆分需求</span>
                </div>
            </template>
            <template v-if="historySubDemandData.length > 0">
                <el-divider>已拆分子需求</el-divider>
                <el-table :data="historySubDemandData" class="dms-table" :max-height="310">
                    <el-table-column label="序号" width="70" align="center">
                        <template #default="scope">
                            {{ scope.$index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="assProductName"
                        label="关联产品"
                        align="center"
                        :showOverflowTooltip="true"
                        min-width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="assProjectName"
                        label="关联团队或项目"
                        align="center"
                        :showOverflowTooltip="true"
                        min-width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="demandName"
                        label="子需求名称"
                        align="center"
                        :showOverflowTooltip="true"
                        min-width="220"
                    >
                    </el-table-column>
                    <el-table-column prop="estimateHour" label="预估工时(d)" align="center" width="90">
                    </el-table-column>
                    <el-table-column prop="expectedDate" label="期望交付日期" width="120" align="center">
                    </el-table-column>
                    <el-table-column prop="notes" label="备注" align="center" min-width="120"> </el-table-column>
                </el-table>
                <el-divider>待拆分子需求</el-divider>
            </template>
            <div class="box-condition">
                <el-form :model="addForm" ref="dataForm" label-width="80px">
                    <div class="flex">
                        <el-form-item label="需求类型" prop="type">
                            <el-select v-model="addForm.storyType" placeholder="请选择需求类型" clearable>
                                <el-option
                                    v-for="item in typeData"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <div class="search-line">
                            <el-form-item label="模版">
                                <el-select
                                    v-model="addForm.template"
                                    @change="handleTemplateChange"
                                    placeholder="请选择模版"
                                    clearable
                                >
                                    <el-option
                                        v-for="item in templateData"
                                        :key="item.id"
                                        :label="item.templateName"
                                        :value="item.id"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-button
                                class="model-btn"
                                type="text"
                                icon="el-icon-setting"
                                @click="templateManagement()"
                                >模板管理</el-button
                            >
                        </div>
                    </div>
                </el-form>
                <div class="box-btn">
                    <el-button type="text" icon="el-icon-plus" @click="addInterval()">新增需求</el-button>
                </div>
            </div>
            <el-form class="form" :model="{ subOrDemandList }" ref="tableForm">
                <el-table :data="subOrDemandList" class="dms-table">
                    <el-table-column label="序号" width="70" align="center">
                        <template #default="scope">
                            {{ scope.$index + 1 }}
                        </template>
                    </el-table-column>
                    <el-table-column label="关联产品" align="center">
                        <template slot-scope="scope">
                            <el-form-item
                                :prop="`subOrDemandList.${scope.$index}.productId`"
                                :rules="rules"
                                style="margin-bottom: 0"
                            >
                                <el-select
                                    v-model="scope.row.productId"
                                    placeholder="请选择产品"
                                    clearable
                                    filterable
                                    style="width: 100%"
                                    @change="handleProductChange($event, scope.row)"
                                >
                                    <el-option
                                        v-for="item in productOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column label="关联团队或项目" align="center">
                        <template slot-scope="scope">
                            <el-form-item
                                :prop="`subOrDemandList.${scope.$index}.projectId`"
                                :rules="rules"
                                style="margin-bottom: 0"
                            >
                                <el-select
                                    v-model="scope.row.projectId"
                                    placeholder="请选择团队或项目"
                                    clearable
                                    filterable
                                    style="width: 100%"
                                    @change="handleProjectChange($event, scope.row)"
                                >
                                    <el-option
                                        v-for="item in groupOrProjectOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column label="子需求名称" align="center">
                        <template slot-scope="scope">
                            <el-form-item
                                :prop="`subOrDemandList.${scope.$index}.storyName`"
                                :rules="rules"
                                style="margin-bottom: 0"
                            >
                                <el-input
                                    v-model="scope.row.storyName"
                                    type="textarea"
                                    placeholder="请输入子需求名称"
                                    :autosize="{ minRows: 2, maxRows: 6 }"
                                    @change="handleProjectChange($event, scope.row)"
                                ></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column label="预估工时(d)" align="center" width="100">
                        <template slot-scope="scope">
                            <el-form-item
                                :prop="`subOrDemandList.${scope.$index}.estimateHour`"
                                :rules="rules"
                                style="margin-bottom: 0"
                            >
                                <div style="display: flex">
                                    <el-input-number
                                        :min="0"
                                        v-model="scope.row.estimateHour"
                                        placeholder="请输入预估工时"
                                        :controls="false"
                                    ></el-input-number>
                                </div>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column label="期望交付日期" width="160" align="center">
                        <template slot-scope="scope">
                            <el-form-item
                                :prop="`subOrDemandList.${scope.$index}.deliveryDate`"
                                :rules="getDeliveryDateRules()"
                                style="margin-bottom: 0"
                            >
                                <div class="date-picker-wrapper">
                                    <el-date-picker
                                        v-model="scope.row.deliveryDate"
                                        @change="handleDateChange($event, scope.row)"
                                        type="date"
                                        style="width: 140px"
                                        placeholder="选择日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        :class="{ 'date-warning': isDateLaterThanExpected(scope.row.deliveryDate) }"
                                    >
                                    </el-date-picker>
                                    <div v-if="isDateLaterThanExpected(scope.row.deliveryDate)" class="warning-text">
                                        日期晚于原始需求
                                    </div>
                                </div>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="showNote" label="备注" align="center">
                        <template slot-scope="scope">
                            <el-form-item :prop="`subOrDemandList.${scope.$index}.notes`" style="margin-bottom: 0">
                                <el-input
                                    v-if="scope.row.isOverDate"
                                    v-model="scope.row.notes"
                                    type="textarea"
                                    placeholder="请输入备注"
                                    :autosize="{ minRows: 2, maxRows: 2 }"
                                ></el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center" fixed="right">
                        <template slot-scope="scope">
                            <div class="rotateButton">
                                <el-button
                                    type="text"
                                    icon="el-icon-delete"
                                    style="color: red"
                                    @click="deleteInterval(scope.$index)"
                                ></el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <span slot="footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button v-if="hasLastStep" @click="handleLast">上一步</el-button>
                <el-button type="primary" @click="save">确 定</el-button>
            </span>
        </el-dialog>
        <TemplateManagementDialog
            :visible.sync="modelManagementDialogVisible"
            :templateData="templateData"
            @success="updateTemplate"
        ></TemplateManagementDialog>
    </div>
</template>

<script>
import TemplateManagementDialog from './TemplateManagementDialog.vue';
import CONSTANS from 'dms/constant/dict.js';

const { typeData } = CONSTANS;
export default {
    name: 'DecompositionDialog',
    components: {
        TemplateManagementDialog
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        data: {
            type: Object,
            default: () => ({})
        },
        demandId: {
            type: String,
            default: ''
        },
        // 有无上一步，有代表是需求审核，没有代表是单纯的需求拆分
        hasLastStep: {
            type: Boolean,
            default: false
        },
        // 当行数据
        rowData: {
            type: Object,
            default: () => ({})
        },
        // 历史子需求列表
        historySubDemandData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            // 弹窗信息
            title: '需求审核',
            // 拆分出的子原始需求列表
            subOrDemandList: [],
            // 关联团队或项目选项
            groupOrProjectOptions: [],
            // 产品选项
            productOptions: [],
            addForm: {
                // 需求类型
                storyType: '',
                // 选择的模板
                template: ''
            },
            // 模板数据
            templateData: [],
            rules: [{ required: true, message: ' ', trigger: 'blur' }],
            typeData,
            modelManagementDialogVisible: false
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        // 需求状态
        auditStatus() {
            return this.hasLastStep ? '需求审核' : '需求拆分';
        },
        // 是否显示备注
        showNote() {
            return this.subOrDemandList.some((i) => i.isOverDate);
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.$nextTick(() => {
                    // 默认赋值原始需求的需求类型
                    this.addForm = {
                        storyType: this.rowData.demandType
                    };
                    this.getProductOrGroupOptions();
                    this.getProductOptions();
                    this.getTemplate();
                    // 默认加一条
                    this.addInterval();
                });
            }
        }
    },
    methods: {
        /**
         * 获取关联团队/项目下拉选项
         */
        async getProductOrGroupOptions() {
            const api = this.$service.dms.common.getProjectOrGroupList;
            const params = {};
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.groupOrProjectOptions = res.data.map((i) => ({
                    label: i.projectName,
                    value: i.projectId
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取产品下拉选项
         */
        async getProductOptions() {
            try {
                const params = {
                    statusList: ['进行中']
                };
                const api = this.$service.dms.common.getProductList;
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.productOptions = res.data.map((i) => ({
                    label: i.productName,
                    value: i.id,
                    ...i
                }));
            } catch (error) {
                console.error('获取产品选项失败:', error);
            }
        },
        // 处理产品选择变化
        handleProductChange(productId, row) {
            if (productId) {
                const selectedProduct = this.productOptions.find((item) => item.value === productId);
                row.productName = selectedProduct ? selectedProduct.label : '';
                row.productLine = selectedProduct ? selectedProduct.productLine : '';
            } else {
                // 清空时的处理
                row.productName = '';
            }
        },
        // 处理团队或项目选择变化
        handleProjectChange(projectId, row) {
            if (projectId) {
                const selectedProject = this.groupOrProjectOptions.find((item) => item.value === projectId);
                row.projectName = selectedProject ? selectedProject.label : '';
            } else {
                // 清空时的处理
                row.projectName = '';
            }
        },
        // 获取交付日期校验规则
        getDeliveryDateRules() {
            return [{ required: true, message: ' ', trigger: 'blur' }];
        },
        // 检查是否有日期晚于原始需求的情况
        checkDeliveryDateWarning() {
            const { expectedDate } = this.rowData;
            if (!expectedDate) return false;

            const expectedTime = new Date(expectedDate).getTime();
            return this.subOrDemandList.some((item) => {
                if (!item.deliveryDate) return false;
                const deliveryTime = new Date(item.deliveryDate).getTime();
                return deliveryTime > expectedTime;
            });
        },
        // 判断单个日期是否晚于期望日期
        isDateLaterThanExpected(deliveryDate) {
            if (!deliveryDate || !this.rowData.expectedDate) return false;

            const deliveryTime = new Date(deliveryDate).getTime();
            const expectedTime = new Date(this.rowData.expectedDate).getTime();
            return deliveryTime > expectedTime;
        },
        // 打开弹窗
        open() {
            this.dialogVisible = true;
            this.subOrDemandList = [];
        },
        // 创建需求
        addInterval() {
            if (!this.addForm.storyType) {
                this.$message.warning('请先选择需求类型');
                return;
            }
            this.subOrDemandList.push({
                storyType: this.addForm.storyType,
                productId: '',
                productName: '',
                projectId: '',
                projectName: '',
                owner: '',
                ownerName: '',
                storyName: this.rowData.demandName,
                estimateHour: null,
                deliveryDate: this.rowData.expectedDate,
                isOverDate: false
            });
        },
        deleteInterval(index) {
            this.subOrDemandList.splice(index, 1);
            // 清除表单验证状态
            this.$nextTick(() => {
                if (this.$refs.tableForm) {
                    this.$refs.tableForm.clearValidate();
                }
            });
        },
        save() {
            // 验证表格数据
            if (this.subOrDemandList.length === 0) {
                this.$message.warning('请至少添加一条需求数据');
                return;
            }
            this.$refs.tableForm.validate((valid) => {
                if (valid) {
                    this.proceedSave();
                } else {
                    this.$message.warning('请输入所有必填项');
                }
            });
        },
        // 执行保存操作
        proceedSave() {
            if (this.auditStatus === '需求拆分') {
                this.saveDecompositionData();
            } else {
                this.changeCheckStatus();
            }
        },
        /**
         * 拆分需求
         */
        async saveDecompositionData() {
            const params = {
                ...this.rowData,
                // 原始需求Id
                orDemandId: this.rowData.demandId,
                // 需求来源
                source: this.rowData.demandSource,
                subOrDemandList: this.subOrDemandList
            };

            const api = this.$service.dms.demand.demandDecomposition;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$emit('success');
                this.$message.success('保存成功');
                this.cancel();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 先变更审核状态，再进行拆分
         */
        async changeCheckStatus() {
            const api = this.$service.dms.demand.demandCheckStatusChange;
            const params = {
                storyId: this.demandId,
                storyVersion: this.rowData.changeTimes + 1,
                checkId: this.rowData.checkId,
                checkAction: '接受',
                reason: '原始需求已提出，经确认，接受此需求',
                checkType: this.rowData.checkType,
                status: '已接受'
            };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.saveDecompositionData();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        // 关闭弹窗
        cancel() {
            this.subOrDemandList = [];
            this.$refs.tableForm.resetFields();
            this.dialogVisible = false;
        },
        templateManagement() {
            this.modelManagementDialogVisible = true;
        },
        /**
         * 获取模板
         */
        async getTemplate() {
            const params = {
                module: 'custom',
                section: 'requirementProduct'
            };
            const api = this.$service.dms.common.getAllConfigInfo;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.templateData = res.data.map((i) => JSON.parse(i.value));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 选择模板之后
         * @param {String} id  模板id
         */
        handleTemplateChange(id) {
            const templateArr = this.templateData
                .find((i) => i.id === id)
                ?.relations.map((j) => ({
                    storyType: this.addForm.storyType,
                    productId: '',
                    productName: '',
                    projectId: '',
                    projectName: '',
                    owner: '',
                    ownerName: '',
                    storyName: this.rowData.demandName,
                    estimateHour: null,
                    deliveryDate: this.rowData.expectedDate,
                    isOverDate: false,
                    ...j
                }));

            this.subOrDemandList = [...this.subOrDemandList, ...templateArr];
        },
        handleLast() {
            this.cancel();
            this.$emit('lastStep');
        },
        updateTemplate() {
            this.addForm.template = '';
            this.getTemplate();
        },
        handleDateChange(date, row) {
            if (date > this.rowData.expectedDate) {
                row.isOverDate = true;
            } else {
                row.isOverDate = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    display: flex;
    gap: 4px;
    .split-title {
        font-size: 12px;
        align-self: flex-end;
    }
}
.flex {
    display: flex;
}
.box-condition {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.box-btn {
    height: 100%;
    display: flex;
    align-items: flex-end;
    margin-bottom: 18px;
}
.rotateButton {
    display: flex;
    justify-content: space-around;
    align-items: center;
    cursor: pointer;
}
.search-line {
    display: flex;
    justify-content: center;
    align-items: center;
    .model-btn {
        margin-left: 8px;
        margin-bottom: 18px;
    }
}
.unit {
    width: 50px;
}

.form {
    ::v-deep .el-table__row > td {
        padding: 8px 0px; // 统一增加表格行高，为错误信息预留空间
    }
    .el-form-item {
        margin-bottom: 0px;
    }

    ::v-deep .el-form-item__content {
        margin: 0 !important;
    }

    // 日期选择器警告样式
    .date-picker-wrapper {
        .date-warning {
            ::v-deep .el-input__inner {
                border-color: #e6a23c;
                background-color: #fdf6ec;
            }
        }

        .warning-text {
            top: 100%;
            left: 0;
            font-size: 12px;
            color: #e6a23c;
            padding-top: 2px;
            white-space: nowrap;
            z-index: 1000;
        }
    }
}
</style>
