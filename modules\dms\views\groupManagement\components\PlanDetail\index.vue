<!-- 计划管理——计划详情 -->
<template>
    <div>
        <!-- 基本信息 -->
        <formula-title :title="basicTitle"></formula-title>
        <plan-card :team-info="basicForm"></plan-card>

        <!--  需求任务 -->
        <formula-title :title="planTitle"></formula-title>
        <!-- <NestedPlanInfoList :list="demandList" :disabledAll="true"></NestedPlanInfoList> -->
        <NestedPlanInfoEvaluateList :list="demandList" :disabledAll="true"></NestedPlanInfoEvaluateList>

        <!--  团队活动 -->
        <formula-title :title="teamActivityTitle"></formula-title>
        <div class="team-activity-container">
            <TeamActivityInfo :list="teamTaskList"></TeamActivityInfo>
        </div>

        <!--  问题修复 -->
        <formula-title :title="problemFixTitle"></formula-title>
        <div class="problem-fix-container">
            <ProblemFixInfo :list="problemTaskList"></ProblemFixInfo>
        </div>

        <!--  工时负载率 -->
        <formula-title :title="worktimeTitle"></formula-title>
        <div class="chart-box">
            <el-card class="chart-right">
                <worktime-chart ref="worktimeRef" :info="workTimeChartInfo"></worktime-chart>
            </el-card>
        </div>
    </div>
</template>

<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';
import worktimeChart from 'dms/views/groupManagement/schedule/components/worktimeChart/index.vue';
import NestedPlanInfoEvaluateList from 'dms/views/groupManagement/schedule/plan-evalute-action/components/NestedPlanInfoEvaluateList.vue';
import TeamActivityInfo from './components/TeamActivityInfo.vue';
import ProblemFixInfo from './components/ProblemFixInfo.vue';
import planCard from 'dms/views/groupManagement/schedule/components/planCard.vue';

export default {
    name: 'PlanDetail',
    components: { formulaTitle, worktimeChart, planCard, NestedPlanInfoEvaluateList, TeamActivityInfo, ProblemFixInfo },
    props: {},
    data() {
        return {
            // 计划ID
            planId: '',
            // 标题
            basicTitle: '基本信息',
            planTitle: '需求任务',
            teamActivityTitle: '团队活动',
            worktimeTitle: '工时负载率',
            problemFixTitle: '问题修复',
            basicForm: {
                dateRange: [],
                planName: '',
                workDays: 0,
                teamName: '',
                teamLeader: '',
                submitDate: ''
            },
            // 团队活动列表
            teamTaskList: [],
            // 问题修复列表
            problemTaskList: [],
            demandList: [],
            // 工时负载率echarts图
            workTimeChartInfo: {}
        };
    },
    computed: {
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        },
        groupOptions() {
            return this.$store.state.dms.groupOption;
        }
    },
    async mounted() {
        // 获取计划ID
        if (this.$route?.query?.id) {
            this.planId = this.$route?.query?.id;
        } else {
            this.$message.error('缺少计划ID参数');
            this.$router.back();
            return;
        }

        this.getBaseInfo();
        this.getGroupMembers();
        this.getTableList();
        this.getResourceLoadData();
    },
    methods: {
        handleReturn() {
            this.$router.back();
        },
        /**
         * 获取基本信息
         */
        async getBaseInfo() {
            try {
                const params = {
                    id: this.planId
                };
                const api = this.$service.dms.plan.getPlanBaseInfo;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                const { startDate, endDate } = res.data;
                this.basicForm = { ...this.basicForm, ...res.data };
                this.basicForm.dateRange = [startDate, endDate];
            } catch (error) {
                console.error('查询基本信息失败:', error);
            }
        },
        /**
         * 获取团队下的人员列表
         */
        async getGroupMembers() {
            try {
                const params = {
                    planId: this.planId
                };
                const api = this.$service.dms.plan.getMemebersInGroup;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                const groupMembers = res.data.map((i) => ({ loginName: i.account, employeeName: i.name }));
                this.$store.dispatch('dms/setGroupMembers', groupMembers);
            } catch (error) {
                console.error('查询团队成员失败:', error);
            }
        },
        /**
         * 获取需求任务、团队活动、问题修复的表格数据
         */
        async getTableList() {
            try {
                const params = {
                    planId: this.planId
                };
                const api = this.$service.dms.plan.getPlanTaskList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.demandList = res.data.storyTaskList || [];
                this.teamTaskList = res.data.teamTaskList || [];
                this.problemTaskList = res.data.problemTaskList || [];
            } catch (error) {
                console.error('查询任务列表失败:', error);
            }
        },
        /**
         * 获取工时负载率数据
         */
        async getResourceLoadData() {
            try {
                const params = {
                    planId: this.planId
                };

                const api = this.$service.dms.plan.getPlanWorkHourChart;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }

                this.workTimeChartInfo = res.data;
                return Promise.resolve();
            } catch (error) {
                console.error('查询工时负载率数据失败:', error);
                return Promise.reject();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.chart-box {
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: space-between;
}
.clearfix {
    height: 30px;
}
.chart-right {
    width: 100%;
    height: 100%;
}
.el-card {
    padding: 10px !important;
}
::v-deep .el-card__header {
    height: 30px !important;
}
.load-rate-item {
    border-bottom: 1px solid #dcdfe6;
}

/* 基本信息样式 */
.basic-info-form {
    padding: 20px;
    background: #fff;
}

.basic-form {
    .el-form-item {
        margin-bottom: 20px;
    }
}

.plan-period-container {
    display: flex;
    align-items: center;
    gap: 20px;

    .period-options {
        .el-radio-group {
            .el-radio-button {
                margin-right: 0;
            }
        }
    }
}

.work-days-display {
    display: flex;
    align-items: center;

    .work-days-number {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
        margin-right: 4px;
    }

    .work-days-unit {
        color: #606266;
    }
}

/* 团队活动样式 */
.team-activity-container {
    margin-bottom: 20px;
}

/* 问题修复样式 */
.problem-fix-container {
    margin-bottom: 20px;
}

/* 禁用状态样式 */
::v-deep .el-date-editor.is-disabled {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
}

::v-deep .el-radio.is-disabled {
    .el-radio__input {
        cursor: not-allowed;
    }
    .el-radio__label {
        color: #c0c4cc;
        cursor: not-allowed;
    }
}
</style>
