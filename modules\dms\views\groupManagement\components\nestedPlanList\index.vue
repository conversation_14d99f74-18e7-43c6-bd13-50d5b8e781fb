<template>
    <div class="nest-table-container">
        <floating-ball :menuItems="menuItems"></floating-ball>
        <div class="relate-demand-button">
            <el-button type="primary" class="relate-demand" @click="handleRelateDemand">关联需求</el-button>
        </div>
        <nest-table
            ref="nestTable"
            :data="demands"
            :columns="demandColumns"
            :nested-columns="taskColumns"
            nested-data-key="taskList"
            @selection-change="handleSelectionChange"
        ></nest-table>
        <RelateDemandDialog
            :visible.sync="relateDemandDialogVisible"
            @confirm="handleRelateDemandSuccess"
        ></RelateDemandDialog>
        <AddMultipleTaskDialog
            :visible.sync="addMultipleTaskDialogVisible"
            @confirm="handleAddMultipleTaskSuccess"
            :assigneeList="groupMembers"
        ></AddMultipleTaskDialog>
    </div>
</template>

<script>
import NestTable from 'dms/components/NestTable/index.vue';
import FloatingBall from 'dms/components/FloatingBall.vue';
import dict from 'dms/constant/dict.js';
import { getUuid } from 'dms/mixins/common.js';
import RelateDemandDialog from './RelateDemandDialog.vue';
import AddMultipleTaskDialog from './AddMultipleTaskDialog.vue';

export default {
    name: 'NestedPlanList',
    components: { NestTable, FloatingBall, RelateDemandDialog, AddMultipleTaskDialog },
    props: {
        saveFlag: {
            type: Number,
            default: 0
        },
        list: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            // 选中的需求行
            selectedMainRows: [],
            // 选中的任务行
            selectedNestedRows: [],
            // 所有选中的行
            allSelectedRows: [],
            demandColumns: [
                { prop: 'projectName', label: '所属产品', width: 120, align: 'center' },
                { prop: 'storyName', label: '需求名称', minWidth: 120 },
                { prop: 'storyDesc', label: '需求描述', minWidth: 220 },
                {
                    prop: 'deliveryDate',
                    label: '计划完成日期',
                    width: 100,
                    align: 'center',
                    type: 'date'
                },
                {
                    prop: 'storyType',
                    label: '需求类型',
                    align: 'center'
                },
                {
                    prop: 'level',
                    label: '需求级别',
                    align: 'center'
                },
                {
                    prop: 'storyPri',
                    label: '优先级',
                    align: 'center'
                },
                {
                    prop: 'storyStatus',
                    label: '需求状态',
                    align: 'center'
                }
            ],
            menuItems: [
                { icon: 'dms-plan-add-multiply', title: '多人+多类型', handler: this.addMultipleTask },
                { icon: 'dms-plan-add-empty', title: '空白任务', handler: this.addEmptyTask },
                { type: 'delete', title: '删除', handler: this.deleteRow }
            ],
            // 关联需求弹窗
            relateDemandDialogVisible: false,
            // 批量创建任务弹窗
            addMultipleTaskDialogVisible: false
        };
    },
    computed: {
        demands: {
            get() {
                return this.list;
            },
            set(value) {
                this.$emit('update:list', value);
            }
        },
        groupMembers() {
            return this.$store.state.dms.groupMembers;
        },
        taskColumns() {
            return [
                {
                    prop: 'taskName',
                    label: '任务名称',
                    editable: true,
                    required: true
                },
                { prop: 'taskDesc', label: '任务描述', editable: true },
                {
                    prop: 'startDate',
                    label: '计划开始日期',
                    minWidth: 110,
                    align: 'center',
                    editable: true,
                    type: 'date',
                    required: true
                },
                {
                    prop: 'endDate',
                    label: '计划完成日期',
                    minWidth: 110,
                    align: 'center',
                    editable: true,
                    type: 'date',
                    required: true
                },
                {
                    prop: 'taskType',
                    label: '任务类型',
                    align: 'center',
                    editable: true,
                    type: 'select',
                    options: dict.taskTypeData,
                    required: true
                },
                {
                    prop: 'assignedTo',
                    label: '责任人',
                    align: 'center',
                    editable: true,
                    type: 'people',
                    options: this.groupMembers,
                    required: true
                },
                {
                    prop: 'estimate',
                    label: '预计工时',
                    align: 'center',
                    editable: true,
                    required: true,
                    type: 'number',
                    min: 0
                }
            ];
        }
    },
    watch: {
        saveFlag(newVal) {
            if (newVal) {
                this.$emit('success', this.demands);
            }
        }
    },
    methods: {
        handleRelateDemand() {
            this.relateDemandDialogVisible = true;
        },
        handleRelateDemandSuccess(value) {
            // 检查是否有重复的需求ID
            const existingIds = this.demands.map((demand) => demand.storyId);
            const duplicateItems = value.filter((item) => existingIds.includes(item.id));

            if (duplicateItems.length > 0) {
                const duplicateNames = duplicateItems.map((item) => item.storyName).join('、');
                this.$message.warning(`以下需求已存在，无法重复添加：${duplicateNames}`);
                return;
            }
            const newDemands = value.map((i) => ({
                storyId: i.id,
                projectName: i.product,
                storyName: i.storyName,
                storyDesc: i.description,
                deliveryDate: i.endDate,
                storyType: i.type,
                level: i.level,
                storyPri: i.priority,
                storyStatus: i.status,
                taskList: []
            }));
            const updatedList = [...this.demands, ...newDemands];
            this.$emit('update:list', updatedList);
        },
        handleAddMultipleTaskSuccess(result) {
            const updatedList = [...this.demands];
            // 遍历选中的需求行，为每个需求根据选择的任务类型和责任人创建任务
            this.selectedMainRows.forEach((selectedRow) => {
                const targetDemand = updatedList.find((demand) => demand.storyId === selectedRow.storyId);
                if (targetDemand) {
                    // 为每个任务类型和责任人的组合创建任务
                    result.taskTypes.forEach((taskType) => {
                        result.assignees.forEach((assignee) => {
                            const newTask = {
                                innerId: getUuid(),
                                innerType: 'add',
                                taskName: '',
                                taskDesc: '',
                                startDate: '',
                                endDate: '',
                                taskType,
                                assignedTo: assignee,
                                pri: '',
                                criticalTask: '否',
                                estimate: null
                            };

                            if (!targetDemand.taskList) {
                                targetDemand.taskList = [];
                            }
                            targetDemand.taskList.push(newTask);
                        });
                    });
                }
            });

            this.$emit('update:list', updatedList);
            this.$message.success(
                `成功创建了${result.taskTypes.length * result.assignees.length * this.selectedMainRows.length}个任务`
            );
        },
        handleSelectionChange(data) {
            this.selectedMainRows = data.mainRows || [];
            this.selectedNestedRows = data.nestedRows || [];
            this.allSelectedRows = data.allSelected || [];
        },
        addMultipleTask() {
            // 只有选择需求行才有效
            if (!this.selectedMainRows || this.selectedMainRows.length === 0) {
                this.$message.warning('请先选择需求行！');
                return;
            }

            // 显示批量创建任务弹窗
            this.addMultipleTaskDialogVisible = true;
        },
        addEmptyTask() {
            // 只有选择需求行才有效
            if (!this.selectedMainRows || this.selectedMainRows.length === 0) {
                this.$message.warning('请先选择需求行！');
                return;
            }

            const updatedList = [...this.demands];
            // 遍历选中的需求行，为每个需求添加空任务
            this.selectedMainRows.forEach((selectedRow) => {
                const targetDemand = updatedList.find((demand) => demand.storyId === selectedRow.storyId);

                if (!targetDemand) return;

                const newEmptyTask = {
                    innerId: getUuid(),
                    innerType: 'add',
                    taskName: '',
                    taskDesc: '',
                    startDate: '',
                    endDate: '',
                    taskType: '',
                    assignedTo: '',
                    criticalTask: '否',
                    estimate: null
                };
                if (!targetDemand.taskList) {
                    targetDemand.taskList = [];
                }
                targetDemand.taskList.push(newEmptyTask);
            });
            this.$emit('update:list', updatedList);
        },
        async deleteRow() {
            // 检查是否有选中的行
            const hasMainSelection = this.selectedMainRows.length > 0;
            const hasNestedSelection = this.selectedNestedRows.length > 0;

            if (!hasMainSelection && !hasNestedSelection) {
                this.$message.warning('请先选择要删除的行！');
                return;
            }

            // 构建确认消息
            let confirmMessage = '确定要删除选中的';
            if (hasMainSelection && hasNestedSelection) {
                confirmMessage += `${this.selectedMainRows.length}个需求和${this.selectedNestedRows.length}个任务吗？`;
            } else if (hasMainSelection) {
                confirmMessage += `${this.selectedMainRows.length}个需求吗？`;
            } else {
                confirmMessage += `${this.selectedNestedRows.length}个任务吗？`;
            }

            await this.$confirm(confirmMessage, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            });

            let deletedCount = 0;

            const updatedList = [...this.demands];

            // 删除选中的需求行
            if (hasMainSelection) {
                this.selectedMainRows.forEach((selectedRow) => {
                    const demandIndex = updatedList.findIndex((demand) => demand.storyId === selectedRow.storyId);
                    if (demandIndex !== -1) {
                        updatedList.splice(demandIndex, 1);
                        deletedCount += 1;
                    }
                });
            }

            // 删除选中的任务行
            if (hasNestedSelection) {
                this.selectedNestedRows.forEach((selectedRow) => {
                    if (this.deleteTaskFromList(updatedList, selectedRow)) {
                        deletedCount += 1;
                    }
                });
            }

            this.$emit('update:list', updatedList);

            // 清空选中状态
            this.$refs.nestTable.clearSelection();
            this.selectedMainRows = [];
            this.selectedNestedRows = [];
            this.allSelectedRows = [];

            this.$message.success(`删除成功！共删除了${deletedCount}条记录`);
        },
        deleteTaskFromList(demandList, taskRow) {
            for (const demand of demandList) {
                if (demand.taskList && demand.taskList.length > 0) {
                    const taskIndex = demand.taskList.findIndex((task) => task.id === taskRow.id);
                    if (taskIndex !== -1) {
                        demand.taskList.splice(taskIndex, 1);
                        // 返回true表示删除成功
                        return true;
                    }
                }
            }
            return false;
        }
    }
};
</script>

<style lang="scss" scoped>
.relate-demand-button {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 5px;
}
</style>
