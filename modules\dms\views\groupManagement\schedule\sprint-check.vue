<!-- 冲刺评价-审核 -->
<template>
    <div class="view">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button type="primary">提交</el-button>
            <el-button type="primary">返回</el-button>
        </div>
        <div>
            <!-- 基本信息 -->
            <formula-title :title="basicTitle"></formula-title>
            <plan-card :teamInfo="teamInfo"></plan-card>
            <!--  计划完详情 -->
            <formula-title :title="planTitle"></formula-title>
            <!--  自评 -->
            <formula-title :title="informationTitle"></formula-title>
            <el-form :model="informationForm" ref="informationForm" label-width="100px">
                <el-form-item label="冲刺结果">
                    <el-radio-group v-model="informationForm.type">
                        <el-radio :label="0">成功</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="说明" prop="desc">
                    <el-input type="textarea" v-model="informationForm.desc" maxlength="500" :rows="3"></el-input>
                </el-form-item>
            </el-form>
            <!-- 审核 -->
            <formula-title :title="evaluationTitle"></formula-title>
            <el-form :model="ruleForm" ref="ruleForm" label-width="100px">
                <el-form-item label="冲刺结果">
                    <el-radio-group v-model="ruleForm.type">
                        <el-radio :label="0">成功</el-radio>
                        <el-radio :label="1">失败</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="审批意见" prop="desc">
                    <el-input
                        type="textarea"
                        v-model="ruleForm.desc"
                        placeholder="请填写您的审批意见或理由"
                        maxlength="500"
                        :rows="3"
                    ></el-input>
                </el-form-item>
            </el-form>
            <formula-title :title="recordTitle"></formula-title>
            <operation-record :collapseItems="collapseItems"></operation-record>
        </div>
    </div>
</template>

<script>
import formulaTitle from './components/formulaTitle.vue';
import planCard from './components/planCard.vue';
import operationRecord from './components/operationRecord.vue';

export default {
    components: { formulaTitle, planCard, operationRecord },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            basicTitle: '基本信息',
            informationTitle: '自评',
            planTitle: '计划详情',
            evaluationTitle: '审核',
            recordTitle: '操作纪录',
            // 基本信息数据
            teamInfo: {
                teamName: '自动化分拣前端团队',
                teamLeader: '王鑫',
                planName: '自动化分拣前端团队(25.6.6-6.30)',
                planCycle: '2025-06-06 至 2025-06-30',
                availableWorkDays: '20 天',
                version: 'V2',
                submitDate: '2025-06-12'
            },
            loadRateData: {
                over120: {
                    label: '负载率 > 120%',
                    count: 2,
                    persons: '王五、李四'
                },
                under80: {
                    label: '负载率 < 80%',
                    count: 1,
                    persons: '于淼'
                }
            },
            rawData: [
                { name: '于淼', teamTime: 70, externalTime: 10, idleTime: 20, theory: 100 },
                { name: '钟振国', teamTime: 75, externalTime: 20, idleTime: 5, theory: 100 },
                { name: '王云鹏', teamTime: 80, externalTime: 20, idleTime: 0, theory: 100 },
                { name: '张三', teamTime: 100, externalTime: 0, idleTime: 0, theory: 100 },
                { name: '李四', teamTime: 90, externalTime: 20, idleTime: 0, theory: 100 },
                { name: '王五', teamTime: 120, externalTime: 0, idleTime: 0, theory: 100 },
                { name: '于淼', teamTime: 70, externalTime: 10, idleTime: 20, theory: 100 },
                { name: '钟振国', teamTime: 75, externalTime: 20, idleTime: 5, theory: 100 },
                { name: '王云鹏', teamTime: 80, externalTime: 20, idleTime: 0, theory: 100 },
                { name: '张三', teamTime: 100, externalTime: 0, idleTime: 0, theory: 100 },
                { name: '李四', teamTime: 90, externalTime: 20, idleTime: 0, theory: 100 },
                { name: '王五', teamTime: 120, externalTime: 0, idleTime: 0, theory: 100 }
            ],
            informationForm: {
                type: 0,
                desc: ''
            },
            ruleForm: {
                type: 0,
                desc: ''
            },
            collapseItems: [
                {
                    name: '1',
                    date: '2025-06-17 17:27:16',
                    operator: '钟振国',
                    status: '创建',
                    opinion: '创建任务初始记录',
                    concept1: '修改内容：最初预计、预计剩余从 12 改为 16',
                    concept2: '版本从 1 改为 2；',
                    concept3: '版本从 2 改为 3；',
                    concept4: '版本从 2 改为 3；'
                },
                {
                    name: '2',
                    date: '2025-06-17 17:35:00',
                    operator: '钟振国',
                    status: '编辑',
                    opinion: '调整任务相关参数及描述',
                    concept1: '修改内容：最初预计、预计剩余从 12 改为 16',
                    concept2: '版本从 1 改为 2；',
                    concept3: '版本从 2 改为 3；',
                    concept4: '版本从 2 改为 3；'
                },
                {
                    name: '3',
                    date: '2025-06-18 16:41:34',
                    operator: '于淼',
                    status: '启动',
                    opinion: '启动计划管理页面开发任务',
                    concept1: '修改内容：最初预计、预计剩余从 12 改为 16',
                    concept2: '版本从 1 改为 2；',
                    concept3: '版本从 2 改为 3；',
                    concept4: '版本从 2 改为 3；'
                },
                {
                    name: '4',
                    date: '2025-06-19 19:06:11',
                    operator: '钟振国',
                    status: '编辑',
                    opinion: '调整任务截止日期和版本'
                }
            ]
        };
    },
    mounted() {
        this.$refs.worktimeRef.twoChart(this.rawData);
    },
    methods: {}
};
</script>

<style lang="scss" scoped>
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
.chart-box {
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: space-between;
}
.chart-left {
    width: 30%;
    height: 100%;
}
.clearfix {
    height: 30px;
}
.chart-right {
    width: 69%;
    height: 100%;
}
.el-card {
    padding: 10px !important;
}
::v-deep .el-card__header {
    height: 30px !important;
}
.load-rate-item {
    border-bottom: 1px solid #dcdfe6;
}
::v-deep .el-tabs__content {
    padding: 3px 0 0 0;
}
</style>
