<template>
    <div class="add-product-container">
        <!-- 页签菜单 -->
        <el-tabs v-model="activeIndex" type="card" :before-leave="handleTabSelect">
            <el-tab-pane label="基本信息" name="baseInfo">
                <div style="padding: 20px">
                    <formula-title :title="dynamicTitle"></formula-title>
                    <el-form :model="addForm" ref="dataForm" label-width="140px" :rules="userRules">
                        <div class="add-flex">
                            <el-form-item label="产品名称" prop="productName">
                                <el-input
                                    v-model="addForm.productName"
                                    placeholder="请输入产品名称"
                                    clearable
                                    maxlength="64"
                                    style="width: 300px"
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="产品编号" prop="productCode">
                                <el-input
                                    v-model="addForm.productCode"
                                    placeholder="请输入产品编号"
                                    clearable
                                    maxlength="64"
                                    style="width: 300px"
                                ></el-input>
                            </el-form-item>
                        </div>
                        <div class="add-flex">
                            <el-form-item label="产品线" prop="productLine">
                                <el-select
                                    v-model="addForm.productLine"
                                    placeholder="请选择产品线"
                                    clearable
                                    style="width: 300px"
                                >
                                    <el-option
                                        v-for="item in productLineData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="Product Owner" prop="productOwner">
                                <PeopleSelector
                                    v-model="addForm.productOwner"
                                    placeholder="请选择Product Owner"
                                    :isMultipled="false"
                                    style="width: 300px"
                                />
                            </el-form-item>
                        </div>
                        <el-form-item label="目标用户" prop="targetUsers">
                            <el-input
                                type="textarea"
                                v-model="addForm.targetUsers"
                                maxlength="500"
                                :rows="4"
                                placeholder="建议参考的模板：<行业/身份>中面临<具体痛点>的<角色>"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="产品定位" prop="productPosition">
                            <el-input
                                type="textarea"
                                v-model="addForm.productPosition"
                                placeholder="建议参考的模板：针对<细分市场>的<产品类型>，以<差异化特性>区别于<竞品类别>"
                                maxlength="500"
                                :rows="4"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="核心价值" prop="coreValue">
                            <el-input
                                type="textarea"
                                v-model="addForm.coreValue"
                                placeholder="建议参考的模板：功能价值：通过<核心技术/功能>实现<用户获益>，如<使用场景>。情感价值：帮助用户<心理诉求>。量化价值：将<原有指标>从X提升至Y。"
                                maxlength="500"
                                :rows="4"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="竞争策略" prop="competitiveStrategy">
                            <el-input
                                type="textarea"
                                v-model="addForm.competitiveStrategy"
                                placeholder="建议参考的模板：差异化路径：在<维度>上超越竞品或保持同等水平（如：<具体指标>）。防御策略：通过<壁垒类型>构建护城河，如<专利/生态等>。替代方案转化：针对<竞品用户>的<迁移诱因>。"
                                maxlength="500"
                                :rows="4"
                            ></el-input>
                        </el-form-item>
                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane label="产品结构" name="productStructure">
                <div class="product-structure-container">
                    <DraggableTree
                        :data="rootNode"
                        :isTeamLeader="isTeamLeader"
                        :productName="productName"
                        @add-node="handleTreeDataChange"
                        @remove-node="handleTreeDataChange"
                        @node-drop="handleTreeDataChange"
                        @label-change="handleTreeDataChange"
                        style="width: 600px"
                    ></DraggableTree>
                </div>
            </el-tab-pane>
        </el-tabs>
        <!-- 按钮 -->
        <div class="add-btn">
            <el-button type="primary" @click="confirm()">发布</el-button>
            <el-button @click="handleBack()">返回</el-button>
        </div>
    </div>
</template>
<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';
import DraggableTree from 'dms/views/productManagement/components/DraggableTree.vue';
import PeopleSelector from 'dms/components/PeopleSelector';

export default {
    name: 'EditProduct',
    components: { formulaTitle, DraggableTree, PeopleSelector },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            activeIndex: 'baseInfo',
            addForm: {
                productName: '',
                productCode: '',
                productLine: '',
                productOwner: '',
                targetUsers: '',
                productPosition: '',
                coreValue: '',
                competitiveStrategy: ''
            },
            productLineData: [],
            userRules: {
                productName: [
                    {
                        required: true,
                        message: '请输入产品名称',
                        trigger: 'blur'
                    }
                ],
                productCode: [
                    {
                        required: true,
                        message: '请输入产品编号',
                        trigger: 'blur'
                    }
                ],
                productLine: [
                    {
                        required: true,
                        message: '请选择产品线',
                        trigger: 'change'
                    }
                ],
                productOwner: [
                    {
                        required: true,
                        message: '请选择Product Owner',
                        trigger: 'change'
                    }
                ],
                targetUsers: [
                    {
                        required: true,
                        message: '请输入目标用户',
                        trigger: 'blur'
                    }
                ],
                productPosition: [
                    {
                        required: true,
                        message: '请输入产品定位',
                        trigger: 'blur'
                    }
                ],
                coreValue: [
                    {
                        required: true,
                        message: '请输入核心价值',
                        trigger: 'blur'
                    }
                ]
            },
            // 产品ID，新增时使用
            type: '',
            productId: '',
            productName: '',
            checkId: '',
            // 是否是团队leader
            isTeamLeader: false,
            // 产品结构数据
            productChildren: []
        };
    },
    computed: {
        dynamicTitle() {
            return '编辑产品';
        },
        // 根节点（产品节点）
        rootNode() {
            return [
                {
                    productId: this.productId,
                    moduleName: this.productName,
                    type: 'product',
                    moduleType: 'P',
                    sort: 1,
                    children: this.productChildren
                }
            ];
        }
    },
    created() {
        this.type = this.$route.query.type;
        this.productId = this.$route.query.productId;
        this.checkId = this.$route.query.checkId;
        this.productName = this.$route.query.productName;
        this.queryData();
        this.$nextTick(() => {
            if (this.type === 'edit') {
                this.getProductInfo();
                this.getProductTreedata();
            }
            if (this.$refs.dataForm) {
                this.$refs.dataForm.resetFields();
            }
        });
    },
    methods: {
        async getProductInfo() {
            try {
                const params = {
                    productId: this.productId
                };
                const res = await this.$service.dms.product.getProductInfo(params);
                if (res.code === '0000') {
                    this.addForm = res.data;
                } else {
                    this.$message.error(res.message || '获取产品详情失败');
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        },
        async handleTabSelect(activeName) {
            if (activeName === 'baseInfo') {
                return Promise.resolve();
            }
            const valid = await this.validateForm();
            if (!valid) {
                this.$message.warning('请填写完整的基本信息');
                return Promise.reject();
            }
            return Promise.resolve();
        },
        // 查询产品线
        async queryData() {
            try {
                const res = await this.$service.dms.product.getProductLine();
                if (res.code !== '0000') {
                    this.$tools.message.err(res.message || '系统异常');
                    return;
                }
                this.productLineData = res.data.map((item) => {
                    return {
                        value: item,
                        label: item
                    };
                });
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 返回上一页
        handleBack() {
            this.$router.back();
        },
        // 发布
        async confirm() {
            const valid = await this.validateForm();
            if (!valid) return;
            try {
                const params = { ...this.productChildren, ...this.addForm };
                const api =
                    this.type === 'add' ? this.$service.dms.product.addProduct : this.$service.dms.product.editProduct;
                const res = await api(params);

                if (res.code === '0000') {
                    this.$message.success(res.message);
                    this.$router.back();
                } else {
                    this.$message.error(res.message || '发布失败，请重试');
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        },
        async validateForm() {
            return new Promise((resolve) => {
                this.$refs.dataForm.validate((valid) => {
                    resolve(valid);
                });
            });
        },
        /**
         * 获取产品结构数据
         * @param {Object} param0 产品结构数据
         */
        handleTreeDataChange({ treeData }) {
            // 只保存根节点下的子节点
            this.productChildren = treeData[0]?.children || [];
        },
        /**
         * 获取产品结构数据
         */
        async getProductTreedata() {
            try {
                const params = { productId: this.productId };
                const res = await this.$service.dms.product.getProductModuleInfo(params);
                if (res.code !== '0000') {
                    this.$tools.message.err(res.message || '系统异常');
                    this.productChildren = [];
                    return;
                }
                // 将接口返回的子节点数据赋值给productChildren
                this.productChildren = res.data || [];
            } catch (error) {
                console.error('获取产品结构失败', error);
                this.$tools.message.err('系统异常');
                this.productChildren = [];
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.add-flex {
    width: 100%;
    display: flex;
    justify-content: flex-start;
}
.add-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1;
}
.add-product-container {
    position: relative;
    ::v-deep .el-tabs__nav-wrap {
        display: flex;
        justify-content: flex-end;
        margin: 1vh 0 1vh 10px;
        .el-tabs__item {
            background-color: #fff;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
            color: #4377ee;
        }
        .el-tabs__item.is-active {
            background-color: #4377ee;
            color: #fff;
        }
    }
    ::v-deep .el-tabs--card > .el-tabs__header {
        border-bottom: 3px solid #4377ee;
    }
    ::v-deep .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
        border: none;
    }
}
.product-structure-container {
    margin-left: 30px;
}
</style>
