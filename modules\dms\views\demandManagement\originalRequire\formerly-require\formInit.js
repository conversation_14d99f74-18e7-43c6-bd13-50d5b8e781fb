import CommonItems from 'snbcCommon/common/form-items.js';
import dictData from 'dms/constant/dict.js';

const { select, input, dateRange, numberRange, peopleSelector, productLineSelector, number } = CommonItems;

// 需求名称
const demandName = {
    ...input,
    name: '需求名称',
    modelKey: 'demandName'
};

// 需求ID
const demandId = {
    ...input,
    name: '需求ID',
    modelKey: 'demandId'
};

// 需求来源
const demandSource = {
    ...select,
    name: '需求来源',
    modelKey: 'demandSource',
    elOptions: dictData.demandSourceData
};

// 来源编号
const sourceNo = {
    ...input,
    name: '来源编号',
    modelKey: 'sourceNo'
};

// 需求类型
const demandType = {
    ...select,
    name: '需求类型',
    modelKey: 'demandType',
    elOptions: dictData.typeData
};

// 优先级
const priority = {
    ...select,
    name: '优先级',
    modelKey: 'priority',
    elOptions: dictData.priorityData
};

// 需求状态
const demandStatus = {
    ...select,
    name: '需求状态',
    modelKey: 'demandStatus',
    elOptions: dictData.originalDemandStatus
};

// 产品线
const productLine = {
    ...productLineSelector,
    name: '产品线',
    modelKey: 'productLine'
};

// 产品
const assProductId = {
    ...select,
    name: '产品',
    modelKey: 'assProductId',
    elOptions: []
};

// 团队/项目
const assProjectId = {
    ...select,
    name: '团队/项目',
    modelKey: 'assProjectId',
    elOptions: []
};

// 需求提出人
const proposer = {
    ...peopleSelector,
    name: '需求提出人',
    modelKey: 'proposer',
    elSelectAttrs: {
        size: 'small',
        isMultipled: false
    }
};

// 需求提出时间
const proposalTime = {
    ...dateRange,
    name: '需求提出时间',
    modelKey: 'proposalTime'
};

// 需求负责人
const responsiblePerson = {
    ...peopleSelector,
    name: '需求负责人',
    modelKey: 'responsiblePerson',
    elSelectAttrs: {
        size: 'small',
        isMultipled: false
    }
};

// 期望交付日期
const expectedDate = {
    ...dateRange,
    name: '期望交付日期',
    modelKey: 'expectedDate'
};

// 变更次数
const changeTimes = {
    ...number,
    name: '变更次数',
    modelKey: 'changeTimes',
    elInputNumberAttrs: {
        controls: false,
        min: 0,
        class: 'change-time-input'
    }
};

// 需求进度
const demandProgress = {
    ...select,
    name: '需求进度',
    modelKey: 'demandProgress',
    elOptions: dictData.demandProgressData
};

// 成本偏差
const costDeviation = {
    ...numberRange,
    name: '成本偏差',
    modelKey: 'costDeviation',
    elInputNumberAttrs: {
        placeholder: '请输入'
    }
};

// 预估工时
const estimateHour = {
    ...numberRange,
    name: '预估工时',
    modelKey: 'estimateHour',
    elInputNumberAttrs: {
        placeholder: '请输入'
    }
};

// 实际工时
const actualHour = {
    ...numberRange,
    name: '实际工时',
    modelKey: 'actualHour',
    elInputNumberAttrs: {
        placeholder: '请输入'
    }
};

// 部署情况
const deploy = {
    ...select,
    name: '部署情况',
    modelKey: 'deploy',
    elOptions: dictData.depolyStituation
};

// 发布情况
const publish = {
    ...select,
    name: '发布情况',
    modelKey: 'publish',
    elOptions: dictData.publistSituation
};

// 查询参数初始化
export const queryParams = {
    demandName: '',
    demandId: '',
    demandSource: '',
    sourceNo: '',
    demandType: '',
    priority: '',
    demandStatus: '',
    productLine: '',
    assProductId: '',
    assProjectId: '',
    proposer: '',
    proposalTime: [],
    responsiblePerson: '',
    expectedDate: [],
    changeTimes: undefined,
    demandProgress: '',
    costDeviation: [],
    estimateHour: [],
    actualHour: [],
    deploy: '',
    publish: ''
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [
        demandName,
        demandId,
        demandSource,
        sourceNo,
        demandType,
        priority,
        demandStatus,
        productLine,
        assProductId,
        assProjectId,
        proposer,
        proposalTime,
        responsiblePerson,
        expectedDate,
        changeTimes,
        demandProgress,
        costDeviation,
        estimateHour,
        actualHour,
        deploy,
        publish
    ]
};
export const navItems = [
    { field: '', name: '所有', queryField: 'demandStatus' },
    {
        field: '待审核',
        name: '待审核',
        queryField: 'demandStatus'
    },
    {
        field: '未开始',
        name: '未开始',
        queryField: 'demandStatus'
    },
    {
        field: '已计划',
        name: '已计划',
        queryField: 'demandStatus'
    },
    {
        field: '进行中',
        name: '进行中',
        queryField: 'demandStatus'
    },
    {
        field: '已完成',
        name: '已完成',
        queryField: 'demandStatus'
    },
    {
        field: '已关闭',
        name: '已关闭',
        queryField: 'demandStatus'
    }
];
