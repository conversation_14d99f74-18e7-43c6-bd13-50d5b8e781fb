/**
 * 模块store都写在这里,框架会自动注册为模块store
 *
 * 使用方式：this.$store.dms.xxx
 */

const state = {
    department: [],
    departmentOption: [],
    project: '',
    projectOption: [],
    group: '',
    groupOption: {},
    // 根据部门获取的人员列表
    departmentEmployeeList: [],
    // 所有员工列表
    allEmployeeList: [],
    // 当前员工列表
    currentEmployeeList: [],
    // 根据部门ID查询过的人员
    employeeQueried: false,
    // 是否查过所有人员
    allEmployeeQueried: false,
    resourceName: '',
    // 团队成员列表
    groupMembers: []
};

const mutations = {
    SET_DEPARTMENT(state, department) {
        state.department = department;
    },
    SET_DEPARTMENT_OPTION(state, departmentOption) {
        state.departmentOption = departmentOption;
    },
    SET_PROJECT(state, project) {
        state.project = project;
    },
    SET_PROJECT_OPTION(state, projectOption) {
        state.projectOption = projectOption;
    },
    SET_GROUP(state, group) {
        state.group = group;
    },
    SET_GROUP_OPTION(state, groupOption) {
        state.groupOption = groupOption;
    },
    CHANGE_DEPARTMENT_EMPLOYEE_LIST(state, departmentEmployeeList) {
        state.departmentEmployeeList = departmentEmployeeList;
    },
    CHANGE_EMPLOYEE_QUERIED(state, queried) {
        state.employeeQueried = queried;
    },
    CHANGE_ALL_EMPLOYEE_QUERIED(state, queried) {
        state.allEmployeeQueried = queried;
    },
    CHANGE_ALL_EMPLOYEE_LIST(state, allEmployeeList) {
        state.allEmployeeList = allEmployeeList;
    },
    CHANGE_CURRENT_EMPLOYEE_LIST(state, currentEmployeeList) {
        state.currentEmployeeList = currentEmployeeList;
    },
    SET_RESOURCE_NAME(state, resourceName) {
        state.resourceName = resourceName;
    },
    SET_GROUP_MEMBERS(state, groupMembers) {
        state.groupMembers = groupMembers;
    }
};

const actions = {
    setDepartment({ commit }, department) {
        commit('SET_DEPARTMENT', department);
    },
    setDepartmentOption({ commit }, departmentOption) {
        commit('SET_DEPARTMENT_OPTION', departmentOption);
    },
    setProject({ commit }, project) {
        commit('SET_PROJECT', project);
    },
    setProjectOption({ commit }, projectOption) {
        commit('SET_PROJECT_OPTION', projectOption);
    },
    setGroup({ commit }, group) {
        commit('SET_GROUP', group);
    },
    setGroupOption({ commit }, groupOption) {
        commit('SET_GROUP_OPTION', groupOption);
    },
    changeDepartmentEmployeeList({ commit }, queried) {
        commit('CHANGE_DEPARTMENT_EMPLOYEE_LIST', queried);
    },
    changeEmployeeQueried({ commit }, queried) {
        commit('CHANGE_EMPLOYEE_QUERIED', queried);
    },
    changeAllEmployeeQueried({ commit }, queried) {
        commit('CHANGE_ALL_EMPLOYEE_QUERIED', queried);
    },
    changeAllEmployeeList({ commit }, allEmployeeList) {
        commit('CHANGE_ALL_EMPLOYEE_LIST', allEmployeeList);
    },
    changeCurrentEmployeeList({ commit }, currentEmployeeList) {
        commit('CHANGE_CURRENT_EMPLOYEE_LIST', currentEmployeeList);
    },
    setResourceName({ commit }, resourceName) {
        commit('SET_RESOURCE_NAME', resourceName);
    },
    setGroupMembers({ commit }, groupMembers) {
        commit('SET_GROUP_MEMBERS', groupMembers);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
