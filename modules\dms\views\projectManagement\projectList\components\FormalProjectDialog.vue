<template>
    <div>
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="85%" top="5vh" :before-close="closeDialog">
            <div class="project-container">
                <el-upload
                    class="import-button"
                    v-if="!isDisabled"
                    :action="url"
                    :show-file-list="false"
                    :on-success="handleUploadSuccess"
                    :on-error="handleUploadError"
                    :before-upload="validateExcelFile"
                    :auto-upload="true"
                    name="file"
                    :headers="headers"
                >
                    <el-button type="primary">导入项目设计任务书</el-button>
                </el-upload>

                <el-form ref="form" :model="form" :rules="rules" label-width="130px" class="form">
                    <!-- 基本信息 -->
                    <div class="title">基本信息</div>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="项目名称" prop="projectName">
                                <el-input
                                    v-model="form.projectName"
                                    placeholder="请输入项目名称"
                                    clearable
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="项目经理" prop="pmAccount">
                                <PeopleSelector
                                    v-model="form.pmAccount"
                                    placeholder="请选择项目经理"
                                    :is-multipled="false"
                                    ref="projectManagerRef"
                                    clearable
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="PPQA" prop="pqaAccount">
                                <PeopleSelector
                                    v-model="form.pqaAccount"
                                    placeholder="请选择PPQA"
                                    :is-multipled="false"
                                    clearable
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="开始时间" prop="startDate">
                                <el-date-picker
                                    v-model="form.startDate"
                                    type="date"
                                    placeholder="请选择开始时间"
                                    value-format="yyyy-MM-dd"
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="计划结束时间" prop="planEndDate">
                                <el-date-picker
                                    v-model="form.planEndDate"
                                    type="date"
                                    placeholder="请选择计划结束时间"
                                    value-format="yyyy-MM-dd"
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="实际结束时间" prop="endDate">
                                <el-date-picker
                                    v-model="form.endDate"
                                    type="date"
                                    placeholder="请选择实际结束时间"
                                    value-format="yyyy-MM-dd"
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="项目状态" prop="projectStatus">
                                <el-select
                                    v-model="form.projectStatus"
                                    type="date"
                                    placeholder="请选择项目状态"
                                    value-format="yyyy-MM-dd"
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in dictData.projectStatusData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 关联信息 -->
                    <div class="title">关联信息</div>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="项目归属" prop="projectAttribution">
                                <el-select
                                    v-model="form.projectAttribution"
                                    placeholder="请选择"
                                    clearable
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in projectAttributionOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="所属部门" prop="orgCode">
                                <DepartmentSelector
                                    style="width: 100%"
                                    v-model="form.orgCode"
                                    :disabled="isDisabled"
                                ></DepartmentSelector>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="产品线" prop="productLine">
                                <el-select
                                    v-model="form.productLine"
                                    placeholder="请选择"
                                    clearable
                                    style="width: 100%"
                                    @change="handleProductLineChange"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in productLineOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="产品经理" prop="productManager">
                                <PeopleSelector
                                    v-model="form.productManager"
                                    placeholder="请输入产品经理"
                                    clearable
                                    :is-multipled="false"
                                    ref="productManagerRef"
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="16">
                            <el-form-item label="涉及产品" prop="products">
                                <el-select
                                    v-model="form.products"
                                    placeholder="请选择"
                                    multiple
                                    clearable
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in productOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <!-- 其他信息 -->
                    <div class="title">其他信息</div>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="项目来源" prop="projectOrigin">
                                <el-select
                                    v-model="form.projectOrigin"
                                    placeholder="请选择"
                                    clearable
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in projectOriginOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="项目类型" prop="projectPlanType">
                                <el-select
                                    v-model="form.projectPlanType"
                                    placeholder="请选择"
                                    clearable
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in projectPlanTypeOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="项目立项类型" prop="projectApproval">
                                <el-select
                                    v-model="form.projectApproval"
                                    placeholder="请选择"
                                    clearable
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in projectApprovalOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="项目规模" prop="groupScale">
                                <el-select
                                    v-model="form.groupScale"
                                    placeholder="请选择"
                                    clearable
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in groupScaleOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="项目难度等级" prop="projectScale">
                                <el-select
                                    v-model="form.projectScale"
                                    placeholder="请选择"
                                    clearable
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in dictData.projectDifficultyLevelData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="公司立项等级" prop="approvalGrade">
                                <el-select
                                    v-model="form.approvalGrade"
                                    placeholder="请选择"
                                    clearable
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in approvalGradeOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="项目级别" prop="projectLevel">
                                <el-select
                                    v-model="form.projectLevel"
                                    placeholder="请选择"
                                    clearable
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="item in dictData.projectLevelData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="项目编号" prop="projectNumber">
                                <el-input
                                    v-model="form.projectNumber"
                                    placeholder="请输入项目编号"
                                    clearable
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 公司立项项目信息 -->
                    <div class="title">公司立项项目信息</div>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="公司立项项目名称" prop="companyProjectName">
                                <el-input
                                    placeholder="请选择公司立项项目"
                                    @click.native="handleCompanyProjectClick"
                                    @clear="handleCompanyProjectClear"
                                    v-model="form.companyProjectName"
                                    :clearable="true"
                                    readonly
                                >
                                </el-input>
                            </el-form-item>
                            <CompanyProjectSelectorModal
                                ref="CompanyProjectSelectorModalRef"
                                @getProjectInfo="getCompanyProjectInfo"
                            ></CompanyProjectSelectorModal>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="立项决议函名称" prop="marketDecisionName">
                                <el-input
                                    v-model="form.marketDecisionName"
                                    placeholder="请输入立项决议函名称"
                                    clearable
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="立项时间" prop="marketDecisionAcceptDate">
                                <el-date-picker
                                    v-model="form.marketDecisionAcceptDate"
                                    type="date"
                                    placeholder="请选择立项时间"
                                    value-format="yyyy-MM-dd"
                                    style="width: 100%"
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <!-- 硬件项目团队信息 -->
                    <div class="title">硬件项目团队信息</div>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="硬件产品经理" prop="hardwareProductManager">
                                <el-input
                                    v-model="form.hardwareProductManager"
                                    placeholder="请输入硬件产品经理"
                                    clearable
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="硬件项目经理" prop="hardwareProjectManager">
                                <el-input
                                    v-model="form.hardwareProjectManager"
                                    placeholder="请输入硬件项目经理"
                                    clearable
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="硬件团队代表" prop="hardwareGroup">
                                <el-input
                                    v-model="form.hardwareGroup"
                                    placeholder="请输入硬件团队代表"
                                    clearable
                                    :disabled="isDisabled"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <!-- 项目描述 -->
                    <div class="title">项目描述</div>
                    <el-form-item label="任务来源" prop="taskOrigin">
                        <el-input
                            v-model="form.taskOrigin"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入任务来源"
                            resize="vertical"
                            :disabled="isDisabled"
                        />
                    </el-form-item>
                    <el-form-item label="任务描述" prop="taskNote">
                        <el-input
                            v-model="form.taskNote"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入任务描述"
                            resize="vertical"
                            :disabled="isDisabled"
                        />
                    </el-form-item>
                    <el-form-item label="任务分析" prop="taskAnalysis">
                        <el-input
                            v-model="form.taskAnalysis"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入任务分析"
                            resize="vertical"
                            :disabled="isDisabled"
                        />
                    </el-form-item>
                    <el-form-item label="总体要求" prop="projectRequirement">
                        <el-input
                            v-model="form.projectRequirement"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入总体要求"
                            resize="vertical"
                            :disabled="isDisabled"
                        />
                    </el-form-item>
                    <el-form-item label="技术要求" prop="projectTechnology">
                        <el-input
                            v-model="form.projectTechnology"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入技术要求"
                            resize="vertical"
                            :disabled="isDisabled"
                        />
                    </el-form-item>
                </el-form>
            </div>
            <div slot="footer" class="dialog-footer" v-if="!isDisabled">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import PeopleSelector from 'dms/components/PeopleSelector';
import DepartmentSelector from 'dms/components/DepartmentSelector';
import dictData from 'dms/constant/dict';
import { getSelectedLabel } from 'dms/mixins/common';
import CompanyProjectSelectorModal from './CompanyProjectSelectorModal.vue';

export default {
    name: 'FormalProjectDialog',
    components: { PeopleSelector, DepartmentSelector, CompanyProjectSelectorModal },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        // 新增/编辑
        type: {
            type: String,
            default: 'add'
        },
        projectId: {
            type: [String, Number],
            default: null
        }
    },

    data() {
        return {
            form: {
                // 基本信息
                projectId: null,
                projectName: '',
                pmAccount: '',
                pqaAccount: '',
                startDate: '',
                planEndDate: '',
                endDate: '',
                projectStatus: '进行中',
                // 关联信息
                projectAttribution: '',
                orgCode: '',
                org: '',
                productLine: '',
                productManager: '',
                products: [],
                // 其他信息
                projectOrigin: '',
                projectPlanType: '',
                projectApproval: '',
                groupScale: '',
                projectScale: '',
                approvalGrade: '',
                projectLevel: [],
                projectNumber: '',
                // 公司立项项目信息
                companyProjectName: '',
                companyProjectApprovalId: '',
                marketDecisionName: '',
                marketDecisionAcceptDate: '',
                // 硬件项目团队信息
                hardwareProductManager: '',
                hardwareProjectManager: '',
                hardwareGroup: '',
                // 项目描述
                taskOrigin: '',
                taskNote: '',
                taskAnalysis: '',
                projectRequirement: '',
                projectTechnology: ''
            },
            rules: {
                projectName: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
                pmAccount: [{ required: true, message: '请选择项目经理', trigger: 'change' }],
                startDate: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
                orgCode: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
                productLine: [{ required: true, message: '请选择产品线', trigger: 'change' }],
                productManager: [{ required: true, message: '请输入产品经理', trigger: 'change' }],
                products: [{ required: true, message: '请选择涉及产品', trigger: 'change' }],
                planEndDate: [{ required: true, message: '请选择计划结束时间', trigger: 'change' }],
                projectStatus: [{ required: true, message: '请选择项目状态', trigger: 'change' }]
            },
            // 选项数据
            departmentOptions: [],
            productLineOptions: [],
            productOptions: [],
            dictData,
            projectAttributionOptions: dictData.projectOwnershipData || [],
            projectOriginOptions: dictData.projectSourceData || [],
            projectPlanTypeOptions: dictData.projectTypeData || [],
            projectApprovalOptions: dictData.projectApprovalTypeData || [],
            projectScaleOptions: dictData.projectScaleData || [],
            approvalGradeOptions: dictData.companyApprovalLevelData || [],
            groupScaleOptions: dictData.projectScaleData || [],
            headers: {
                Accept: 'application/json, text/plain, */*',
                Authorization: `Bearer ${this.$tools.getToken()}`
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        dialogTitle() {
            if (this.type === 'add') {
                return '新增正式项目';
            } else if (this.type === 'view') {
                return '正式项目详情';
            }
            return '编辑正式项目';
        },
        isDisabled() {
            return this.type === 'view';
        },
        url() {
            return this.$service.dms.project.import();
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.initData();
            }
        }
    },

    methods: {
        /**
         * 初始化数据
         */
        async initData() {
            // 先获取产品线选项
            await this.getProductLineOptions();

            // 如果有groupId，说明是编辑模式，需要获取详情
            if (this.projectId) {
                await this.getDetail();
                // 编辑/详情模式下，如果有产品线，需要获取对应的产品选项
                if (this.form.productLine) {
                    await this.getProductOptions(this.form.productLine);
                }
            }
        },
        async getDetail() {
            if (!this.projectId) return;
            const api = this.$service.dms.project.getProjectDetail;
            const params = { projectId: this.projectId };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.convertDataToFrom(res.data);
            } catch (error) {
                console.error('Error:', error);
                this.$message.error('获取项目详情失败');
            }
        },
        convertDataToFrom(data) {
            const { project, extend, products } = data;

            // 基本信息
            this.form.projectId = project.projectId || null;
            this.form.projectName = project.projectName || '';
            this.form.pmAccount = project.pmAccount || '';
            this.form.pqaAccount = project.pqaAccount || '';
            this.form.startDate = project.startDate || '';
            this.form.planEndDate = project.planEndDate || '';
            this.form.endDate = project.endDate || '';
            this.form.projectStatus = project?.projectStatus || '';

            // 关联信息
            this.form.projectAttribution = project.projectAttribution || '';
            this.form.orgCode = project.orgCode || '';
            this.form.org = project.org || '';
            this.form.productLine = project.productLine || '';
            this.form.productManager = project.productManager || '';
            if (Array.isArray(products) && products.length > 0) {
                this.form.products = products.filter(Boolean);
            } else {
                this.form.products = [];
            }

            // 其他信息
            this.form.projectOrigin = project.projectOrigin || '';
            this.form.projectPlanType = project.projectPlanType || '';
            this.form.projectApproval = project.projectApproval || '';
            this.form.groupScale = project.groupScale || '';
            this.form.projectScale = project.projectScale || '';
            this.form.approvalGrade = project.approvalGrade || '';
            this.form.projectNumber = project.projectNumber || '';
            this.form.projectLevel = project.projectLevel || '';

            // 公司立项项目信息
            this.form.companyProjectName = project.companyProjectName || '';
            this.form.companyProjectApprovalId = project.companyProjectApprovalId || '';
            this.form.marketDecisionName = project.marketDecisionName || '';
            this.form.marketDecisionAcceptDate = project.marketDecisionAcceptDate || '';

            // 硬件项目团队信息
            this.form.hardwareProductManager = (extend && extend.hardwareProductManager) || '';
            this.form.hardwareProjectManager = (extend && extend.hardwareProjectManager) || '';
            this.form.hardwareGroup = (extend && extend.hardwareGroup) || '';

            // 项目描述
            this.form.taskOrigin = (extend && extend.taskOrigin) || '';
            this.form.taskNote = (extend && extend.taskNote) || '';
            this.form.taskAnalysis = (extend && extend.taskAnalysis) || '';
            this.form.projectRequirement = (extend && extend.projectRequirement) || '';
            this.form.projectTechnology = (extend && extend.projectTechnology) || '';
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.form = {
                // 基本信息
                projectId: null,
                projectName: '',
                pmAccount: '',
                pqaAccount: '',
                startDate: '',
                planEndDate: '',
                endDate: '',
                projectStatus: '进行中',
                // 关联信息
                projectAttribution: '',
                orgCode: '',
                org: '',
                productLine: '',
                productManager: '',
                products: [],
                projectNumber: '',
                projectLevel: [],
                // 其他信息
                projectOrigin: '',
                projectPlanType: '',
                projectApproval: '',
                groupScale: '',
                projectScale: '',
                approvalGrade: '',
                // 公司立项项目信息
                companyProjectName: '',
                companyProjectApprovalId: '',
                marketDecisionName: '',
                marketDecisionAcceptDate: '',
                // 硬件项目团队信息
                hardwareProductManager: '',
                hardwareProjectManager: '',
                hardwareGroup: '',
                // 项目描述
                taskOrigin: '',
                taskNote: '',
                taskAnalysis: '',
                projectRequirement: '',
                projectTechnology: ''
            };
            this.$nextTick(() => {
                this.$refs.form && this.$refs.form.resetFields();
            });
        },
        /**
         * 获取产品线选项
         */
        async getProductLineOptions() {
            try {
                const res = await this.$service.dms.product.getProductLine();
                if (res.code !== '0000') {
                    this.$tools.message.err(res.message || '系统异常');
                    return;
                }
                this.productLineOptions = res.data.map((item) => {
                    return {
                        value: item,
                        label: item
                    };
                });
            } catch (error) {
                console.error('获取产品线列表失败:', error);
            }
        },
        /**
         * 处理产品线变化
         * @param {String} productLine 选中的产品线
         */
        async handleProductLineChange(productLine) {
            // 清空已选择的产品，因为产品线变了，产品列表也会变
            this.form.responsibleProduct = [];
            // 获取新的产品选项
            await this.getProductOptions(productLine);
        },

        /**
         * 获取产品下拉选项
         * @param {String} productLine  产品线
         */
        async getProductOptions(productLine) {
            // 如果没有传入产品线参数，使用表单中的产品线
            const selectedProductLine = productLine || this.form.productLine;

            if (!selectedProductLine) {
                this.productOptions = [];
                return;
            }

            try {
                const params = {
                    statusList: ['进行中'],
                    productLine: selectedProductLine
                };
                const api = this.$service.dms.common.getProductList;
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.productOptions = res.data.map((i) => ({
                    label: i.productName,
                    value: i.id
                }));
            } catch (error) {
                console.error('获取产品选项失败:', error);
                this.productOptions = [];
            }
        },
        /**
         * 提交表单
         */
        handleSubmit() {
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    try {
                        const submitData = {
                            project: {
                                projectId: this.projectId ? this.projectId : null,
                                projectName: this.form.projectName,
                                pmAccount: this.form.pmAccount,
                                projectManager: getSelectedLabel(this.$refs.projectManagerRef),
                                productManagerName: getSelectedLabel(this.$refs.productManagerRef),
                                productManager: this.form.productManager,
                                pqaAccount: this.form.pqaAccount,
                                startDate: this.form.startDate,
                                planEndDate: this.form.planEndDate,
                                endDate: this.form.endDate,
                                projectAttribution: this.form.projectAttribution,
                                orgCode: this.form.orgCode,
                                productLine: this.form.productLine,
                                projectOrigin: this.form.projectOrigin,
                                projectPlanType: this.form.projectPlanType,
                                projectApproval: this.form.projectApproval,
                                groupScale: this.form.groupScale,
                                projectScale: this.form.projectScale,
                                approvalGrade: this.form.approvalGrade,
                                companyProjectName: this.form.companyProjectName,
                                companyProjectApprovalId: this.form.companyProjectApprovalId,
                                marketDecisionName: this.form.marketDecisionName,
                                marketDecisionAcceptDate: this.form.marketDecisionAcceptDate,
                                projectNumber: this.form.projectNumber,
                                projectStatus: this.form.projectStatus,
                                projectLevel: this.form.projectLevel
                            },
                            extend: {
                                hardwareProductManager: this.form.hardwareProductManager,
                                hardwareProjectManager: this.form.hardwareProjectManager,
                                hardwareGroup: this.form.hardwareGroup,
                                taskOrigin: this.form.taskOrigin,
                                taskNote: this.form.taskNote,
                                taskAnalysis: this.form.taskAnalysis,
                                projectRequirement: this.form.projectRequirement,
                                projectTechnology: this.form.projectTechnology
                            },
                            products: this.form.products || []
                        };

                        const res = await this.$service.dms.project.createFormalProject(submitData);
                        if (res.code === '0000') {
                            this.$message.success('创建正式项目成功');
                            this.closeDialog();
                            this.$emit('success');
                        } else {
                            this.$message.error(res.message || '创建正式项目失败');
                        }
                    } catch (error) {
                        console.error('创建正式项目失败:', error);
                        this.$message.error('创建正式项目失败');
                    }
                }
            });
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.resetForm();
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        },
        /**
         * 上传成功后的处理
         * @param {Object} response 响应
         */
        handleUploadSuccess(response) {
            if (response.code !== '0000') {
                this.$message.error(response.message);
                return;
            }
            this.convertDataToFrom(response.data);
            if (this.form.productLine) {
                this.getProductOptions(this.form.productLine);
            }
            this.$message.success('导入成功! 请确认无误后保存');
        },
        /**
         * 校验文件大小与类型
         * @param {Object} file 文件
         * @returns {Boolean} 是否通过校验
         */
        validateExcelFile(file) {
            const isExcel =
                file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel';

            if (!isExcel) {
                this.$message.error('只能上传Excel文件!');
                return false;
            }

            const maxSize = 2 * 1024 * 1024;
            if (file.size > maxSize) {
                this.$message.error('文件大小不能超过2MB!');
                return false;
            }

            return true;
        },
        /**
         * 上传错误的处理
         * @param {String} err 错误信息
         */
        handleUploadError(err) {
            this.$message.error('文件上传失败,请重试!');
            console.error('上传错误:', err);
        },
        /**
         * 打开公司立项项目选择弹窗
         */
        openCompanyProjectSelectorModal() {
            this.$refs.CompanyProjectSelectorModalRef.openCompanyProjectSelectorModal();
        },
        /**
         * 处理公司立项项目输入框点击事件
         * @param {Event} event 事件
         */
        handleCompanyProjectClick(event) {
            // 检查点击的是否是清除按钮
            if (event.target.classList.contains('el-input__clear') || event.target.closest('.el-input__clear')) {
                // 如果是清除按钮，不打开弹窗
                return;
            }
            this.openCompanyProjectSelectorModal();
        },
        /**
         * 处理公司立项项目清除事件
         */
        handleCompanyProjectClear() {
            this.form.companyProjectName = '';
            this.form.companyProjectApprovalId = '';
            this.form.marketDecisionAcceptDate = '';
            this.form.marketDecisionName = '';
        },
        /**
         * 获取公司立项项目信息
         * @param {*} projectInfo 项目信息
         */
        getCompanyProjectInfo(projectInfo) {
            const { projectId, projectName, marketDecisionAcceptDate, marketDecisionName } = projectInfo;
            this.form.companyProjectName = projectName;
            this.form.companyProjectApprovalId = projectId;
            this.form.marketDecisionAcceptDate = marketDecisionAcceptDate;
            this.form.marketDecisionName = marketDecisionName;
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    margin-bottom: 16px;
    margin-top: 32px;

    &:first-child {
        margin-top: 0;
    }
}

.flex {
    display: flex;
}

.info-card {
    margin-bottom: 16px;

    .card-header {
        .card-title {
            font-weight: bold;
            font-size: 14px;
            color: #303133;
        }
    }

    .text-content {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        min-height: 60px;
        color: #303133;
    }
}

::v-deep .el-card__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

::v-deep .el-card__body {
    padding: 20px;
}

::v-deep .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

::v-deep .form .el-form-item__label {
    font-weight: bold;
}

.dialog-footer {
    text-align: right;
}

::v-deep .el-select {
    width: 100%;
}

::v-deep .el-textarea__inner {
    resize: vertical;
}

// 表格样式
::v-deep .el-table {
    .el-table__header-wrapper {
        th {
            background-color: #fafbfc;
            color: #606266;
            font-weight: 600;
        }
    }

    .el-table__body-wrapper {
        td {
            color: #303133;
        }
    }

    .el-table__border {
        border-color: #ebeef5;
    }

    th,
    td {
        border-color: #ebeef5;
    }
}
.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 14px;
    }
}
.project-container {
    position: relative;
}
.import-button {
    position: absolute;
    right: 0px;
    top: -10px;
    z-index: 2;
}
</style>
