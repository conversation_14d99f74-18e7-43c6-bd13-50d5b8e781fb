<template>
    <div class="zandaoModal">
        <el-dialog title="公司立项项目选择" :visible.sync="dialogVisible" width="60%" append-to-body ref="dialog">
            <form @submit.prevent="getList" class="flex">
                <el-input class="projectgetList" v-model="projectName" placeholder="请输入项目名称" size="medium">
                </el-input>
                <el-button
                    icon="el-icon-search"
                    @click="getList"
                    type="primary"
                    style="height: 36px; width: 80px"
                ></el-button>
            </form>
            <p style="color: red; margin: 5px">根据项目开始时间倒序，最多显示100条，请根据项目名称进行过滤</p>
            <el-table class="dms-table" :data="list" height="450">
                <el-table-column
                    prop="projectName"
                    label="项目名称"
                    header-align="center"
                    align="left"
                    min-width="400"
                ></el-table-column>
                <el-table-column label="操作" width="120" header-align="center" align="center">
                    <template slot-scope="scope">
                        <el-button type="primary" @click="confirm(scope.row)">确认</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name: 'ZendaoSelectorModal',
    components: {},
    data() {
        return {
            dialogVisible: false,
            list: [],
            // 筛选条件
            projectName: ''
        };
    },
    methods: {
        /**
         * 控制弹框打开时的操作
         */
        openCompanyProjectSelectorModal() {
            this.dialogVisible = true;
            // 等待表格实例挂载完成
            this.$nextTick(() => {
                this.getList();
            });
        },
        /**
         * 确认
         * @param {Object} row 每行对应的项目
         */
        confirm(row) {
            this.$emit('getProjectInfo', row);
            this.dialogVisible = false;
        },
        async getList() {
            const params = {
                projectName: this.projectName
            };
            const api = this.$service.dms.project.getCompanyTemporaryProjectList;

            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.list = res?.data?.list || [];
                console.log(res.data, 'data');
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}

.el-main {
    padding: 0 10px;
    height: 100%;
}

// 修改弹窗大小
.zandaoModal {
    ::v-deep .el-dialog {
        margin: 5% auto !important;
        width: 55% !important;
    }
}
.dms-table {
    ::v-deep .el-table .el-table__row {
        height: 35px !important;
    }
}

// 搜索框按钮样式
::v-deep .el-input-group__append {
    background-color: #3370ff;
    color: white;
}
// 搜索框
.projectgetList {
    height: 30px;
    min-width: 200px;
}
.getListBtn {
    width: 80px;
    background-color: #3370ff;
}
.getListBtn:hover {
    background-color: #3370ff;
}

.getListBtn:focus {
    background-color: #3370ff;
}
</style>
