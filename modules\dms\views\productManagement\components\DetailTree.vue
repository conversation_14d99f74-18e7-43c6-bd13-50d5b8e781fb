<template>
    <div class="draggable-tree">
        <el-tree
            ref="tree"
            :data="data"
            :props="props"
            :default-expand-all="expandAll"
            :expand-on-click-node="false"
            node-key="id"
        >
            <template #default="{ node, data }">
                <div class="tree-node">
                    <div class="node-label">
                        <span v-if="data.action !== '删除'" class="node-index">{{
                            `${data.moduleType || ''}${data.moduleType === 'P' ? '' : data.sort}`
                        }}</span>
                        <span v-if="data.action === '新增'" class="node-add">
                            {{ node.label }}
                            <svg-icon icon-class="dms-product-add" class="svg-icon"></svg-icon>
                        </span>
                        <span v-else-if="data.action === '修改'" :title="data.originalModuleName" class="node-edit">
                            {{ node.label }}
                            <svg-icon icon-class="dms-product-edit" class="svg-icon"></svg-icon>
                        </span>
                        <div v-else-if="data.action === '删除'">
                            <del class="node-delete">{{ node.label }}</del
                            ><i class="el-icon-delete" style="color: #f00; margin-left: 4px"></i>
                        </div>
                        <div v-else>{{ node.label }}</div>
                    </div>
                </div>
            </template>
        </el-tree>
    </div>
</template>

<script>
export default {
    name: 'DraggableTree',
    props: {
        data: {
            type: Array,
            default: () => []
        },
        props: {
            type: Object,
            default: () => ({
                children: 'children',
                label: 'moduleName'
            })
        },
        expandAll: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {};
    }
};
</script>

<style scoped>
.draggable-tree {
    width: 100%;
}

.tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 8px;
}

.node-label {
    display: flex;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.node-label:hover {
    background-color: #f5f7fa;
}

.node-label.disabled {
    color: #c0c4cc;
    cursor: not-allowed;
}

.node-label.disabled:hover {
    background-color: transparent;
}

.node-input {
    flex: 1;
    margin-right: 22px;
}

.add-node-popover {
    margin-right: 8px;
}

.node-type-selector {
    text-align: center;
}

.selector-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #303133;
}

.selector-options {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.type-button {
    flex: 1;
    min-width: 60px;
}

.node-index {
    color: #333;
    font-weight: 600;
    margin-right: 4px;
}
.node-delete {
    color: #333;
}
.node-edit {
    color: #6090ee;
}
.node-add {
    color: #70b603;
}

.svg-icon {
    width: 14px;
    height: 14px;
    padding: 0;
}
</style>
