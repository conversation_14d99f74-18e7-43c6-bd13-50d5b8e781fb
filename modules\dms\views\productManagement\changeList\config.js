import CommonItems from 'snbcCommon/common/form-items.js';

const { productLineSelector, input, dateRange } = CommonItems;

// 产品线
const productLine = {
    ...productLineSelector,
    name: '产品线',
    modelKey: 'productLine'
};
const productName = {
    ...input,
    name: '产品名称',
    modelKey: 'productName'
};

const productCode = {
    ...input,
    name: '产品编号',
    modelKey: 'productCode'
};

const productOwner = {
    ...input,
    name: 'Product Owner',
    modelKey: 'productOwner'
};

const productId = {
    ...input,
    name: '产品ID',
    modelKey: 'productId'
};

const checkUser = {
    ...input,
    name: '审批人',
    modelKey: 'checkUser'
};

const applyDate = {
    ...dateRange,
    name: '申请日期',
    modelKey: 'applyDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

const checkDate = {
    ...dateRange,
    name: '审批日期',
    modelKey: 'checkDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [productLine, productName, productCode, productOwner, productId, checkUser, applyDate, checkDate]
};

// 查询条件参数
export const queryParams = {
    productLine: '',
    productName: '',
    productCode: '',
    productOwner: '',
    productId: '',
    checkUser: '',
    applyDate: [],
    checkDate: []
};

// 导航栏配置
export const navItems = [
    { field: '', name: '所有', queryField: 'status' },
    { field: '待审核', name: '待审核', queryField: 'status' },
    { field: '已通过', name: '已通过', queryField: 'status' },
    { field: '已拒绝', name: '已拒绝', queryField: 'status' },
    { field: '已撤回', name: '已撤回', queryField: 'status' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        minWidth: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productId',
        label: 'ID',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productCode',
        label: '产品编号',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productName',
        label: '产品名称',
        minWidth: 200,
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productOwner',
        label: 'Product Owner',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'checkUser',
        label: '审批人',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'applyDate',
        label: '申请日期',
        minWidth: 160,
        sortable: 'custom',
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'checkResult',
        label: '审批结果',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'checkDate',
        label: '审批日期',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    }
];
