// 目前框架路由，只支持到二级路由
export default [
    {
        path: '/app/dms/groupManagement',
        useLayout: true,
        redirect: 'noRedirect',
        noPermission: true,
        name: 'DmsGroupManagement',
        meta: { title: '团队管理', icon: 'fa fa-square' },
        children: [
            {
                path: 'schedule',
                component: () => import('../views/groupManagement/schedule/index'),
                noPermission: true,
                name: 'Schedule',
                meta: { title: '进度计划', icon: 'fa fa-square' }
            },
            {
                path: 'resourceManagement',
                component: () => import('../views/groupManagement/resourceManagement'),
                noPermission: true,
                name: 'GroupResourceManagement',
                meta: { title: '资源管理', icon: 'fa fa-square' }
            },
            {
                path: 'PlanEvalute',
                component: () => import('../views/groupManagement/schedule/plan-evalute/index.vue'),
                name: 'PlanEvalute',
                meta: { title: '计划管理冲刺评价', icon: 'fa el-icon-document' }
            },
            {
                path: 'planEvaluteAction',
                component: () => import('../views/groupManagement/schedule/plan-evalute-action'),
                name: 'planEvaluteAction',
                meta: { title: '冲刺评价审核', icon: 'fa el-icon-document' }
            },
            {
                path: 'planReviewAction',
                component: () => import('../views/groupManagement/schedule/plan-review-action'),
                name: 'planReviewAction',
                meta: { title: '计划审核批准/拒绝页面', icon: 'fa el-icon-document' }
            },
            {
                path: 'planChange',
                component: () => import('../views/groupManagement/schedule/plan-change/index.vue'),
                name: 'PlanChange',
                meta: { title: '变更计划', icon: 'fa el-icon-document' }
            },
            {
                path: 'groupList',
                component: () => import('../views/groupManagement/groupList/index.vue'),
                name: 'GroupList',
                meta: { title: '团队列表', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'addPlan',
                component: () => import('../views/groupManagement/schedule/add-plan'),
                name: 'AddPlan',
                meta: { title: '新建计划', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'planDetail',
                component: () => import('../views/groupManagement/schedule/plan-detail/index.vue'),
                name: 'PlanDetail',
                meta: { title: '计划详情', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            }
        ]
    },
    {
        path: '/app/dms/departmentManagement',
        useLayout: true,
        redirect: 'noRedirect',
        noPermission: true,
        name: 'DmsDepartmentManagement',
        meta: { title: '部门管理', icon: 'fa fa-square' },
        children: [
            {
                path: 'resourceManagement',
                component: () => import('../views/departmentManagement/resourceManagement'),
                noPermission: true,
                name: 'DepartmentResourceManagement',
                meta: { title: '资源管理', icon: 'fa fa-square' }
            },
            {
                path: 'personnelDashboard',
                component: () => import('../views/departmentManagement/resourceManagement/personnelDashboard'),
                noPermission: true,
                name: 'PersonnelDashboard',
                meta: { title: '个人看板', icon: 'fa fa-square' }
            }
        ]
    },
    {
        path: '/app/dms/projectManagement',
        useLayout: true,
        redirect: 'noRedirect',
        noPermission: true,
        name: 'DmsProjectManagement',
        meta: { title: '项目管理', icon: 'fa fa-square' },
        children: [
            {
                path: 'resourceManagement',
                component: () => import('../views/projectManagement/resourceManagement'),
                noPermission: true,
                name: 'ProjectManagementResourceManagement',
                meta: { title: '资源管理', icon: 'fa fa-square' }
            },
            {
                path: 'projectList',
                component: () => import('../views/projectManagement/projectList'),
                noPermission: true,
                name: 'ProjectList',
                meta: { title: '项目列表', icon: 'fa fa-square' }
            },
            {
                path: 'projectReview',
                component: () => import('../views/projectManagement/review'),
                noPermission: true,
                name: 'ProjectReview',
                meta: { title: '项目审核', icon: 'fa fa-square' }
            }
        ]
    },
    {
        path: '/app/dms/demandManagement',
        useLayout: true,
        redirect: 'noRedirect',
        noPermission: true,
        name: 'DmsDemandManagement',
        meta: { title: '需求管理', icon: 'fa fa-square' },
        children: [
            {
                path: 'AddRequire',
                component: () => import('../views/demandManagement/add-require'),
                name: 'AddRequire',
                meta: { title: '新建需求', icon: 'fa el-icon-document' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'demandList',
                component: () => import('../views/demandManagement/demandList'),
                name: 'DemandList',
                meta: { title: '需求列表', icon: 'fa el-icon-document' }
            },
            {
                path: 'OriginalRequire',
                component: () => import('../views/demandManagement/originalRequire/index'),
                name: 'OriginalRequire',
                meta: { title: '我负责的', icon: 'fa fa-square' }
            },
            {
                path: 'CreateRequire',
                component: () => import('../views/demandManagement/originalRequire/create-require.vue'),
                name: 'CreateRequire',
                meta: { title: '创建需求', icon: 'fa fa-square' },
                hidden: true
            },
            {
                path: 'auditDemand',
                component: () => import('../views/demandManagement/auditDemand/index.vue'),
                noPermission: true,
                name: 'AuditDemand',
                meta: { title: '需求审核', icon: 'fa fa-square' }
            },
            {
                path: 'myProposal',
                component: () => import('../views/demandManagement/myProposal/index.vue'),
                noPermission: true,
                name: 'MyProposal',
                meta: { title: '我提出的', icon: 'fa fa-square' }
            },
            {
                path: 'ChangeRequire',
                component: () => import('../views/demandManagement/change-require'),
                hidden: true,
                noPermission: true,
                name: 'ChangeRequire',
                meta: { title: '需求变更', icon: 'fa fa-square' }
            }
        ]
    },
    {
        path: '/app/dms/productManagement',
        useLayout: true,
        redirect: 'noRedirect',
        noPermission: true,
        name: 'DmsProductManagement',
        meta: { title: '产品管理', icon: 'fa fa-square' },
        children: [
            {
                path: 'ProductList',
                component: () => import('../views/productManagement/productList/index.vue'),
                name: 'ProductList',
                meta: { title: '产品列表', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'AddProduct',
                component: () => import('../views/productManagement/addProduct/index.vue'),
                name: 'AddProduct',
                meta: { title: '新建产品', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'EditProduct',
                component: () => import('../views/productManagement/editProduct/index.vue'),
                name: 'EditProduct',
                meta: { title: '审核编辑产品', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'DetailsProduct',
                component: () => import('../views/productManagement/detailsProduct/index.vue'),
                name: 'DetailsProduct',
                meta: { title: '产品详情', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'ChangeList',
                component: () => import('../views/productManagement/changeList/index.vue'),
                name: 'ChangeList',
                meta: { title: '我的变更', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'CheckDetails',
                component: () => import('../views/productManagement/checkDetails/index.vue'),
                name: 'CheckDetails',
                meta: { title: '我的变更——审批详情', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'ChangeDetails',
                component: () => import('../views/productManagement/changeDetails/index.vue'),
                name: 'ChangeDetails',
                meta: { title: '产品审核——审核变更详情', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'ProductCheck',
                component: () => import('../views/productManagement/productCheck/index.vue'),
                name: 'ProductCheck',
                meta: { title: '产品审核', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            },
            {
                path: 'MyProduct',
                component: () => import('../views/productManagement/myProduct/index.vue'),
                name: 'MyProduct',
                meta: { title: '我的产品', icon: 'fa fa-square' },
                hidden: true,
                noPermission: true
            }
        ]
    }
];
