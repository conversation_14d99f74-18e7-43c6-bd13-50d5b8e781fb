<!-- 计划管理——计划详情 -->
<template>
    <div class="plan-detail-container">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button @click="handleReturn" type="primary">返回</el-button>
        </div>
        <PlanDetail></PlanDetail>
        <FormulaTitle title="操作记录"></FormulaTitle>
        <OperationRecord :collapseItems="collapseItems"></OperationRecord>
    </div>
</template>

<script>
import OperationRecord from 'dms/views/productManagement/components/operationRecord.vue';
import PlanDetail from 'dms/views/groupManagement/components/PlanDetail';
import FormulaTitle from '../components/formulaTitle.vue';

export default {
    name: 'PlanDetailPage',
    components: { OperationRecord, PlanDetail, FormulaTitle },
    props: {},
    data() {
        return {
            // 计划ID
            planId: '',
            collapseItems: []
        };
    },
    computed: {
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        },
        groupOptions() {
            return this.$store.state.dms.groupOption;
        }
    },
    async mounted() {
        // 获取计划ID
        if (this.$route?.query?.id) {
            this.planId = this.$route?.query?.id;
        } else {
            this.$message.error('缺少计划ID参数');
            this.$router.back();
            return;
        }
        this.getActions();
    },
    methods: {
        handleReturn() {
            this.$router.back();
        },
        /**
         * 获取操作记录
         */
        async getActions() {
            try {
                const params = {
                    objectId: this.planId
                };
                const res = await this.$service.dms.product.getActions(params);
                if (res.code === '0000') {
                    this.collapseItems = res.data || [];
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.plan-detail-container {
    padding: 10px 20px 15px 20px;
}
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
</style>
