/* eslint-disable max-lines-per-function */
import './icons';
import Cookies from 'js-cookie';

import moduleRoutes from './router';
import moduleI18n from './lang/index.js';
import moduleStore from './store';
import moduleService from './service';
import config from './config.js';
import moduleConstant from './common/constant.js';

const { moduleName } = config;

const initNativeDrop = ($eventBus) => {
    const getEnvUrl = () => {
        const { hostname } = window.location;
        // 根据不同环境（本地/测试/生产），使用不同地址
        if (hostname === 'plouto.xtjc.net') {
            // 测试环境
            return 'plouto.xtjc.net';
        } else if (hostname === '127.0.0.1' || hostname === 'local-plouto.xtjc.net') {
            return 'local-plouto.xtjc.net:8000/index.dev.html';
        }
        // 生产环境
        return 'test-plouto.xtjc.net';
    };
    const handleChangePWDEvent = () => {
        const loginPageUrl = `http://${getEnvUrl()}`;
        window.location.href = loginPageUrl;
    };
    const handleLogOutEvent = () => {
        const loginPageUrl = `http://${getEnvUrl()}`;
        window.location.href = loginPageUrl;
    };
    // 监听修改密码
    $eventBus.on('changePWDEvent', handleChangePWDEvent);
    // 监听退出登录
    $eventBus.on('logOutEvent', handleLogOutEvent);
};

export default ({ Vue, router, store, i18n }) => {
    // 加载国际化
    Vue.prototype.$addI18n(moduleName, moduleI18n);
    // 注册路由
    Vue.prototype.$addRoutes(moduleName, moduleRoutes);
    // 注册状态树
    Vue.prototype.$addStore(moduleName, moduleStore);
    // 注册模块service
    Vue.prototype.$service[moduleName] = moduleService(Vue);
    // 注册静态变量
    Vue.prototype.$addConstant(moduleName, moduleConstant);

    // 初始化原生下拉框事件
    initNativeDrop(Vue.prototype.$eventBus);

    router.beforeEach(async (to, from, next) => {
        const { hasMenu } = store.state.frame;
        const { token } = store.getters;
        if (!hasMenu && token) {
            const recursionMenu = (menu) => {
                const res = [];
                menu.forEach((item) => {
                    item.children = item.nodes;
                    item.meta = {};
                    // 如果存在icon，则直接设置，否则设置为默认的icon
                    if (!item.icon) {
                        item.icon = 'fa fa-square';
                    }
                    item.meta.icon = item.icon;
                    item.meta.title = item.text;
                    item.permissionIcon = item.icon;

                    if (item.url && (item.url.indexOf('https') > -1 || item.url.indexOf('http') > -1)) {
                        if (process.env.NODE_ENV === 'development') {
                            item.path = item.url.replace(
                                'http://test-plouto.xtjc.net/dms',
                                'http://local-plouto.xtjc.net/dms'
                            );
                        } else {
                            item.path = item.url;
                        }
                    } else {
                        let port = '';
                        let suffix = '';
                        // 本地起服务，需要配置端口号
                        if (window.location.hostname === 'local-plouto.xtjc.net') {
                            if (item.url.startsWith('/dms')) {
                                port = `:8001`;
                            } else {
                                port = `:8000`;
                            }
                            suffix = '/index.dev.html';
                            // 去除第一个斜杠
                            const match = item.url.match(/^\/(.*)$/);
                            item.url = match ? match[1] : item.url;
                        }
                        // 这里参照wtf2.0中对于routingUrl的处理，补充路由的前缀
                        // 路由配置为除了hostname之外的所有部分，所以直接拼接即可
                        item.path = `http://${window.location.hostname}${port}${suffix}${item.url}`;
                    }

                    item.useLayout = true;
                    if (item.children && item.children.length > 0) {
                        recursionMenu(item.children);
                    } else {
                        item.children = [];
                    }
                    res.push(item);
                });
                return res;
            };

            const getPermissionList = () => {
                let langKey = '';
                if (Cookies.get('langKey')) {
                    langKey = Cookies.get('langKey');
                }
                const menuData = {
                    sysType: 'SYS-PC-WEB',
                    langKey,
                    permissionType: 'MENU'
                };
                // 获取菜单信息
                Vue.prototype.$service.frame
                    .getMenuList(menuData)
                    .then((response) => {
                        if (response.code === '00') {
                            //  遍历菜单数据，调整成跟BBPF2.0格式一样的
                            const menuList = recursionMenu(response.result);
                            store.dispatch('permission/generateRoutes', menuList);
                            // 存储当前是否已经存在菜单
                            store.dispatch('frame/saveMenuState', true);
                        } else {
                            Vue.prototype.$message({
                                message: response.message,
                                type: 'error'
                            });
                        }
                    })
                    .catch((err) => {
                        console.error(err);
                    });
            };

            const getBtnPermissionList = () => {
                let langKey = '';
                if (Cookies.get('langKey')) {
                    langKey = Cookies.get('langKey');
                }
                const menuData = {
                    sysType: 'SYS-PC-WEB',
                    langKey,
                    permissionType: 'BUTTON'
                };

                // 递归获取所有层级的按钮权限码
                const getAllButtonCodes = (data) => {
                    const codes = [];

                    const traverse = (items) => {
                        if (!items) return;

                        const itemsArray = Array.isArray(items) ? items : [items];

                        itemsArray.forEach((item) => {
                            if (item.code) {
                                codes.push(item.code);
                            }
                            if (item.nodes) {
                                traverse(item.nodes);
                            }
                        });
                    };

                    traverse(data);
                    return codes;
                };

                // 获取菜单信息
                Vue.prototype.$service.frame
                    .getMenuList(menuData)
                    .then((response) => {
                        if (response.code === '00') {
                            // 获取所有层级的按钮权限码
                            const btnDatas = getAllButtonCodes(response.result);
                            store.dispatch('permission/btnPermissionData', btnDatas);
                        } else {
                            Vue.prototype.$message({
                                message: response.message,
                                type: 'error'
                            });
                        }
                    })
                    .catch((err) => {
                        console.error(err);
                    });
            };
            store.commit('user/SET_HADLOGIN', true);
            getPermissionList();
            getBtnPermissionList();
        }
        // 判断路由中404、500等异常情况
        next();
    });
};
