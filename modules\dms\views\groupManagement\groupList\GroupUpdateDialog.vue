<template>
    <div>
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="85%" top="5vh" :before-close="closeDialog">
            <div class="project-container">
                <el-upload
                    v-if="!isViewMode"
                    class="import-button"
                    :action="url"
                    :show-file-list="false"
                    :on-success="handleUploadSuccess"
                    :on-error="handleUploadError"
                    :before-upload="validateExcelFile"
                    :auto-upload="true"
                    name="file"
                    :headers="headers"
                >
                    <el-button type="primary">导入团队建立任务书</el-button>
                </el-upload>
                <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="form">
                    <!-- 基本信息 -->
                    <div class="title">基本信息</div>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="团队名称" prop="teamName">
                                <el-input
                                    v-model="form.teamName"
                                    :disabled="isViewMode"
                                    placeholder="请输入团队名称"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="Team Leader" prop="teamLeader">
                                <PeopleSelector
                                    v-model="form.teamLeader"
                                    :disabled="isViewMode"
                                    placeholder="请选择Team Leader"
                                    :isMultipled="false"
                                    ref="teamLeaderRef"
                                >
                                </PeopleSelector>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="24">
                        <el-col span="12">
                            <el-form-item label="团队类型" prop="teamType">
                                <el-select v-model="form.teamType" :disabled="isViewMode" placeholder="请选择">
                                    <el-option
                                        v-for="item in teamTypeOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="form.projectStatus === '关闭' ? 6 : 12">
                            <el-form-item label="团队状态" prop="projectStatus">
                                <el-select
                                    v-model="form.projectStatus"
                                    :disabled="isViewMode"
                                    placeholder="请选择"
                                    @change="() => (this.form.endDate = '')"
                                >
                                    <el-option
                                        v-for="item in dictData.groupStatusData"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col span="6" v-if="form.projectStatus === '关闭'">
                            <el-form-item label="关闭日期" prop="endDate">
                                <el-date-picker
                                    v-model="form.endDate"
                                    :disabled="isViewMode"
                                    type="date"
                                    placeholder="请选择关闭日期"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                    style="width: 100%"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!-- 关联信息 -->
                    <div class="title">关联信息</div>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="直属上级" prop="directSuperior">
                                <PeopleSelector
                                    v-model="form.directSuperior"
                                    :disabled="isViewMode"
                                    :isMultipled="false"
                                    placeholder="请选择请选择直属上级"
                                >
                                </PeopleSelector>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="所属部门" prop="department">
                                <DepartmentSelector
                                    v-model="form.department"
                                    :disabled="isViewMode"
                                    placeholder="请选择"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="item in departmentOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </DepartmentSelector>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="产品线" prop="productLine">
                                <el-select
                                    v-model="form.productLine"
                                    :disabled="isViewMode"
                                    @change="handleProductLineChange"
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="item in productLineOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="负责产品" prop="responsibleProduct">
                                <el-select
                                    v-model="form.responsibleProduct"
                                    :disabled="isViewMode"
                                    multiple
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="item in productOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <!-- 团队愿景 -->
                    <div class="title">团队愿景</div>
                    <el-form-item prop="teamVision">
                        <el-input
                            v-model="form.teamVision"
                            :disabled="isViewMode"
                            type="textarea"
                            :rows="4"
                            placeholder="建议参考的模板：在<时间范围>内成为<领域>的<角色定位>，通过<核心手段>实现<变革性影响>"
                        >
                        </el-input>
                    </el-form-item>

                    <!-- 业务和职能范围 -->
                    <div class="title">业务和职能范围</div>
                    <el-form-item prop="businessScope">
                        <el-input
                            v-model="form.businessScope"
                            :disabled="isViewMode"
                            type="textarea"
                            :rows="4"
                            placeholder="建议参考的模板：核心业务边界---<直接负责领域>；协同边界---与<关联团队>共同承担的<交叉职能>"
                        >
                        </el-input>
                    </el-form-item>

                    <!-- 质量技术目标及要求 -->
                    <div class="title">质量技术目标及要求</div>
                    <el-form-item prop="qualityTechGoals">
                        <el-input
                            v-model="form.qualityTechGoals"
                            :disabled="isViewMode"
                            type="textarea"
                            :rows="4"
                            placeholder="建议参考的模板：基础线---100%满足<行业标准/合规要求>；挑战线---<关键质量维度>达到<行业标杆>水平；技术雷达---主攻<核心技术方向>，限制使用<淘汰技术>，试点<前沿技术>"
                        >
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">{{ isViewMode ? '关闭' : '取消' }}</el-button>
                <el-button v-if="!isViewMode" type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import PeopleSelector from 'dms/components/PeopleSelector';
import DepartmentSelector from 'dms/components/DepartmentSelector';
import dictData from 'dms/constant/dict.js';
import { getSelectedLabel } from 'dms/mixins/common';

export default {
    name: 'CreateGroupDialog',
    components: { PeopleSelector, DepartmentSelector },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        groupId: {
            type: [String, Number],
            default: null
        },
        type: {
            type: String,
            // add: 新增, edit: 编辑, view: 查看
            default: 'add',
            validator: (value) => ['add', 'edit', 'view'].includes(value)
        }
    },

    data() {
        return {
            form: {
                // projectName - 项目中文名
                teamName: '',
                // projectManager - 项目经理
                teamLeader: '',
                // projectGroupType - 项目类型
                teamType: '',
                // higherUp - 直属上级
                directSuperior: '',
                // 团队状态
                projectStatus: '进行中',
                // 关闭日期
                endDate: '',
                // org - 归属部门
                department: '',
                // productLine - 产品线
                productLine: '',
                // productsName - 涉及产品名字
                responsibleProduct: [],
                // taskNote - 任务描述或者团队愿景
                teamVision: '',
                // projectRequirement - 总体要求或者业务和职能范围
                businessScope: '',
                // projectTechnology - 技术要求或者质量技术目标及要求
                qualityTechGoals: ''
            },
            rules: {
                teamName: [{ required: true, message: '请输入团队名称', trigger: 'change' }],
                teamLeader: [{ required: true, message: '请选择Team Leader', trigger: 'change' }],
                department: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
                productLine: [{ required: true, message: '请选择产品线', trigger: 'change' }],
                responsibleProduct: [{ required: true, message: '请选择负责产品', trigger: 'change' }],
                projectStatus: [{ required: true, message: '请选择项目状态', trigger: 'change' }],
                endDate: [{ required: true, message: '请选择关闭日期', trigger: 'change' }]
            },
            // 团队类型选项
            teamTypeOptions: dictData.groupTypeData,
            dictData,
            // 部门选项
            departmentOptions: [],
            // 产品线选项
            productLineOptions: [],
            // 产品选项
            productOptions: [],
            headers: {
                Accept: 'application/json, text/plain, */*',
                Authorization: `Bearer ${this.$tools.getToken()}`
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        url() {
            return this.$service.dms.group.import();
        },
        isViewMode() {
            return this.type === 'view';
        },
        dialogTitle() {
            const titleMap = {
                add: '创建团队',
                edit: '编辑团队',
                view: '团队详情'
            };
            return titleMap[this.type] || '团队信息';
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.initData();
            }
        }
    },
    methods: {
        /**
         * 初始化数据
         */
        async initData() {
            // 如果不是查看模式，需要获取产品线选项
            if (!this.isViewMode) {
                await this.getProductLineOptions();
            }

            // 如果有groupId，需要获取详情
            if (this.groupId) {
                await this.fetchGroupDetail();
                // 如果有产品线，需要获取对应的产品选项
                if (this.form.productLine) {
                    await this.getProductOptions(this.form.productLine);
                }
            }
        },

        /**
         * 获取团队详情（编辑模式）
         */
        async fetchGroupDetail() {
            try {
                const params = { projectId: this.groupId, projectCategory: 'T' };
                const api = this.$service.dms.group.getGroupDetail;
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                const { data } = res;
                this.convertDataToFrom(data);
            } catch (error) {
                this.$message.error('获取团队详情失败');
                throw error;
            }
        },
        convertDataToFrom(data) {
            // 映射后端字段到前端表单字段
            this.form = {
                teamName: data.project?.projectName || '',
                teamLeader: data.project?.pmAccount || '',
                teamType: data.project?.projectGroupType || '',
                directSuperior: data.project?.higherUp || '',
                projectStatus: data.project?.projectStatus || '',
                endDate: data.project?.endDate || '',
                department: data.project?.orgCode || '',
                productLine: data.project?.productLine || '',
                responsibleProduct: data.products || [],
                teamVision: data.extend?.taskNote || '',
                businessScope: data.extend?.projectRequirement || '',
                qualityTechGoals: data.extend?.projectTechnology || ''
            };
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.form = {
                // projectName - 项目中文名
                teamName: '',
                // projectManager - 项目经理
                teamLeader: '',
                // projectGroupType - 项目类型
                teamType: '',
                // higherUp - 直属上级
                directSuperior: '',
                // org - 归属部门
                department: '',
                // 团队状态
                projectStatus: '进行中',
                // 关闭日期
                endDate: '',
                // productLine - 产品线
                productLine: '',
                // productsName - 涉及产品
                responsibleProduct: [],
                // taskNote - 任务描述或者团队愿景
                teamVision: '',
                // projectRequirement - 总体要求或者业务和职能范围
                businessScope: '',
                // projectTechnology - 技术要求或者质量技术目标及要求
                qualityTechGoals: ''
            };
            this.$nextTick(() => {
                this.$refs.form && this.$refs.form.resetFields();
            });
        },
        /**
         * 获取产品线选项
         */
        async getProductLineOptions() {
            try {
                const res = await this.$service.dms.product.getProductLine();
                if (res.code !== '0000') {
                    this.$tools.message.err(res.message || '系统异常');
                    return;
                }
                this.productLineOptions = res.data.map((item) => {
                    return {
                        value: item,
                        label: item
                    };
                });
            } catch (error) {
                console.error('获取产品线列表失败:', error);
            }
        },

        /**
         * 处理产品线变化
         * @param {String} productLine 选中的产品线
         */
        async handleProductLineChange(productLine) {
            // 清空已选择的产品，因为产品线变了，产品列表也会变
            this.form.responsibleProduct = [];
            // 获取新的产品选项
            await this.getProductOptions(productLine);
        },

        /**
         * 获取产品下拉选项
         * @param {String} productLine  产品线
         */
        async getProductOptions(productLine) {
            // 如果没有传入产品线参数，使用表单中的产品线
            const selectedProductLine = productLine || this.form.productLine;

            if (!selectedProductLine) {
                this.productOptions = [];
                return;
            }

            try {
                const params = {
                    statusList: ['进行中'],
                    productLine: selectedProductLine
                };
                const api = this.$service.dms.common.getProductList;
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.productOptions = res.data.map((i) => ({
                    label: i.productName,
                    value: i.id
                }));
            } catch (error) {
                console.error('获取产品选项失败:', error);
                this.productOptions = [];
            }
        },
        /**
         * 提交表单
         */
        handleSubmit() {
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    try {
                        // 映射前端字段到后端字段
                        const submitData = {
                            project: {
                                projectId: this.groupId || null,
                                projectName: this.form.teamName,
                                pmAccount: this.form.teamLeader,
                                projectManager: getSelectedLabel(this.$refs.teamLeaderRef),
                                projectGroupType: this.form.teamType,
                                higherUp: this.form.directSuperior,
                                orgCode: this.form.department,
                                productLine: this.form.productLine,
                                projectStatus: this.form.projectStatus,
                                endDate: this.form.endDate
                            },
                            products: this.form.responsibleProduct,
                            extend: {
                                taskNote: this.form.teamVision,
                                projectRequirement: this.form.businessScope,
                                projectTechnology: this.form.qualityTechGoals
                            }
                        };

                        const res = await this.$service.dms.group.updateGroup(submitData);
                        if (res.code === '0000') {
                            this.$message.success(this.groupId ? '编辑团队成功' : '创建团队成功');
                            this.closeDialog();
                            this.$emit('success');
                        } else {
                            this.$message.error(res.message || (this.groupId ? '编辑团队失败' : '创建团队失败'));
                        }
                    } catch (error) {
                        console.error(this.groupId ? '编辑团队失败:' : '创建团队失败:', error);
                        this.$message.error(this.groupId ? '编辑团队失败' : '创建团队失败');
                    }
                }
            });
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.resetForm();
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        },
        /**
         * 上传成功后的处理
         * @param {Object} response 响应
         */
        handleUploadSuccess(response) {
            if (response.code !== '0000') {
                this.$message.error(response.message);
                return;
            }
            this.convertDataToFrom(response.data);
            if (this.form.productLine) {
                this.getProductOptions(this.form.productLine);
            }
            this.$message.success('导入成功! 请确认无误后保存');
        },
        /**
         * 校验文件大小与类型
         * @param {Object} file 文件
         * @returns {Boolean} 是否通过校验
         */
        validateExcelFile(file) {
            const isExcel =
                file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel';

            if (!isExcel) {
                this.$message.error('只能上传Excel文件!');
                return false;
            }

            const maxSize = 2 * 1024 * 1024;
            if (file.size > maxSize) {
                this.$message.error('文件大小不能超过2MB!');
                return false;
            }

            return true;
        },
        /**
         * 上传错误的处理
         * @param {String} err 错误信息
         */
        handleUploadError(err) {
            this.$message.error('文件上传失败,请重试!');
            console.error('上传错误:', err);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    margin-bottom: 16px;
    margin-top: 32px;

    &:first-child {
        margin-top: 0;
    }
}

.flex {
    display: flex;
}

.info-card {
    margin-bottom: 16px;

    .card-header {
        .card-title {
            font-weight: bold;
            font-size: 14px;
            color: #303133;
        }
    }

    .text-content {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        min-height: 60px;
        color: #303133;
    }
}

::v-deep .el-card__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

::v-deep .el-card__body {
    padding: 20px;
}

::v-deep .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

::v-deep .form .el-form-item__label {
    font-weight: bold;
}

.dialog-footer {
    text-align: right;
}

::v-deep .el-select {
    width: 100%;
}

::v-deep .el-textarea__inner {
    resize: vertical;
}

// 表格样式
::v-deep .el-table {
    .el-table__header-wrapper {
        th {
            background-color: #fafbfc;
            color: #606266;
            font-weight: 600;
        }
    }

    .el-table__body-wrapper {
        td {
            color: #303133;
        }
    }

    .el-table__border {
        border-color: #ebeef5;
    }

    th,
    td {
        border-color: #ebeef5;
    }
}
.project-container {
    position: relative;
}
.import-button {
    position: absolute;
    right: 0px;
    top: -10px;
    z-index: 2;
}
.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 14px;
    }
}
</style>
