<template>
    <div class="top-selector-container">
        <div class="info">
            <!-- el-select组件，用于进行项目选择操作 -->
            <div class="info-tag middle-tag">
                <el-select
                    class="selector"
                    @change="handleChange"
                    :placeholder="placeholder"
                    v-model="selectValue"
                    ref="selectRef"
                    filterable
                    clearable
                >
                    <el-option
                        v-for="item in options"
                        :key="item.projectId"
                        :label="item.projectName"
                        :value="item.projectId"
                    >
                    </el-option>
                </el-select>
            </div>
        </div>
    </div>
</template>
<script>
/**
 * GroupSelector 组件
 * @desc 该组件用于展示信息的选择
 * @param {String} [placeholder]   - 选择器的占位符文本
 * @example 调用示例
 *  <GroupSelector :placeholder="placeholder" @input="handleChange"></GroupSelector>
 * */

export default {
    name: 'GroupSelector',
    props: {
        placeholder: {
            type: String,
            default: '请选择'
        }
    },
    data() {
        return {
            options: [],
            // 当前项目状态
            currentStatus: ''
        };
    },
    computed: {
        selectValue: {
            get() {
                return this.$store.state.dms.group;
            },
            set(val) {
                this.$emit('input', val);
            }
        }
    },
    mounted() {
        this.getOptions();
    },
    activated() {
        this.getOptions();
    },
    methods: {
        /**
         * 选择的值发生变化时触发
         * @param {string} value 选择的值
         */
        handleChange(value) {
            // 找到对应的完整选项对象
            const selectedOption = this.options.find((item) => item.projectId === value);
            this.$store.dispatch('dms/setGroup', value);
            // 将完整的选项对象存储到store中
            if (selectedOption) {
                this.$store.dispatch('dms/setGroupOption', selectedOption);
            }
            // 向父组件传递选中的值
            this.$emit('input', value);
        },
        /**
         * 获取选择项
         * 每次选择项变更之后都会进行一次查询
         */
        async getOptions() {
            const api = this.$service.dms.common.getProjectOrGroupList;
            const params = {
                projectCategory: 'T'
            };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.options = res.data || [];
                // 如果值有效，触发事件，便于查询
                if (this.selectValue) {
                    this.handleChange(this.selectValue);
                }
            } catch (error) {
                console.error(error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.top-selector-container {
    height: 45px;
    display: flex;
    margin: 10px 0 7px 18px;
}
.project-list-button {
    height: 34px;
    margin: 0 10px;
    flex: 1;
    font-weight: 400;
}
.info {
    background-color: #fff;
    margin-right: 10px;
    width: 380px;
}
.info-tag {
    height: 45px;
    display: flex;
    background: #f0f0f0;
    border-bottom: 1px solid #d8dce5;
    box-shadow: 0px 1px 3px 0 rgba(0, 0, 0, 0.12), 0px 0 3px 0 rgba(0, 0, 0, 0.04);
    justify-content: center;
    align-items: center;
    position: relative;
    .selector {
        margin: 0 10px 0 10px;
        flex: 1;
        ::v-deep .el-input--mini .el-input__inner {
            line-height: 34px;
            height: 34px;
            font-weight: 400;
        }
    }
}
.info-tag::after {
    content: '';
    position: absolute;
    right: -14px;
    top: 0px;
    width: 0;
    height: 0;
    border-top: 23px solid transparent;
    border-bottom: 23px solid transparent;
    border-left: 15px solid #f0f0f0;
    z-index: 2;
}
// 利用placeholder进行数据回显，修改字体颜色
::v-deep .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685) !important;
}
</style>
<style lang="scss">
// 隐藏单选框
.MaintenanceSelector-cascader .el-cascader-panel .el-radio__input {
    display: none;
}
.MaintenanceSelector-cascader .el-cascader-panel .el-cascader-node__postfix {
    top: 10px;
}
.MaintenanceSelector-cascader .el-cascader-menu__wrap {
    height: 300px;
}
</style>
