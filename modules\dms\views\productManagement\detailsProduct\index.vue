<template>
    <div class="add-product-container">
        <!-- 页签菜单 -->
        <el-menu default-active="0" class="" mode="horizontal" @select="handleTabSelect">
            <el-menu-item v-for="(tab, index) in tabsPageConfig.tabItems" :index="index.toString()" :key="tab">{{
                tab
            }}</el-menu-item>
        </el-menu>
        <!-- 动态组件区域 -->
        <component :is="currentComponent" :id="id"></component>
        <!-- 按钮 -->
        <div class="add-btn">
            <el-button @click="handleBack()">返回</el-button>
        </div>
    </div>
</template>
<script>
import baseInfo from './baseInfo/index.vue';
import productStructure from './productStructure/index.vue';
import connectionInfo from './connectionInfo/index.vue';

export default {
    name: 'AddProduct',
    components: {
        baseInfo,
        productStructure,
        connectionInfo
    },
    data() {
        return {
            currentComponent: 'baseInfo',
            tabsPageConfig: {
                activeName: '基本信息',
                tabItems: ['基本信息', '产品结构', '干系人']
            },
            id: ''
        };
    },
    created() {
        if (this.$route?.query?.id) {
            this.id = this.$route.query.id;
        }
    },
    methods: {
        handleTabSelect(index) {
            const components = ['baseInfo', 'productStructure', 'connectionInfo'];
            this.currentComponent = components[parseInt(index)];
        },
        // 返回上一页
        handleBack() {
            this.$router.back();
        }
    }
};
</script>
<style lang="scss" scoped>
.add-product-container {
    position: relative;
}
.add-btn {
    position: absolute;
    top: 5px;
    right: 10px;
    z-index: 1;
}
::v-deep .el-menu.el-menu--horizontal {
    display: flex;
    margin: 1vh 0 1vh 10px;
    border-bottom: 3px solid #3370ff;
    .el-menu-item {
        background-color: #fff;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
        color: #4377ee;
        height: 45px;
        line-height: 45px;
    }
    .el-menu-item.is-active {
        background-color: #4377ee;
        color: #fff;
    }
}
</style>
