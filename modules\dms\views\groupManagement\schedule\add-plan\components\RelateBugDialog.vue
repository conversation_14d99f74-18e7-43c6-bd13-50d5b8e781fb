<template>
    <div>
        <el-dialog title="关联bug" :visible.sync="dialogVisible" width="1100px" top="5vh" @close="closeDialog">
            <!-- 需求列表表格 -->
            <div class="Bug-table">
                <el-table
                    ref="BugTable"
                    :data="bugList"
                    class="dms-table"
                    height="400"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column header-align="center" prop="id" label="id" width="120" show-overflow-tooltip />
                    <el-table-column
                        header-align="center"
                        prop="title"
                        label="bug标题"
                        min-width="150"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="assignedToName"
                        label="责任人"
                        width="120"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="deadLine"
                        label="截止日期"
                        width="120"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        header-align="center"
                        align="center"
                        prop="openedBy"
                        label="创建者"
                        width="120"
                        show-overflow-tooltip
                    />
                </el-table>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'RelateBugDialog',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            // 需求列表
            bugList: [],
            // 选中的需求
            selectedBugs: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        }
    },
    watch: {
        visible(val) {
            if (val) {
                this.getBugList();
            }
        }
    },
    methods: {
        /**
         * 获取需求列表
         */
        async getBugList() {
            try {
                const params = {
                    projectId: this.groupValue
                };

                const res = await this.$service.dms.plan.getUnsolvedBugList(params);
                if (res.code === '0000') {
                    this.bugList = res.data;
                } else {
                    this.$message.error(res.message || '获取需求列表失败');
                }
            } catch (error) {
                console.error('获取需求列表失败:', error);
                this.$message.error('获取需求列表失败');
            }
        },

        /**
         * 表格选择变更
         */
        handleSelectionChange(selection) {
            this.selectedBugs = selection;
        },

        /**
         * 确认关联
         */
        handleConfirm() {
            if (this.selectedBugs.length === 0) {
                this.$message.warning('请选择要关联的bug');
                return;
            }

            this.$emit('confirm', this.selectedBugs);
            this.closeDialog();
        },

        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.resetData();
        },
        resetData() {
            this.selectedBugs = [];
            this.bugList = [];
        }
    }
};
</script>

<style lang="scss" scoped>
.search-form {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .el-form-item {
        margin-bottom: 10px;
    }

    .search-form-row {
        margin-top: 10px;
    }
}

.Bug-table {
    .pagination-wrapper {
        margin-top: 20px;
        text-align: right;
    }
}

.dialog-footer {
    text-align: right;

    .el-button {
        margin-left: 10px;
    }
}

::v-deep .el-dialog__body {
    padding: 20px;
}
</style>
