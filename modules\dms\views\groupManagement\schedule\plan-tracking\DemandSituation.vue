<template>
    <div class="searchable-nested-plan-list">
        <el-descriptions class="demand-type-numbers" :column="10">
            <el-descriptions-item
                v-for="item in demandConfig.items"
                :key="item.key"
                :label="item.label"
                :content-style="{ color: item.color }"
                >{{ item.value }}</el-descriptions-item
            >
        </el-descriptions>
        <!-- 搜索面板 -->
        <collapsible-search-panel
            v-model="activeNavName"
            :is-dot="false"
            :nav-items="navItems"
            :query-params="queryParams"
            :query-config="queryConfig"
            @nav-change="handleNavChange"
            @search="query"
            @reset="query"
        >
        </collapsible-search-panel>

        <!-- 嵌套计划列表 -->
        <NestedPlanInfoEvaluateList :list="list" :disabledAll="true"></NestedPlanInfoEvaluateList>
    </div>
</template>

<script>
import CollapsibleSearchPanel from 'dms/components/CollapsibleSearchPanel/index.vue';
import NestedPlanInfoEvaluateList from 'dms/views/groupManagement/schedule/plan-evalute-action/components/NestedPlanInfoEvaluateList.vue';

export default {
    name: 'DemandSituation',
    components: {
        CollapsibleSearchPanel,
        NestedPlanInfoEvaluateList
    },
    props: {},
    data() {
        return {
            activeNavName: '',
            navItems: [
                { field: '', name: '所有', queryField: 'status' },
                { field: '未关闭', name: '未关闭', queryField: 'status' },
                { field: '已延期', name: '已延期', queryField: 'status' },
                { field: '已完成', name: '完成', queryField: 'status' },
                { field: '已关闭', name: '关闭', queryField: 'status' }
            ],
            queryConfig: {},
            queryParams: {},
            demandConfig: {
                items: [
                    { label: '需求总数', value: 0, color: '#6090ee' },
                    { label: '按时完成', value: 0, color: '#70b603' },
                    { label: '延期完成', value: 0, color: '#f59a23' },
                    { label: '进行中', value: 0, color: '#6090ee' },
                    { label: '延期进行中', value: 0, color: '#cc0000' }
                ],
                config: {
                    elDescriptionsAttrs: { column: 10 }
                }
            }
        };
    },
    computed: {},
    watch: {},
    methods: {
        /**
         * 查询
         */
        async query() {
            if (!this.groupValue) {
                this.$message.warning('请选择团队');
                return;
            }
            let startDate = '';
            let endDate = '';
            const { dateRange } = this.queryParams;
            if (Array.isArray(dateRange) && dateRange[0]) {
                startDate = dateRange[0];
                endDate = dateRange[1];
            }

            try {
                const params = {
                    ...this.queryParams,
                    ...this.navQuery,
                    startDate,
                    endDate,
                    projectId: this.groupValue,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder,
                    currentPage: this.page,
                    pageSize: this.size
                };
                const api = this.$service.dms.plan.getPlanTrackDemandList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.planList = res.data.list;
                this.total = res.data.total || 0;
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.searchable-nested-plan-list {
    width: 100%;

    .nested-plan-list {
        margin-top: 10px;
    }
}
.demand-type-numbers {
    ::v-deep .el-descriptions-item__container {
        font-weight: 600;
        font-size: 14px;
    }
}
</style>
