<template>
    <div class="schedule-container">
        <!-- 团队列表 -->
        <GroupSelector class="group-selector"></GroupSelector>
        <!-- 页签菜单 -->
        <el-menu default-active="0" class="" mode="horizontal" @select="handleTabSelect">
            <el-menu-item v-for="(tab, index) in tabsPageConfig.tabItems" :index="index.toString()" :key="tab">{{
                tab
            }}</el-menu-item>
        </el-menu>
        <!-- 动态组件区域 -->
        <component :is="currentComponent"></component>
    </div>
</template>
<script>
import PlanTracking from './plan-tracking/index.vue';
import PlanManager from './plan-manager/index.vue';
import PlanReview from './plan-review/index.vue';
import SprintReview from './sprint-review/index.vue';
import GroupSelector from 'dms/components/TopSelectorGroup/GroupSelector.vue';

export default {
    name: 'Schedule',
    components: {
        PlanTracking,
        PlanManager,
        PlanReview,
        SprintReview,
        GroupSelector
    },
    data() {
        return {
            currentComponent: 'PlanManager',
            tabsPageConfig: {
                activeName: '计划管理',
                tabItems: ['计划管理', '计划跟踪', '计划审核', '冲刺评价']
            }
        };
    },
    methods: {
        handleTabSelect(index) {
            const components = ['PlanManager', 'PlanTracking', 'PlanReview', 'SprintReview'];
            this.currentComponent = components[parseInt(index)];
        }
    }
};
</script>
<style lang="scss" scoped>
.view {
    justify-content: flex-start;
}
.schedule-container {
    position: relative;
    padding: 20px;
}
.group-selector {
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
}
::v-deep .el-menu.el-menu--horizontal {
    display: flex;
    justify-content: center;
    margin: 1vh 0;
    border-bottom: 3px solid #3370ff;
    .el-menu-item {
        background-color: #fff;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
        color: #4377ee;
        height: 45px;
        line-height: 45px;
    }
    .el-menu-item.is-active {
        background-color: #4377ee;
        color: #fff;
    }
}
</style>
