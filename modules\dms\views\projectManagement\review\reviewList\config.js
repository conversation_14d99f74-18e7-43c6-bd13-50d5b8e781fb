import CommonItems from 'snbcCommon/common/form-items.js';
import dictData from 'dms/constant/dict';

const { select, input, dateRange, productLineSelector, departmentSelector } = CommonItems;

// 部门
const department = {
    ...departmentSelector,
    name: '部门',
    modelKey: 'orgCode'
};

// 产品线
const productLine = {
    ...productLineSelector,
    name: '产品线',
    modelKey: 'productLine'
};

// 项目名称
const projectName = {
    ...input,
    name: '项目名称',
    modelKey: 'projectName'
};

// 项目经理
const projectManager = {
    ...input,
    name: '项目经理',
    modelKey: 'pmName'
};

// 产品经理
const productManager = {
    ...input,
    name: '产品经理',
    modelKey: 'poName'
};

// 审批人
const checkUser = {
    ...input,
    name: '审批人',
    modelKey: 'checkUser'
};

// 提交日期
const applyDate = {
    ...dateRange,
    name: '提交日期',
    modelKey: 'applyDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 审批结果
const checkResult = {
    ...select,
    name: '审批结果',
    modelKey: 'checkResult',
    elOptions: dictData.reviewResultData
};

// 审批日期
const checkDate = {
    ...dateRange,
    name: '审批日期',
    modelKey: 'checkDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 开始时间
const startDate = {
    ...dateRange,
    name: '开始时间',
    modelKey: 'startDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 计划结束时间
const planEndDate = {
    ...dateRange,
    name: '计划结束时间',
    modelKey: 'planEndDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [
        department,
        productLine,
        projectName,
        projectManager,
        productManager,
        checkUser,
        applyDate,
        checkResult,
        checkDate,
        startDate,
        planEndDate
    ]
};

// 查询条件参数
export const queryParams = {
    orgCode: '',
    productLine: '',
    projectName: '',
    pmName: '',
    poName: '',
    checkUser: '',
    applyDate: [],
    checkResult: [],
    checkDate: [],
    startDate: [],
    planEndDate: []
};

// 导航栏配置
export const navItems = [
    { field: [], name: '所有', queryField: 'checkResult' },
    { field: ['待审核'], name: '待审核', queryField: 'checkResult' },
    { field: ['已通过'], name: '已通过', queryField: 'checkResult' },
    { field: ['已拒绝'], name: '已拒绝', queryField: 'checkResult' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        minWidth: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'checkId',
        label: 'ID',
        minWidth: 200,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'orgName',
        label: '部门',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'poName',
        label: '产品经理',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectName',
        label: '项目名称',
        minWidth: 200,
        showOverflowTooltip: true,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'pmName',
        label: '项目经理',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'applyDate',
        label: '提交日期',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'checkResult',
        label: '审批结果',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        sortable: 'custom',
        showOverflowTooltip: true
    },
    {
        prop: 'checkDate',
        label: '审批日期',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'startDate',
        label: '项目开始时间',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'planEndDate',
        label: '计划结束时间',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        showOverflowTooltip: true,
        columnManage: {
            sortableDisabled: true
        }
    }
];
