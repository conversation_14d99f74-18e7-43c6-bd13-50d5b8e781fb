<template>
    <el-cascader
        class="selector"
        @change="handleChange"
        :placeholder="placeholder"
        :props="props"
        v-model="currentValue"
        :options="options"
        v-bind="$attrs"
        ref="cascaderRef"
        popper-class="DepartmentSelector-cascader"
        filterable
    >
        <div slot-scope="{ data }" @click="clickNode" class="span-click" :class="data.disabled ? 'is-disabled' : ''">
            {{ data.orgName }}
        </div>
    </el-cascader>
</template>
<script>
/**
 * DepartmentSelector 组件
 * @desc 该组件用于展示信息的级联选择
 * @param {String} [placeholder]   - 级联选择器的占位符文本
 * @param {Array} [value] - 级联选择器当前选择的值，数组格式
 * @example 调用示例
 *  <DepartmentSelector :placeholder="placeholder" @input="handleChange"></DepartmentSelector>
 * */

export default {
    name: 'DepartmentSelector',
    props: {
        placeholder: {
            type: String,
            default: '请选择'
        },
        emitPath: {
            type: Boolean,
            default: false
        },
        value: {
            type: [String, Array],
            default: () => []
        }
    },
    data() {
        return {
            props: {
                // 是否可以直接选中父节点
                checkStrictly: true,
                // 配置展开方式
                expandTrigger: 'hover',
                // 悬停状态保持时间，小于这个时间不会触发hover事件
                hoverThreshold: 150,
                value: 'orgCode',
                label: 'orgName',
                emitPath: this.emitPath
            },
            options: [],
            // 当前项目状态
            currentStatus: ''
        };
    },
    computed: {
        currentValue: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            }
        }
    },
    mounted() {
        this.getOptions();
        // 挂载后向外传递ref
        this.$nextTick(() => {
            this.$emit('cascaderRef', this.$refs.cascaderRef);
        });
    },
    activated() {
        this.getOptions();
    },
    methods: {
        /**
         * 通过点击文字选中的处理函数
         * @param {Object} e 事件对象
         */
        clickNode(e) {
            // 模拟点击对应的radio
            e.target.parentElement.parentElement.firstElementChild.click();
        },
        /**
         * 选择的值发生变化时触发
         * @param {Array} value 选择的值
         */
        handleChange(value) {
            // 向父组件传递选中的值
            this.$emit('input', value);
            // 每次选择结束之后自动关闭
            if (this.$refs?.cascaderRef?.dropDownVisible) {
                this.$refs.cascaderRef.dropDownVisible = false;
            }
        },
        /**
         * 获取选择项
         * 每次选择项变更之后都会进行一次查询
         */
        async getOptions() {
            const api = this.$service.dms.common.getFirstAndSecondLevelDepartment;
            const params = {
                orgCode: '0002'
            };
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.options = res.data;
                // 如果值有效，触发事件，便于查询
                if (this.currentValue.length !== 0) {
                    this.handleChange(this.currentValue);
                }
            } catch (error) {
                console.error(error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.top-selector-container {
    height: 45px;
    display: flex;
    margin: 10px 0 7px 18px;
}
.project-list-button {
    height: 34px;
    margin: 0 10px;
    flex: 1;
    font-weight: 400;
}
.info {
    background-color: #fff;
    margin-right: 10px;
    width: 380px;
}
.info-tag {
    height: 45px;
    display: flex;
    background: #f0f0f0;
    border-bottom: 1px solid #d8dce5;
    box-shadow: 0px 1px 3px 0 rgba(0, 0, 0, 0.12), 0px 0 3px 0 rgba(0, 0, 0, 0.04);
    justify-content: center;
    align-items: center;
    position: relative;
    .selector {
        margin: 0 10px 0 10px;
        flex: 1;
        ::v-deep .el-input--mini .el-input__inner {
            line-height: 34px;
            height: 34px;
            font-weight: 400;
        }
    }
}
.info-tag::after {
    content: '';
    position: absolute;
    right: -14px;
    top: 0px;
    width: 0;
    height: 0;
    border-top: 23px solid transparent;
    border-bottom: 23px solid transparent;
    border-left: 15px solid #f0f0f0;
    z-index: 2;
}
.span-click {
    width: 100%;
}
.span-click.is-disabled {
    color: rgb(192, 196, 204);
}
</style>
<style lang="scss">
// 隐藏单选框
.DepartmentSelector-cascader .el-cascader-panel .el-radio__input {
    display: none;
}
.DepartmentSelector-cascader .el-cascader-panel .el-cascader-node__postfix {
    top: 10px;
}
.DepartmentSelector-cascader .el-cascader-menu__wrap {
    height: 300px;
}
</style>
