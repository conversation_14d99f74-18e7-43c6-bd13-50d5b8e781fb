// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: []
};

// 查询条件参数
export const queryParams = {
    dateRange: [],
    quickQuery: '最近一年'
};

// 表格列配置 - 根据你的需求定制
export const tableColumns = [
    {
        label: '任务ID',
        prop: 'taskId',
        minWidth: 200,
        attrs: { align: 'left' }
    },
    {
        label: '任务名称',
        prop: 'taskName',
        minWidth: 200,
        attrs: { align: 'left' }
    },
    {
        label: '责任人',
        prop: 'assignedTo',
        minWidth: 120,
        attrs: { align: 'center' }
    },
    {
        label: '状态',
        prop: 'taskStatus',
        minWidth: 120,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '进度',
        prop: 'schedule',
        minWidth: 120,
        attrs: { align: 'center' }
    },

    {
        label: '计划开始日期',
        prop: 'startDate',
        minWidth: 140,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '计划完成日期',
        prop: 'endDate',
        minWidth: 140,
        attrs: { align: 'center' }
    },
    {
        label: '预计',
        prop: 'estimate',
        minWidth: 120,
        attrs: { align: 'center' }
    },
    {
        label: '消耗',
        prop: 'consumed',
        minWidth: 100,
        attrs: { align: 'center' }
    },
    {
        label: '剩余',
        prop: 'left',
        minWidth: 140,
        attrs: { align: 'center' }
    }
];

// 导航标签配置 - tabsConfig中的tabItems
export const navItems = [
    { field: '', name: '所有', queryField: 'status' },
    { field: '未关闭', name: '未关闭', queryField: 'status' },
    { field: '已延期', name: '已延期', queryField: 'status' },
    { field: '已完成', name: '已完成', queryField: 'status' },
    { field: '已关闭', name: '已关闭', queryField: 'status' },
    { field: '已取消', name: '已取消', queryField: 'status' }
];

// 表格字段与后端接口字段的映射
export const sortMap = {
    status: 'status',
    startDate: 'estStarted'
};
