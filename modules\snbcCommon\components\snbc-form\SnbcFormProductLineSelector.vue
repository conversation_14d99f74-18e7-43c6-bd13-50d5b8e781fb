<!-- 产品线选择器 - 带版本选择 -->
<template>
    <el-form-item v-bind="elFormItemAttrs">
        <div>
            <el-select
                v-model="config.modelObj[config.modelKey]"
                v-bind="elSelectAttrs"
                @change="handleChange"
                clearable
            >
                <el-radio-group v-model="version" @change="handleVersionChange">
                    <template v-if="versionInfo.length > 1">
                        <el-radio-button
                            v-for="(item, index) in versionInfo"
                            :key="index"
                            :label="item.version"
                        ></el-radio-button>
                    </template>
                </el-radio-group>
                <el-option
                    v-for="item in productLineOptions"
                    :key="item.productLine"
                    :label="item.productLine"
                    :value="item.productLine"
                ></el-option>
            </el-select>
        </div>
    </el-form-item>
</template>

<script>
export default {
    name: 'SnbcFormProductLineSelector',
    props: {
        /**
         * SnbcFormProductLineSelector组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elSelectAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-select组件默认属性设置
            defaultElSelectAttrs: {
                filterable: true,
                clearable: true,
                placeholder: '请选择产品线',
                style: 'width: 100%'
            },
            // 版本
            version: '',
            // 全部版本信息
            versionInfo: []
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-select组件应用属性
        elSelectAttrs() {
            return {
                ...this.defaultElSelectAttrs,
                ...(this.config.elSelectAttrs || {})
            };
        },
        // 产品线下拉选项
        productLineOptions() {
            return this.versionInfo.find((i) => i.version === this.version)?.children || [];
        }
    },
    created() {
        this.getProductOptions();
    },
    methods: {
        async getProductOptions() {
            const params = {};
            const api = this.$service.dms.common.getProductLineList;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.versionInfo = res.data;
                this.version = this.versionInfo[0]?.version;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        // 产品线切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        },
        // 版本切换操作
        handleVersionChange() {
            // 版本切换时清空产品线选择
            this.config.modelObj[this.config.modelKey] = '';
            this.config.versionChangeHandler && this.config.versionChangeHandler(this.version);
        }
    }
};
</script>

<style lang="scss" scoped>
.el-radio-group {
    text-align: right;
    width: 100%;
    padding-right: 10px;
    margin-bottom: 5px;
}
</style>
