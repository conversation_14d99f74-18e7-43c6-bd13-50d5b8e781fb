<template>
    <div class="project-management-list">
        <div class="content query-label-line2">
            <project-list
                v-model="activeNavTab"
                :nav-items="navItems"
                :query-params="queryParams"
                :query-config="queryConfig"
                :columns="columns"
                :data="productData"
                :load-data="query"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                :table-attrs="{ height: 'calc(100vh - 190px)' }"
                config-section="projectList"
                :actions-width="100"
                @search="handleSearch"
                @reset="handleReset"
                @nav-change="handleNavChange"
                @pagination="query"
            >
                <template #rightNav>
                    <div class="right-nav-container">
                        <el-button
                            type="text"
                            v-if="addTemporaryProjectPermission"
                            @click="addTemporaryProject"
                            class="create-demand-button"
                            ><i class="el-icon-plus"></i> 新增临时项目
                        </el-button>
                        <el-button
                            v-if="addFormalProjectPermission"
                            type="text"
                            @click="addFormalProject"
                            class="create-demand-button"
                            ><i class="el-icon-plus"></i> 新增正式项目
                        </el-button>
                        <el-button type="text" @click="downloadProjectInfo" class="create-demand-button"
                            ><i class="el-icon-download"></i> 下载项目信息
                        </el-button>
                    </div>
                </template>
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button type="text" size="small" @click="handleView(row)">详情</el-button>
                    <!-- 这个页面只有正式项目可以编辑?? -->
                    <el-button
                        v-if="row.enableStatus === '正式' && updateAllProjectPermission"
                        type="text"
                        size="small"
                        @click="handleEdit(row)"
                        >编辑</el-button
                    >
                </template>
            </project-list>
        </div>

        <!-- 新增临时项目弹窗 -->
        <TemporaryProjectDialog
            :type="temporaryProjectDialogType"
            :visible.sync="temporaryProjectDialogVisible"
            :projectId="currentProjectId"
            @success="query"
        />
        <!-- 新增正式项目弹窗 -->
        <FormalProjectDialog
            :type="formalProjectDialogType"
            :visible.sync="formalProjectDialogVisible"
            :projectId="currentProjectId"
            @success="query"
        />
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import TemporaryProjectDialog from './components/TemporaryProjectDialog.vue';
import FormalProjectDialog from './components/FormalProjectDialog.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';
import { syncNavFromSearch } from 'dms/mixins/common.js';

export default {
    name: 'ProjectManagementList',
    components: {
        ProjectList,
        TemporaryProjectDialog,
        FormalProjectDialog
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '进行中',
            // 导航栏配置
            navItems,
            // 查询参数
            params: { projectStatus: '进行中' },
            // 查询参数
            queryParams,
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [],
            // 分页配置
            total: 0,
            page: 1,
            limit: 20,
            // 当前选中的项目ID
            currentProjectId: null,
            // 正式项目新增/编辑类型
            formalProjectDialogType: 'add',
            // 临时项目新增/详情类型
            temporaryProjectDialogType: 'add',
            // 新增/编辑临时项目弹窗
            temporaryProjectDialogVisible: false,
            // 新增/编辑正式项目弹窗
            formalProjectDialogVisible: false
        };
    },
    computed: {
        updateAllProjectPermission() {
            return this.$store.state.permission.btnDatas.includes('Button-updateAllProject');
        },
        addTemporaryProjectPermission() {
            return this.$store.state.permission.btnDatas.includes('Button-addTemporaryProject');
        },
        addFormalProjectPermission() {
            return this.$store.state.permission.btnDatas.includes('Button-addFormalProject');
        }
    },
    created() {
        this.query();
    },
    methods: {
        // 加载产品数据
        async query() {
            try {
                const params = {
                    currentPage: this.page,
                    pageSize: this.limit,
                    projectCategory: 'P',
                    ...this.params
                };

                // 日期处理
                if (params.startDate && params.startDate.length > 0) {
                    params.startDateStart = params.startDate[0];
                    params.startDateEnd = params.startDate[1];
                }
                if (params.endDate && params.endDate.length > 0) {
                    params.endDateStart = params.endDate[0];
                    params.endDateEnd = params.endDate[1];
                }

                if (params.planEndDate && params.planEndDate.length > 0) {
                    params.planEndDateStart = params.planEndDate[0];
                    params.planEndDateEnd = params.planEndDate[1];
                }

                const res = await this.$service.dms.project.getProjectList(params);

                if (res.code === '0000') {
                    this.productData = res.data?.list || [];
                    // 添加序号
                    if (res?.data?.startRow) {
                        this.productData = this.productData.map((item, index) => {
                            item.index = res.data.startRow + index;
                            return item;
                        });
                    }
                    this.total = res.data?.total || 0;
                } else {
                    this.$message.error(res.message || '获取产品列表失败');
                }
            } catch (error) {
                console.error('加载产品数据失败:', error);
                this.$message.error('加载产品数据失败');
            }
        },

        // 处理搜索
        handleSearch() {
            this.params = this.queryParams;
            this.page = 1;
            // 搜索时同步导航状态 - 根据搜索条件自动设置对应的导航标签
            syncNavFromSearch(this);
            this.query();
        },

        // 处理重置
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
            // 重置时重置到第一页
            this.page = 1;
            this.query();
        },

        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.queryParams[queryField] = field;
            this.params = this.$tools.cloneDeep(this.queryParams);
            this.query();
        },

        // 编辑产品
        handleEdit(row) {
            this.currentProjectId = row.projectId;
            if (row.enableStatus === '正式') {
                this.formalProjectDialogType = 'edit';
                this.formalProjectDialogVisible = true;
            } else {
                this.temporaryProjectDialogType = 'edit';
                this.temporaryProjectDialogVisible = true;
            }
        },

        // 查看产品详情
        handleView(row) {
            this.currentProjectId = row.projectId;
            if (row.enableStatus === '正式') {
                this.formalProjectDialogType = 'view';
                this.formalProjectDialogVisible = true;
            } else {
                this.temporaryProjectDialogType = 'view';
                this.temporaryProjectDialogVisible = true;
            }
        },

        /**
         * 新增临时项目
         */
        addTemporaryProject() {
            this.temporaryProjectDialogType = 'add';
            this.currentProjectId = null;
            this.temporaryProjectDialogVisible = true;
        },

        /**
         * 新增正式项目
         */
        addFormalProject() {
            this.formalProjectDialogVisible = true;
            this.currentProjectId = null;
            this.formalProjectDialogType = 'add';
        },

        /**
         * 下载项目信息
         */
        downloadProjectInfo() {}
    }
};
</script>

<style lang="scss" scoped>
.project-management-list {
    padding: 20px;
}
.right-nav-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 10px;
}
</style>
