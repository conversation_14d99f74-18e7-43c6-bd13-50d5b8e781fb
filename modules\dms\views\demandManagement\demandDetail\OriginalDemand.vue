<template>
    <div>
        <el-dialog title="需求详情" :visible.sync="dialogVisible" width="85%" top="5vh">
            <div class="pre-line">
                <!-- 基本信息 -->
                <div class="title">基本信息</div>

                <el-descriptions :column="3">
                    <el-descriptions-item label="需求ID">{{ form.id || '-' }} </el-descriptions-item>
                    <el-descriptions-item label="需求名称">{{ form.storyName || '-' }} </el-descriptions-item>
                    <el-descriptions-item label="需求来源">{{ form.source || '-' }} </el-descriptions-item>
                    <el-descriptions-item label="来源编号">{{ form.sourceNo || '-' }} </el-descriptions-item>
                    <el-descriptions-item label="提出时间">{{ form.proposalDate || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="提出人">{{ form.proposer || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="期望交付日期">{{ form.deliveryDate || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="负责人">{{ form.ownerName || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="需求类型">{{ form.storyType || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="优先级">{{ form.priority || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="需求状态">{{ form.status || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="变更次数">{{ form.changeNo || '-' }}</el-descriptions-item>
                </el-descriptions>
                <!-- 进度成本 -->
                <div class="title">进度成本</div>
                <el-descriptions :column="4">
                    <el-descriptions-item label="预估成本">{{
                        form.estimateCost ? `${form.estimateCost}人天` : '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="实际成本">{{
                        form.realCost ? `${form.realCost}人天` : '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="成本偏差">{{
                        form.costDeviation !== '-' && form.costDeviation ? `${form.costDeviation}%` : '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="需求进度">{{
                        form.storyProgress ? `${form.storyProgress}%` : '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="发布情况">{{ form.publish || '-' }}</el-descriptions-item>
                    <el-tions-idescriptions-item label="部署情况">{{ form.deploy || '-' }}</el-tions-idescriptions-item>
                </el-descriptions>

                <!-- 里程碑节点 -->
                <div class="title" v-if="form.projectTaskList.length > 0">里程碑节点</div>
                <el-table
                    v-if="form.projectTaskList.length > 0"
                    class="dms-table"
                    :data="form.projectTaskList"
                    style="width: 100%; margin-bottom: 24px; font-size: 13px"
                >
                    <el-table-column prop="taskName" label="任务名称" align="center"></el-table-column>
                    <el-table-column prop="contactName" label="责任人" width="120" align="center"></el-table-column>
                    <el-table-column
                        prop="planFinishDate"
                        label="交付日期"
                        width="150"
                        align="center"
                    ></el-table-column>
                </el-table>

                <!-- 关联信息 -->
                <div class="title" v-if="form.productVoList.length > 0">关联信息</div>
                <el-table
                    v-if="form.productVoList.length > 0"
                    class="dms-table"
                    :data="form.productVoList"
                    style="width: 100%; margin-bottom: 24px; font-size: 13px"
                >
                    <el-table-column :label="`产品线：${form.productLine}`" header-align="center">
                        <el-table-column prop="index" label="序号" width="120" align="center"></el-table-column>
                        <el-table-column prop="productName" label="产品" align="center"></el-table-column>
                        <el-table-column prop="projectName" label="项目/团队" align="center"></el-table-column>
                    </el-table-column>
                </el-table>

                <!-- 需求详情 -->
                <div class="title">需求详情</div>
                <el-descriptions :column="1">
                    <el-descriptions-item label="变更原因" v-if="form.reason">{{
                        form.reason || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="用户场景">{{ form.scene || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="需求描述">{{ form.description || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="业务价值">{{ form.businessValue || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="预期收益">{{ form.earnings || '-' }}</el-descriptions-item>
                </el-descriptions>

                <!-- 附件列表 -->
                <div class="title">附件列表</div>
                <div v-if="form.fileVoList && form.fileVoList.length > 0" class="file-list">
                    <div v-for="(file, index) in form.fileVoList" :key="index" class="file-item">
                        <div class="file-info">
                            <i class="el-icon-document"></i>
                            <span class="file-name">{{ file.title }}</span>
                        </div>
                        <el-button type="text" @click="downloadFile(file)" class="download-btn">
                            <i class="el-icon-download"></i>
                            下载
                        </el-button>
                    </div>
                </div>
                <div v-else class="no-files">
                    <i class="el-icon-document"></i>
                    <span>暂无附件</span>
                </div>

                <!-- 历史版本 -->
                <div class="title">历史版本</div>

                <el-select
                    v-model="selectedVersion"
                    placeholder="请选择版本"
                    style="margin-bottom: 16px"
                    @change="onVersionChange"
                >
                    <el-option
                        v-for="item in form.versionVoList"
                        :key="item.version"
                        :label="`V${item.version}`"
                        :value="item.version"
                    ></el-option>
                </el-select>
                <el-descriptions v-if="selectedVersion" :column="4">
                    <el-descriptions-item label="提出时间">{{
                        currentVersionInfo.proposalDate || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="需求名称">{{
                        currentVersionInfo.storyName || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="期望交付日期">{{
                        currentVersionInfo.deliveryDate || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="责任人">{{
                        currentVersionInfo.ownerName || '-'
                    }}</el-descriptions-item>
                </el-descriptions>
                <el-descriptions v-if="selectedVersion" :column="1">
                    <el-descriptions-item label="变更原因" v-if="currentVersionInfo.reason">{{
                        currentVersionInfo.reason || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="用户场景">{{ currentVersionInfo.scene || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="需求描述">{{
                        currentVersionInfo.description || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="业务价值">{{
                        currentVersionInfo.businessValue || '-'
                    }}</el-descriptions-item>
                    <el-descriptions-item label="预期收益">{{
                        currentVersionInfo.earnings || '-'
                    }}</el-descriptions-item>
                </el-descriptions>
            </div>
            <div slot="footer"></div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'OriginalDemand',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        demandId: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            form: {
                id: '',
                storyName: '',
                storyClass: '',
                source: '',
                sourceNo: '',
                proposalDate: '',
                proposer: '',
                deliveryDate: '',
                owner: '',
                ownerName: '',
                storyType: '',
                productLine: '',
                priority: '',
                status: '',
                changeNo: '',
                version: '',
                estimateCost: '',
                realCost: '',
                costDeviation: '',
                storyProgress: '',
                publish: '',
                deploy: '',
                storyLevel: '',
                moduleName: '',
                scene: '',
                verify: '',
                description: '',
                businessValue: '',
                earnings: '',
                reason: '',
                // 里程碑数据
                projectTaskList: [],
                // 关联产品信息
                productVoList: [],
                // 历史版本数据
                versionVoList: [],
                // 附件信息
                fileVoList: []
            },
            version: '',
            selectedVersion: null
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        currentVersionInfo() {
            return this.form.versionVoList.find((i) => i.version === this.selectedVersion);
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getFromData();
            }
        }
    },
    methods: {
        async getFromData() {
            if (!this.demandId) {
                this.$message.error('需求ID不能为空');
                return;
            }

            try {
                const params = {
                    id: this.demandId
                };
                const api = this.$service.dms.demand.getDemandDetail;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message || '获取需求详情失败');
                    return;
                }

                const { data } = res;
                if (!data) {
                    this.$message.error('获取需求详情失败：数据为空');
                    return;
                }

                // 赋值基本信息
                if (data.baseInfo) {
                    const { baseInfo } = data;
                    this.form.id = baseInfo.id || '';
                    this.form.storyName = baseInfo.storyName || '';
                    this.form.storyClass = baseInfo.storyClass || '';
                    this.form.source = baseInfo.source || '';
                    this.form.sourceNo = baseInfo.sourceNo || '';
                    this.form.proposalDate = baseInfo.proposalDate || '';
                    this.form.proposer = baseInfo.proposer || '';
                    this.form.deliveryDate = baseInfo.deliveryDate || '';
                    this.form.owner = baseInfo.owner || '';
                    this.form.ownerName = baseInfo.ownerName || '';
                    this.form.storyType = baseInfo.storyType || '';
                    this.form.productLine = baseInfo.productLine || '';
                    this.form.priority = baseInfo.priority || '';
                    this.form.status = baseInfo.status || '';
                    this.form.changeNo = baseInfo.changeNo;
                    this.form.version = baseInfo.version || '';
                    this.form.estimateCost = baseInfo.estimateCost || '';
                    this.form.realCost = baseInfo.realCost;
                    this.form.costDeviation = baseInfo.costDeviation;
                    this.form.storyProgress = baseInfo.storyProgress || '';
                    this.form.publish = baseInfo.publish || '';
                    this.form.deploy = baseInfo.deploy || '';
                    this.form.storyLevel = baseInfo.storyLevel || '';
                    this.form.moduleName = baseInfo.moduleName || '';
                }

                // 赋值扩展信息
                if (data.extendInfo) {
                    const { extendInfo } = data;
                    this.form.description = extendInfo.description || '';
                    this.form.verify = extendInfo.verify || '';
                    this.form.scene = extendInfo.scene || '';
                    this.form.businessValue = extendInfo.businessValue || '';
                    this.form.earnings = extendInfo.earnings || '';
                    this.form.reason = extendInfo.reason || '';
                }

                // 赋值里程碑数据
                this.form.projectTaskList = data.projectTaskList || [];

                // 赋值关联产品信息
                this.form.productVoList =
                    data.productVoList.map((i, index) => ({
                        index: index + 1,
                        ...i
                    })) || [];

                // 赋值历史版本数据
                this.form.versionVoList = data.versionVoList || [];

                // 赋值附件信息
                this.form.fileVoList = data.fileVoList || [];
            } catch (error) {
                console.error('获取需求详情失败:', error);
            }
        },
        /**
         * 版本选择变更
         * @param {Object} version 版本对象
         */
        onVersionChange(version) {
            this.selectedVersion = version;
        },
        /**
         * 下载文件
         * @param {Object} file 文件对象
         */
        async downloadFile(file) {
            try {
                const params = {
                    fileId: file.id
                };
                const fileName = file.title.split('.')[0];
                const fileExtension = file.title.split('.').pop();
                const stream = await this.$service.dms.original.xzFile({ ...params });
                this.$tools.downloadExprotFile(stream, fileName, fileExtension).catch((e) => {
                    this.$message.error(e || '下载失败');
                });
            } catch (error) {
                console.error('下载文件失败:', error);
                this.$message.error('下载失败');
            }
        },
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.selectedVersion = null;
            this.selectedVersion = null;
            this.$emit('update');
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'dms/views/demandManagement/common/common.scss';

.title {
    @include section-title;
    margin-bottom: 16px;
    margin-top: 32px;
    font-size: 15px;
    &:first-child {
        margin-top: 0;
    }
}

.flex {
    display: flex;
}

.info-card {
    margin-bottom: 16px;

    .card-header {
        .card-title {
            font-weight: bold;
            font-size: 14px;
            color: #303133;
        }
    }

    .text-content {
        white-space: pre-wrap;
        word-break: break-word;
        line-height: 1.6;
        min-height: 60px;
        color: #303133;
    }
}

::v-deep .el-card__header {
    padding: 12px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

::v-deep .el-card__body {
    padding: 20px;
}

::v-deep .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

::v-deep .el-descriptions {
    margin-bottom: 20px;

    .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        background-color: #fafbfc;
        width: 120px;
    }

    .el-descriptions__content {
        color: #303133;
        word-break: break-word;
    }
}

::v-deep.form .el-form-item__label {
    font-weight: bold;
}

// 表格样式
::v-deep .el-table {
    .el-table__header-wrapper {
        th {
            background-color: #fafbfc;
            color: #606266;
            font-weight: 600;
        }
    }

    .el-table__body-wrapper {
        td {
            color: #303133;
        }
    }

    .el-table__border {
        border-color: #ebeef5;
    }

    th,
    td {
        border-color: #ebeef5;
    }
}
.el-descriptions {
    margin: 0px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 13px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 13px;
    }
}

// 附件列表样式
.file-list {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 8px;
    background-color: #fafbfc;

    .file-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
            border-bottom: none;
        }

        .file-info {
            display: flex;
            align-items: center;
            flex: 1;

            .el-icon-document {
                color: #409eff;
                margin-right: 8px;
                font-size: 16px;
            }

            .file-name {
                color: #303133;
                font-size: 14px;
                margin-right: 8px;
                word-break: break-all;
            }

            .file-size {
                color: #909399;
                font-size: 12px;
            }
        }

        .download-btn {
            color: #409eff;
            padding: 4px 8px;

            &:hover {
                background-color: #ecf5ff;
                border-radius: 4px;
            }

            .el-icon-download {
                margin-right: 4px;
            }
        }
    }
}

.no-files {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #909399;
    font-size: 13px;
    border: 1px dashed #dcdfe6;
    border-radius: 4px;
    background-color: #fafbfc;

    .el-icon-document {
        margin-right: 8px;
        font-size: 18px;
    }
}

::v-deep .dms-table.el-table .el-table__row .cell {
    font-size: 13px;
}
</style>
