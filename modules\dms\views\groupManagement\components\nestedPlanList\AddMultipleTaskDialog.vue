<template>
    <div>
        <el-dialog
            title="快速创建任务"
            :visible.sync="dialogVisible"
            width="800px"
            top="5vh"
            @close="closeDialog"
            class="add-multiple-task-dialog"
        >
            <div class="dialog-content">
                <div class="subtitle">选择任务类型&责任人</div>

                <!-- 任务类型选择 -->
                <div class="selection-section">
                    <div class="section-title">任务类型：</div>
                    <div class="checkbox-group">
                        <el-checkbox
                            v-model="selectAll.taskType"
                            @change="handleSelectAllTaskType"
                            class="select-all-checkbox"
                        >
                            全选
                        </el-checkbox>
                        <div class="checkbox-row">
                            <el-checkbox
                                v-for="item in taskTypeList"
                                :key="item.value"
                                v-model="selectedTaskTypes"
                                :label="item.label"
                                @change="handleTaskTypeChange"
                            >
                                {{ item.label }}
                            </el-checkbox>
                        </div>
                    </div>
                </div>

                <!-- 责任人选择 -->
                <div class="selection-section">
                    <div class="section-title">责任人：</div>
                    <div class="checkbox-group">
                        <el-checkbox
                            v-model="selectAll.assignee"
                            @change="handleSelectAllAssignee"
                            class="select-all-checkbox"
                        >
                            全选
                        </el-checkbox>
                        <div class="checkbox-grid">
                            <el-checkbox
                                v-for="item in assigneeList"
                                :key="item.loginName"
                                v-model="selectedAssignees"
                                :label="item.loginName"
                                @change="handleAssigneeChange"
                            >
                                {{ item.employeeName }}
                            </el-checkbox>
                        </div>
                    </div>
                </div>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import dictData from 'dms/constant/dict.js';

export default {
    name: 'AddMultipleTaskDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        // 责任人列表
        assigneeList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            taskTypeList: dictData.quickCreateTaskTypeData,
            // 选中的任务类型
            selectedTaskTypes: [],
            // 选中的责任人
            selectedAssignees: [],
            // 全选状态
            selectAll: {
                taskType: false,
                assignee: false
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(val) {
            if (val) {
                this.initDialog();
            }
        }
    },
    methods: {
        /**
         * 初始化对话框
         */
        initDialog() {
            // 默认选中设计和开发
            this.selectedTaskTypes = [];
            this.selectedAssignees = [];
            this.updateSelectAllStatus();
        },

        /**
         * 处理任务类型全选
         */
        handleSelectAllTaskType(checked) {
            if (checked) {
                this.selectedTaskTypes = this.taskTypeList.map((item) => item.value);
            } else {
                this.selectedTaskTypes = [];
            }
        },

        /**
         * 处理责任人全选
         */
        handleSelectAllAssignee(checked) {
            if (checked) {
                this.selectedAssignees = this.assigneeList.map((item) => item.loginName);
            } else {
                this.selectedAssignees = [];
            }
        },

        /**
         * 任务类型变更处理
         */
        handleTaskTypeChange() {
            this.updateSelectAllStatus();
        },

        /**
         * 责任人变更处理
         */
        handleAssigneeChange() {
            this.updateSelectAllStatus();
        },

        /**
         * 更新全选状态
         */
        updateSelectAllStatus() {
            this.selectAll.taskType = this.selectedTaskTypes.length === this.taskTypeList.length;
            this.selectAll.assignee = this.selectedAssignees.length === this.assigneeList.length;
        },

        /**
         * 确认创建
         */
        handleConfirm() {
            if (this.selectedTaskTypes.length === 0) {
                this.$message.warning('请选择任务类型');
                return;
            }
            if (this.selectedAssignees.length === 0) {
                this.$message.warning('请选择责任人');
                return;
            }

            const result = {
                taskTypes: this.selectedTaskTypes,
                assignees: this.selectedAssignees
            };

            this.$emit('confirm', result);
            this.closeDialog();
        },

        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.resetData();
        },

        /**
         * 重置数据
         */
        resetData() {
            this.selectedTaskTypes = [];
            this.selectedAssignees = [];
            this.selectAll = {
                taskType: false,
                assignee: false
            };
        }
    }
};
</script>

<style lang="scss" scoped>
.add-multiple-task-dialog {
    ::v-deep .el-dialog__body {
        padding: 20px;
    }
}

.dialog-content {
    .subtitle {
        color: #666;
        font-size: 14px;
        margin-bottom: 20px;
    }
}

.selection-section {
    margin-bottom: 30px;

    .section-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 600;
    }
}

.checkbox-group {
    .select-all-checkbox {
        margin-bottom: 15px;

        ::v-deep .el-checkbox__label {
            font-weight: 500;
        }
    }

    .checkbox-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .el-checkbox {
            margin-right: 0;
            margin-bottom: 10px;
            min-width: 80px;
        }
    }

    .checkbox-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px 20px;

        .el-checkbox {
            margin-right: 0;
            margin-bottom: 0;
        }
    }
}

.dialog-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #eee;

    .el-button {
        min-width: 80px;
        margin: 0 10px;
    }
}

::v-deep .el-checkbox__label {
    color: #333;
    font-size: 14px;
}
</style>
