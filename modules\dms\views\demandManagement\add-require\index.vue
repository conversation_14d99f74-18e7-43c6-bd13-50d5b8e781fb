<template>
    <div class="view-box">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button plain @click="confirm('save')">保存</el-button>
            <el-button type="primary" @click="confirm('confirm')">提交</el-button>
            <el-button type="info" @click="handleBack()">返回</el-button>
        </div>
        <div>
            <!-- 标题 -->
            <formula-title :title="getTitleByType()"></formula-title>
            <el-form :model="addForm" ref="dataForm" label-width="120px" :rules="userRules">
                <div class="add-flex">
                    <el-form-item label="需求名称" prop="storyName">
                        <el-input
                            v-model="addForm.storyName"
                            placeholder="请输入需求名称"
                            clearable
                            style="width: 700px"
                            maxlength="64"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="类型" prop="storyType">
                        <el-select v-model="addForm.storyType" placeholder="请选择类型" clearable>
                            <el-option
                                v-for="item in typeData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div class="add-flex">
                    <el-form-item label="需求负责人" prop="owner">
                        <el-select
                            v-model="addForm.owner"
                            placeholder="请选择需求负责人"
                            style="width: 360px"
                            clearable
                            multiple
                            filterable
                        >
                            <el-option
                                v-for="item in ownerData"
                                :key="item.loginName"
                                :label="item.employeeName"
                                :value="item.loginName"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="期望交付日期" prop="deliveryDate">
                        <el-date-picker
                            v-model="addForm.deliveryDate"
                            type="date"
                            placeholder="请选择期望交付日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="优先级" prop="priority">
                        <el-select v-model="addForm.priority" placeholder="请选择优先级" clearable>
                            <el-option
                                v-for="item in priorityData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <el-form-item label="用户场景" prop="scene">
                    <el-input
                        type="textarea"
                        v-model="addForm.scene"
                        maxlength="500"
                        :rows="4"
                        placeholder="建议参考的模板：当<具体情境/触发条件>时，<角色名称>需要通过<当前方式>完成<目标任务>，但存在<痛点问题>"
                    ></el-input>
                </el-form-item>
                <el-form-item label="需求描述" prop="description">
                    <el-input
                        type="textarea"
                        v-model="addForm.description"
                        placeholder="建议参考的模板：作为<角色>，我希望<功能/能力>，以便<达成目标>"
                        maxlength="500"
                        :rows="4"
                    ></el-input>
                </el-form-item>
                <el-form-item label="业务价值" prop="businessValue">
                    <el-input
                        type="textarea"
                        v-model="addForm.businessValue"
                        placeholder="建议参考的模板：解决<战略目标/业务问题>，预计影响<涉及部门/流程>"
                        maxlength="500"
                        :rows="4"
                    ></el-input>
                </el-form-item>
                <el-form-item label="预期收益" prop="earnings">
                    <el-input
                        type="textarea"
                        v-model="addForm.earnings"
                        placeholder="建议参考的模板：直接收益：<效率/成本/收入等>提升<X%>，折合<金额/人天>；间接收益：<用户体验/风险降低等>；投资回报周期预计<时间>"
                        maxlength="500"
                        :rows="4"
                    ></el-input>
                </el-form-item>
                <el-form-item label="附件">
                    <upload-file
                        :uploadUrl="uploadUrl"
                        :headers="headers"
                        :maxSize="maxSize"
                        :accept="accept"
                        :maxCount="maxCount"
                        :initialFileList="initialFileList"
                        @upload-success="handleUploadSuccess"
                        @remove-file="handleRemoveFile"
                        @init-file-ids="handleInitFileIds"
                    ></upload-file>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';
import Constant from 'dms/constant/dict.js';
import UploadFile from 'dms/components/UploadFile.vue';

const { typeData, priorityData } = Constant;

export default {
    components: { formulaTitle, UploadFile },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 标题
            title: '提需求',
            addForm: {
                storyName: '',
                storyType: '',
                owner: [],
                deliveryDate: '',
                priority: '',
                scene: '',
                description: '',
                businessValue: '',
                earnings: ''
            },
            fileId: [],
            initialFileList: [],
            headers: {
                Accept: 'application/json, text/plain, */*',
                Authorization: `Bearer ${this.$tools.getToken()}`
            },
            typeData,
            priorityData,
            ownerData: [],
            maxSize: 20,
            accept: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'pdf', 'pptx', 'doc', 'docx', 'xlsx', 'zip', '7z', 'rar'],
            maxCount: 5,
            userRules: {
                storyName: [
                    {
                        required: true,
                        message: '请输入需求名称',
                        trigger: 'change'
                    }
                ],
                storyType: [
                    {
                        required: true,
                        message: '请选择类型',
                        trigger: 'change'
                    }
                ],
                owner: [
                    {
                        required: true,
                        message: '请选择需求负责人',
                        trigger: 'change'
                    }
                ],
                deliveryDate: [
                    {
                        required: true,
                        message: '请选择期望交付日期',
                        trigger: 'change'
                    }
                ],
                priority: [
                    {
                        required: true,
                        message: '请选择优先级',
                        trigger: 'change'
                    }
                ],
                scene: [
                    {
                        required: true,
                        message: '请输入用户场景',
                        trigger: 'blur'
                    }
                ],
                description: [
                    {
                        required: true,
                        message: '请输入需求描述',
                        trigger: 'blur'
                    }
                ]
            },
            mode: 'edit',
            id: ''
        };
    },
    computed: {
        uploadUrl() {
            return this.$service.dms.original.uploadFile();
        }
    },
    created() {
        if (this.$route?.query?.id) {
            this.mode = 'edit';
            this.id = this.$route.query.id;
            this.getDemandDetail();
        } else {
            this.mode = 'add';
        }
    },
    mounted() {
        this.getEmployees();
        this.addShift();
    },
    methods: {
        getTitleByType() {
            return '提需求';
        },
        addShift() {
            this.addForm = {
                storyName: '',
                storyType: '',
                owner: [],
                ownerName: [],
                deliveryDate: '',
                priority: '',
                scene: '',
                description: '',
                businessValue: '',
                earnings: ''
            };
            this.$nextTick(() => {
                if (this.$refs.dataForm) {
                    this.$refs.dataForm.resetFields();
                }
            });
        },
        // 查询需求负责人
        getEmployees() {
            const params = {
                isAll: '0'
            };
            params.status = this.$service.dms.original.getEmployees(params).then((res) => {
                if (res.code === '0000') {
                    this.ownerData = res.data || [];
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        // 文件上传成功后的处理逻辑
        handleUploadSuccess(res, file, fileList) {
            if (res.code === '0000') {
                this.$message.success(res.message);
                this.fileId.push(res.data);
            } else {
                this.$message.error(res.message);
            }
        },
        handleRemoveFile(file) {
            // 文件删除后的处理逻辑
            const id = file.response.data;
            this.fileId = this.fileId.filter((item) => item !== id);
        },
        // 处理初始文件ID列表
        handleInitFileIds(fileIds) {
            this.fileId = [...fileIds];
        },
        // 保存提交
        confirm(type) {
            this.$refs.dataForm.validate((valid) => {
                if (valid) {
                    const params = {
                        ...this.addForm,
                        source: '内部需求',
                        status: type === 'save' ? '保存' : '提交',
                        ownerName: this.addForm.owner
                            .map((id) => {
                                const employee = this.ownerData.find((item) => item.loginName === id);
                                return employee ? employee.employeeName : null;
                            })
                            .filter(Boolean)
                            .join(','),
                        owner: `${this.addForm.owner.map((id) => `,${id}`).join('')},`,
                        fileId: this.fileId
                    };
                    if (this.id) {
                        params.id = this.id;
                    }
                    params.status = this.$service.dms.original.addOriginal(params).then((res) => {
                        if (res.code === '0000') {
                            const msg = type === 'save' ? '原始需求保存成功' : '原始需求创建成功';
                            this.$message.success(msg);
                            this.$router.back();
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                }
            });
        },
        async getDemandDetail() {
            try {
                const params = {
                    id: this.$route?.query?.id
                };
                const api = this.$service.dms.demand.getDemandDetail;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message || '获取需求详情失败');
                    return;
                }

                const { data } = res;
                if (!data) {
                    this.$message.error('获取需求详情失败：数据为空');
                    return;
                }
                const { baseInfo, extendInfo, fileVoList } = data;
                const { storyName, storyType, owner, deliveryDate, priority } = baseInfo;
                const { scene, description, businessValue, earnings } = extendInfo;
                // 设置表单数据
                this.addForm = {
                    storyName,
                    storyType,
                    owner: owner.startsWith(',') ? owner.slice(1, -1).split(',') : owner.split(','),
                    deliveryDate,
                    priority,
                    scene,
                    description,
                    businessValue,
                    earnings
                };
                // 设置附件列表用于反显
                this.initialFileList = fileVoList || [];
            } catch (error) {
                console.error('获取需求详情失败:', error);
            }
        },
        // 返回上一页
        handleBack() {
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped>
.view-box {
    width: 100%;
    margin: 0px;
    padding: 20px;
    height: calc(100vh - 105px);
    overflow: auto;
    display: flex;
    flex-direction: column;
}
.sprint-btn {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
.add-flex {
    display: flex;
    justify-content: flex-start;
}
</style>
