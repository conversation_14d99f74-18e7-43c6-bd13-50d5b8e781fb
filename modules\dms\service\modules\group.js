/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 公共接口
        group: {
            // 根据部门/团队获取小组成员
            getMembers(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/team/getTeamMemberInfo',
                    method: 'post',
                    data
                });
            },
            // 新增小组成员
            addGroupMembers(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/team/addMember',
                    method: 'post',
                    data
                });
            },
            // 移除小组成员
            removeGroupMember(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/team/deleteMember',
                    method: 'get',
                    params: data
                });
            },
            // 移除小组
            removeGroup(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/team/deleteTeam',
                    method: 'get',
                    params: data
                });
            },
            // 新增小组与小组组长
            addGroupMembersAndGroupLeader(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/team/addTeam',
                    method: 'post',
                    data
                });
            },
            // 修改小组信息
            editGroupInfo(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/team/updateTeam',
                    method: 'post',
                    data
                });
            },
            // 将小组与项目或者团队关联起来
            relateTeamToProjectOrGroup(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/team/addTeamRelation',
                    method: 'post',
                    data
                });
            },
            // 项目页面新增小组(不传部门)
            addTeamWithoutDepartment(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/team/addProjectTeam',
                    method: 'post',
                    data
                });
            },
            // 获取团队列表（支持分页和筛选）
            getGroupList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/project/getProjectsPermission',
                    method: 'post',
                    data
                });
            },
            // 获取团队详情
            getGroupDetail(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/selectProjectInfo',
                    method: 'get',
                    params: data
                });
            },
            // 新增/修改团队
            updateGroup(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/updateTeam',
                    method: 'post',
                    data
                });
            },
            // 导入团队创建任务书
            import() {
                return `${basePath.systemApi.system}/dms/project/importTeamInfo`;
            }
        }
    };

    return service;
};
