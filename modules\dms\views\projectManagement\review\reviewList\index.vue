<template>
    <div class="project-management-list">
        <div class="content query-label-line2">
            <project-list
                v-model="activeNavTab"
                :nav-items="navItems"
                :query-params="queryParams"
                :query-config="queryConfig"
                :columns="columns"
                :data="productData"
                :load-data="query"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                :table-attrs="{ height: 'calc(100vh - 190px)' }"
                config-section="reviewList"
                :actions-width="100"
                @sort-change="handleSortChange"
                @search="handleSearch"
                @reset="handleReset"
                @nav-change="handleNavChange"
                @pagination="query"
            >
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button type="text" size="small" @click="handleView(row)">详情</el-button>
                    <!-- 已拒绝的才能编辑 ,同时配置角色权限-->
                    <el-button
                        v-if="updateAllProjectPermission && row.checkResult === '已拒绝'"
                        type="text"
                        size="small"
                        @click="handleEdit(row)"
                        >编辑</el-button
                    >
                    <!-- 待审核的才能审核 ,同时配置角色权限-->
                    <el-button
                        v-if="reviewProjectPermission && row.checkResult === '待审核'"
                        type="text"
                        size="small"
                        @click="handleAudit(row)"
                        >审核</el-button
                    >
                </template>
            </project-list>
        </div>
        <TemporaryProjectDialog
            :visible.sync="temporaryProjectDialogVisible"
            :projectId="currentProjectId"
            :type="editType"
            :checkId="currentCheckId"
            @success="query"
        />
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';
import TemporaryProjectDialog from 'dms/views/projectManagement/projectList/components/TemporaryProjectDialog.vue';
import { syncNavFromSearch } from 'dms/mixins/common.js';

export default {
    name: 'ProjectManagementList',
    components: {
        ProjectList,
        TemporaryProjectDialog
    },
    props: {},
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '',
            // 导航栏配置
            navItems,
            // 查询参数
            queryParams: this.$tools.cloneDeep(queryParams),
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 项目数据
            productData: [],
            // 分页配置
            total: 0,
            page: 1,
            limit: 20,
            // 新增/编辑/审核/详情
            editType: 'add',
            // 当前行项目ID
            currentProjectId: null,
            // 新增/编辑临时项目弹窗
            temporaryProjectDialogVisible: false,
            // 选中行的审核ID
            currentCheckId: ''
        };
    },
    computed: {
        updateAllProjectPermission() {
            return this.$store.state.permission.btnDatas.includes('Button-updateAllProject');
        },
        reviewProjectPermission() {
            return this.$store.state.permission.btnDatas.includes('Button-reviewProject');
        }
    },
    mounted() {
        this.query();
    },
    methods: {
        // 加载项目数据
        async query() {
            try {
                const params = {
                    currentPage: this.page,
                    pageSize: this.limit,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder,
                    ...this.params
                };

                // 日期处理
                if (params.checkDate && params.checkDate.length > 0) {
                    params.checkDateStart = params.checkDate[0];
                    params.checkDateEnd = params.checkDate[1];
                }
                if (params.applyDate && params.applyDate.length > 0) {
                    params.applyDateStart = params.applyDate[0];
                    params.applyDateEnd = params.applyDate[1];
                }
                if (params.startDate && params.startDate.length > 0) {
                    params.startDateStart = params.startDate[0];
                    params.startDateEnd = params.startDate[1];
                }
                if (params.planEndDate && params.planEndDate.length > 0) {
                    params.planEndDateStart = params.planEndDate[0];
                    params.planEndDateEnd = params.planEndDate[1];
                }

                const res = await this.$service.dms.project.getTobeReviewedTemporaryProjectList(params);

                if (res.code === '0000') {
                    this.productData = res.data?.list || [];
                    this.total = res.data?.total || 0;
                    // 添加序号
                    if (res?.data?.startRow) {
                        this.productData = this.productData.map((item, index) => {
                            item.index = res.data.startRow + index;
                            return item;
                        });
                    }
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                console.error('加载审核数据失败:', error);
            }
        },

        /**
         * 处理搜索
         * @param {Object} searchData - 搜索表单数据
         */
        handleSearch(searchData) {
            const { searchParams } = searchData;
            this.params = {
                ...searchParams
            };
            if (this.params.checkResult === '') {
                this.params.checkResult = null;
            }
            this.page = 1;
            // 搜索时同步导航状态 - 根据搜索条件自动设置对应的导航标签
            syncNavFromSearch(this);
            this.query();
        },

        /**
         * 处理重置
         */
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
            // 重置时重置到第一页
            this.page = 1;
            this.query();
        },

        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.queryParams[queryField] = field;
            this.params = this.$tools.cloneDeep(this.queryParams);
            this.query();
        },

        // 编辑项目
        handleEdit(row) {
            this.editType = 'edit';
            this.currentProjectId = row.projectId;
            this.currentCheckId = row.checkId;
            this.temporaryProjectDialogVisible = true;
        },

        // 查看项目详情
        handleView(row) {
            this.editType = 'view';
            this.currentProjectId = row.projectId;
            this.currentCheckId = row.checkId;
            this.temporaryProjectDialogVisible = true;
        },

        // 审核项目
        handleAudit(row) {
            this.editType = 'review';
            this.currentProjectId = row.projectId;
            this.currentCheckId = row.checkId;
            this.temporaryProjectDialogVisible = true;
        },

        /**
         * 处理排序变化
         * @param {Object} sortInfo - 排序信息 { column, prop, order }
         */
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            this.sortKey = prop;
            this.sortOrder = order;
            this.query();
        }
    }
};
</script>

<style lang="scss" scoped>
.right-nav-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 10px;
}
</style>
