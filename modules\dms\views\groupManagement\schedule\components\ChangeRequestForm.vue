<!-- 变更申请表单组件 -->
<template>
    <el-form :model="formData" ref="changeForm" label-width="100px" :rules="formRules">
        <el-form-item label="变更原因" prop="changeCause">
            <el-input
                type="textarea"
                v-model="formData.changeCause"
                maxlength="500"
                :rows="8"
                :disabled="disabled"
                placeholder="清晰、客观地说明为什么要进行变更。选择所有适用的选项并详细描述。
1、用户反馈： [例如：上线功能出现严重问题或收到关键用户的紧急需求。]
2、依赖阻塞： [例如：依赖的第三方服务或另一个团队的任务延迟，导致当前工作无法继续。]
3、技术风险暴露： [例如：在开发过程中发现了重大技术债务或架构问题，必须优先解决。]
4、资源变动： [例如：关键成员病假/离职，或资源被更高优先级的项目抽调。]
5、估算严重偏差： [例如：任务实际复杂度远超预期，原计划无法完成。]
6、需求调整： [例如：需求在开发过程中才发现逻辑不完整，需要产品经理重新梳理。]
7、其他原因：[请具体说明]
"
            ></el-input>
        </el-form-item>
        <el-form-item label="变更内容" prop="changeContent">
            <el-input
                type="textarea"
                v-model="formData.changeContent"
                maxlength="500"
                :rows="5"
                :disabled="disabled"
                placeholder="具体说明迭代计划中哪些部分发生了变化。例如：
1、[新增]紧急修复：支付失败BUG；
2、[移除]完成用户登录功能优化，因技术依赖未解决，移至下个迭代。
3、[修改] 开发A功能（简化版），因时间有限，先实现核心流程，高级功能后续迭代完成"
            ></el-input>
        </el-form-item>
        <el-divider content-position="left">变更影响: 从进度、质量、成本等方面分析变更影响</el-divider>
        <el-form-item label="影响分析" prop="changeImpact">
            <el-input
                type="textarea"
                v-model="formData.changeImpact"
                maxlength="500"
                :rows="5"
                :disabled="disabled"
                placeholder="全面分析此次变更对项目各方面可能产生的影响。
1、本次变更是否会影响最终版本的发布计划？[是/否，并说明影响]
2、是否需要为后续迭代调整资源安排？
3、是否需要通知市场部、销售部、管理层、测试人员等相关方 "
            ></el-input>
        </el-form-item>
        <el-form-item label="风险评估" prop="changeRisk">
            <el-input
                type="textarea"
                v-model="formData.changeRisk"
                maxlength="500"
                :rows="5"
                :disabled="disabled"
                placeholder="请输入风险评估"
            ></el-input>
        </el-form-item>
    </el-form>
</template>

<script>
export default {
    name: 'ChangeRequestForm',
    props: {
        // 初始表单数据
        initialData: {
            type: Object,
            default: () => ({
                changeCause: '',
                changeContent: '',
                changeImpact: '',
                changeRisk: ''
            })
        },
        // 是否禁用编辑
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            formData: {
                changeCause: '',
                changeContent: '',
                changeImpact: '',
                changeRisk: ''
            },
            formRules: {
                changeCause: [{ required: true, message: '请输入变更原因', trigger: 'blur' }],
                changeContent: [{ required: true, message: '请输入变更内容', trigger: 'blur' }],
                changeImpact: [{ required: true, message: '请输入影响分析', trigger: 'blur' }],
                changeRisk: [{ required: true, message: '请输入风险评估', trigger: 'blur' }]
            }
        };
    },
    watch: {
        initialData: {
            handler(newVal) {
                this.formData = { ...this.formData, ...newVal };
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        /**
         * 校验表单
         * @returns {Promise<boolean>} 校验结果
         */
        validate() {
            return new Promise((resolve) => {
                this.$refs.changeForm.validate((valid) => {
                    resolve(valid);
                });
            });
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.$refs.changeForm.resetFields();
        },
        /**
         * 获取表单数据
         * @returns {Object} 表单数据
         */
        getFormData() {
            return { ...this.formData };
        },
        /**
         * 设置表单数据
         * @param {Object} data 表单数据
         */
        setFormData(data) {
            this.formData = { ...this.formData, ...data };
        }
    }
};
</script>

<style lang="scss" scoped>
.el-divider__text {
    color: #497dfc;
}
</style>
