export const defaultColumns = [
    {
        type: 'selection',
        minWidth: 55,
        label: '选择框',
        columnManage: {
            sortableDisabled: true,
            // 固定在第一行
            pinnedFirst: true,
            widthDisabled: true
        }
    },
    {
        prop: 'demandId',
        label: 'ID',
        minWidth: 140,
        columnManage: {
            show: false,
            sortableDisabled: true
        }
    },
    {
        prop: 'demandName',
        label: '需求名称',
        minWidth: 300,
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'demandSource',
        label: '来源',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'sourceNo',
        label: '来源编号',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            show: false,
            sortableDisabled: true
        }
    },
    {
        prop: 'demandType',
        label: '类型',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'demandStatus',
        label: '状态',
        minWidth: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'demandProgress',
        label: '进度',
        minWidth: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            show: false,
            sortableDisabled: true
        }
    },
    {
        prop: 'costDeviation',
        label: '成本偏差',
        minWidth: 100,
        sortable: 'custom',
        attrs: {
            align: 'center'
        },
        columnManage: {
            show: false
        }
    },
    {
        prop: 'estimateHour',
        label: '预估工时',
        minWidth: 100,
        sortable: 'custom',
        render: (value) => (value ? `${value}d` : ''),
        attrs: {
            align: 'center'
        },
        columnManage: {
            show: false
        }
    },
    {
        prop: 'actualHour',
        label: '实际工时',
        sortable: 'custom',
        minWidth: 100,
        render: (value) => (value ? `${value}d` : ''),
        attrs: {
            align: 'center'
        },
        columnManage: {
            show: false
        }
    },
    {
        prop: 'publish',
        label: '发布',
        minWidth: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true,
            show: false
        }
    },
    {
        prop: 'deploy',
        label: '部署',
        minWidth: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true,
            show: false
        }
    },
    {
        prop: 'proposerName',
        label: '提出人',
        minWidth: 100,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'proposalTime',
        label: '提出时间',
        sortable: 'custom',
        minWidth: 100,
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'priority',
        label: '优先级',
        minWidth: 140,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'expectedDate',
        label: '期望交付日期',
        minWidth: 140,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    },
    {
        prop: 'responsiblePersonName',
        label: '需求负责人',
        minWidth: 140,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        minWidth: 140,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'assProductName',
        label: '关联产品',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'assProjectName',
        label: '关联团队/项目',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'changeTimes',
        label: '变更次数',
        minWidth: 120,
        sortable: 'custom',
        attrs: {
            align: 'center'
        }
    }
];
