<template>
    <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        width="800px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleClose"
    >
        <div class="change-confirm-content">
            <p>{{ dialogMessage }}</p>
            <p class="demand-name">{{ demandName }}</p>
            <a href="javascript:;" @click="handleViewDetail" class="detail-link">{{ detailLinkText }}</a>
            <!-- 对比描述列表 -->
            <el-descriptions class="change-descriptions" :column="1" border>
                <el-descriptions-item label="原始需求名称" labelClassName="desc-label">
                    <div class="source-content">
                        {{ changeData.demandNameAfter }}
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="原始需求来源" labelClassName="desc-label">
                    <div class="source-content">
                        <div class="source-left">{{ changeData.source }}</div>
                        <div class="source-right">{{ changeData.sourceNo }}</div>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="变更原因" labelClassName="desc-label">
                    <div class="source-content">
                        {{ changeData.reason }}
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="变更项" labelClassName="desc-label">
                    <div class="change-content">
                        <div class="change-row">
                            <div class="change-before">
                                <div class="change-label bold">变更前</div>
                            </div>
                            <div class="change-after">
                                <div class="change-label bold">变更后</div>
                            </div>
                        </div>
                    </div>
                </el-descriptions-item>

                <el-descriptions-item label="需求描述" labelClassName="desc-label">
                    <div class="change-content">
                        <div class="change-row">
                            <div class="change-before">
                                <div>{{ changeData.descriptionBefore }}</div>
                            </div>
                            <div class="change-after">
                                <div style="margin-left: 5px">{{ changeData.descriptionAfter }}</div>
                            </div>
                        </div>
                    </div>
                </el-descriptions-item>

                <el-descriptions-item label="期望交付日期" labelClassName="desc-label">
                    <div class="change-content">
                        <div class="change-row">
                            <div class="change-before">
                                <div class="change-label">{{ changeData.deliveryDateBefore }}</div>
                            </div>
                            <div class="change-after">
                                <div class="change-label">{{ changeData.deliveryDateAfter }}</div>
                            </div>
                        </div>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="分析人" labelClassName="desc-label">
                    <div class="change-content">
                        <div class="change-row">
                            <div class="change-before">
                                <div class="change-label">{{ changeData.ownerNameBefore }}</div>
                            </div>
                            <div class="change-after">
                                <div class="change-label">{{ changeData.ownerNameAfter }}</div>
                            </div>
                        </div>
                    </div>
                </el-descriptions-item>
            </el-descriptions>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">{{ cancelButtonText }}</el-button>
            <el-button type="primary" @click="handleConfirm">{{ confirmButtonText }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'ChangeConfirmDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        rowData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            changeData: {}
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        },
        demandName() {
            return this.rowData.demandName || '';
        },
        checkType() {
            return this.rowData.checkType || '';
        },
        dialogTitle() {
            return '需求变更确认';
        },
        dialogMessage() {
            if (this.checkType === '内容变更') {
                return '由于原始需求内容发生变更，请确认以下需求是否受到影响并要进行变更：';
            } else if (this.checkType === '负责人变更') {
                return '由于原始需求的负责人发生变更，请确认是否移除以下需求:';
            }
            return '';
        },
        detailLinkText() {
            return '查看原始需求详情';
        },
        confirmButtonText() {
            if (this.checkType === '内容变更') {
                return '变更需求';
            } else if (this.checkType === '负责人变更') {
                return '保留需求';
            }
            return '确认';
        },
        cancelButtonText() {
            if (this.checkType === '内容变更') {
                return '没有影响';
            } else if (this.checkType === '负责人变更') {
                return '移除需求';
            }
            return '取消';
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getDamandCheckDetail();
            }
        }
    },
    methods: {
        /**
         * 查看详情
         */
        handleViewDetail() {
            this.$emit('view-detail', this.rowData);
        },
        /**
         * 获取变更前内容
         */
        async getDamandCheckDetail() {
            const params = {
                demandId: this.rowData.demandId
            };
            const api = this.$service.dms.demand.getUserOrProductDamandCheckDetail;
            try {
                const res = await api(params);
                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.changeData = res.data || {};
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 确认操作
         */
        async handleConfirm() {
            try {
                if (this.checkType === '内容变更') {
                    await this.handleContentChangeConfirm();
                } else if (this.checkType === '负责人变更') {
                    await this.handleOwnerChangeConfirm();
                }
            } catch (error) {
                console.error('确认操作失败:', error);
                this.$message.error('操作失败');
            }
        },

        /**
         * 取消操作
         */
        async handleCancel() {
            try {
                if (this.checkType === '内容变更') {
                    await this.handleContentChangeCancel();
                } else if (this.checkType === '负责人变更') {
                    await this.handleOwnerChangeCancel();
                }
            } catch (error) {
                console.error('取消操作失败:', error);
                this.$message.error('操作失败');
            }
        },
        /**
         * 内容变更 - 确认变更需求
         */
        async handleContentChangeConfirm() {
            const params = {
                storyId: this.rowData.demandId,
                checkId: this.rowData.checkId,
                storyVersion: this.rowData.demandVersion,
                status: '已接受',
                checkAction: '变更需求',
                reason: '由于原始需求内容发生变更，经确认，本需求也需要变更'
            };

            const res = await this.$service.dms.original.updateStoryCheck(params);
            if (res.code === '0000') {
                this.$message.success(res.message);
                this.$emit('success', { action: 'change', rowData: this.rowData });
                this.dialogVisible = false;
            } else {
                this.$message.error(res.message);
            }
        },

        /**
         * 内容变更 - 没有影响
         */
        async handleContentChangeCancel() {
            const params = {
                storyId: this.rowData.demandId,
                checkId: this.rowData.checkId,
                storyVersion: this.rowData.demandVersion,
                status: '已接受',
                checkAction: '没有影响',
                reason: '原始需求内容发生变更，经确认，本需求没有影响'
            };

            const res = await this.$service.dms.original.updateStoryCheck(params);
            if (res.code === '0000') {
                this.$message.success(res.message);
                this.$emit('success', { action: 'no-impact' });
                this.dialogVisible = false;
            } else {
                this.$message.error(res.message);
            }
        },

        /**
         * 负责人变更 - 保留需求
         */
        async handleOwnerChangeConfirm() {
            const params = {
                storyId: this.rowData.demandId,
                checkId: this.rowData.checkId,
                storyVersion: this.rowData.demandVersion,
                status: '已接受',
                checkAction: '保留需求',
                reason: '原始需求负责人发生变更，经确认，保留本需求'
            };

            const res = await this.$service.dms.original.updateStoryCheck(params);
            if (res.code === '0000') {
                this.$message.success(res.message);
                this.$emit('success', { action: 'keep' });
                this.dialogVisible = false;
            } else {
                this.$message.error(res.message);
            }
        },

        /**
         * 负责人变更 - 移除需求
         */
        async handleOwnerChangeCancel() {
            const params = {
                demandId: this.rowData.demandId
            };

            const res = await this.$service.dms.original.removeDemand(params);
            if (res.code === '0000') {
                this.$message.success(res.message);
                this.$emit('success', { action: 'remove' });
                this.dialogVisible = false;
            } else {
                this.$message.error(res.message);
            }
        },

        /**
         * 关闭弹窗
         */
        handleClose() {
            this.dialogVisible = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.change-confirm-content {
    text-align: center;

    p {
        margin: 0 0 16px 0;
        line-height: 1.5;
    }

    .demand-name {
        font-weight: bold;
        color: #333;
        font-size: 14px;
        margin: 20px 0;
    }

    .detail-link {
        color: #5081fb;
        font-size: 12px;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }
}

.notice-text {
    font-size: 14px;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.5;

    .highlight {
        color: #e74c3c;
        font-weight: bold;
    }
}

.change-descriptions {
    width: 100%;
    padding: 10px 0;
    ::v-deep .el-descriptions__label {
        background-color: #f9f9f9;
        font-weight: bold;
        text-align: center;
        width: 120px;
        vertical-align: middle;
    }

    ::v-deep .el-descriptions__content {
        padding: 12px;
    }

    .title-content {
        text-align: center;
        font-weight: bold;
    }

    .source-content {
        display: flex;
        align-items: center;

        .source-left {
            flex: 1;
        }

        .source-right {
            flex: 1;
        }
    }

    .change-content {
        .change-row {
            display: flex;

            .change-before,
            .change-after {
                flex: 1;

                .change-label {
                    text-align: center;
                }
                .bold {
                    font-weight: bold;
                }
                .bold {
                    font-weight: bold;
                }
                .change-text {
                    line-height: 1.4;
                    word-break: break-all;
                }
            }

            .change-before {
                border-right: 1px solid #ddd;
            }
        }
    }

    .normal-content {
        line-height: 1.4;
        word-break: break-all;
    }

    .date-content {
        text-align: center;
        line-height: 1.4;
    }

    .analyst-content {
        text-align: center;
        line-height: 1.4;
    }
}

.flex {
    display: flex;
}
.font-weight-800 {
    font-weight: 800;
}
.content {
    width: 100%;
    height: 100%;
    padding-top: 10px;
    font-size: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.mt-10 {
    margin-top: 10px;
}

.detail-btn {
    margin-top: 20px;
    text-decoration: underline;
}

::v-deep .desc-label {
    background-color: #f9f9f9;
    font-weight: bold;
    text-align: center;
    width: 120px;
    vertical-align: middle;
}
.dialog-footer {
    text-align: center;
}
.el-checkbox {
    text-align: right;
    width: 100%;
    padding-right: 10px;
}
</style>
