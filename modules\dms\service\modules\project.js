/**
 * 项目管理相关服务接口
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 项目管理接口
        project: {
            // 获取项目详情
            getProjectDetail(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/selectProjectInfo',
                    method: 'get',
                    params: data
                });
            },
            // 获取项目列表
            getProjectList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/selectProjectList',
                    method: 'post',
                    data
                });
            },
            // 下载项目信息
            downloadProjectInfo(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/file/download',
                    method: 'get',
                    responseType: 'blob',
                    params
                });
            },
            // 创建/编辑正式项目
            createFormalProject(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/updateProject',
                    method: 'post',
                    data
                });
            },
            // 创建/编辑临时项目
            createTemporaryProject(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/project/updateTmpProject',
                    method: 'post',
                    data
                });
            },
            // 待审核临时项目列表
            getTobeReviewedTemporaryProjectList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/projectCheck/selectCheckList',
                    method: 'post',
                    data
                });
            },
            // 审核临时项目
            reviewTemporaryProject(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/projectCheck/checkProject',
                    method: 'post',
                    data
                });
            },
            // 待确认正式项目列表
            getTobeConfirmedTemporaryProjectList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/projectCheck/selectConfirmList',
                    method: 'get',
                    data
                });
            },
            // 正式项目确认
            confirmProject(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/projectCheck/confirmProject',
                    method: 'post',
                    data
                });
            },
            // 临时项目下拉框选项
            getTemporaryProjectList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/dms/projectCheck/selectTmpProjects',
                    method: 'get',
                    params: data
                });
            },
            // 导入项目立项任务书
            import() {
                return `${basePath.systemApi.system}/dms/project/importProjectInfo`;
            },
            // 获取公司立项项目列表
            getCompanyTemporaryProjectList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: '/companyProjectmanagement/getCompanyProjectInfoList',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
