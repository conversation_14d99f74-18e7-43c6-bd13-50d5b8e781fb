<template>
    <div class="nest-table-container">
        <nest-table
            style="margin-top: 5px"
            ref="nestTable"
            :data="demands"
            :columns="demandColumns"
            :nested-columns="taskColumns"
            nested-data-key="taskList"
            @field-change="handleFieldChange"
        ></nest-table>
    </div>
</template>

<script>
import NestTable from 'dms/components/NestTable/index.vue';
import dict from 'dms/constant/dict.js';

export default {
    name: 'NestedPlanList',
    components: { NestTable },
    props: {
        saveFlag: {
            type: Number,
            default: 0
        },
        list: {
            type: Array,
            default: () => []
        },
        // 是否所有选项全部禁用
        disabledAll: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 选中的需求行
            selectedMainRows: [],
            // 选中的任务行
            selectedNestedRows: [],
            // 所有选中的行
            allSelectedRows: [],
            demandColumns: [
                { prop: 'storyName', label: '需求名称', minWidth: 120 },
                { prop: 'storyDesc', label: '需求描述', minWidth: 220 },
                {
                    prop: 'deliveryDate',
                    label: '计划完成日期',
                    width: 100,
                    align: 'center'
                },
                {
                    prop: 'storyType',
                    label: '需求分类',
                    align: 'center'
                },
                {
                    prop: 'storyPri',
                    label: '优先级',
                    align: 'center'
                },
                {
                    prop: 'level',
                    label: '需求级别',
                    align: 'center'
                },
                {
                    prop: 'storyStatus',
                    label: '需求状态',
                    align: 'center'
                },
                {
                    prop: 'isFinished',
                    label: '是否达成目标',
                    align: 'center',
                    width: 90,
                    type: this.disabledAll ? '' : 'select',
                    editable: !this.disabledAll,
                    options: [
                        { label: '是', value: '是' },
                        { label: '否', value: '否' }
                    ]
                },
                {
                    prop: 'reasonDesc',
                    label: '原因说明',
                    align: 'center',
                    type: this.disabledAll ? '' : 'text',
                    editable: !this.disabledAll,
                    minWidth: 220,
                    rules: [{ validator: this.validateReasonDesc, trigger: 'blur' }]
                }
            ],
            menuItems: [
                { icon: 'dms-plan-add-multiply', title: '多人+多类型', handler: this.addMultipleTask },
                { icon: 'dms-plan-add-empty', title: '空白任务', handler: this.addEmptyTask },
                { type: 'delete', title: '删除', handler: this.deleteRow }
            ],
            // 关联需求弹窗
            relateDemandDialogVisible: false,
            // 批量创建任务弹窗
            addMultipleTaskDialogVisible: false
        };
    },
    computed: {
        demands: {
            get() {
                return this.list;
            },
            set(value) {
                this.$emit('update:list', value);
            }
        },
        groupMembers() {
            return this.$store.state.dms.groupMembers;
        },
        taskColumns() {
            return [
                {
                    prop: 'taskName',
                    label: '任务名称',
                    editable: true,
                    disabled: true,
                    required: true
                },
                { prop: 'taskDesc', label: '任务描述', editable: true, disabled: true },
                {
                    prop: 'startDate',
                    label: '计划开始日期',
                    minWidth: 110,
                    align: 'center',
                    editable: true,
                    type: 'date',
                    required: true,
                    disabled: true
                },
                {
                    prop: 'endDate',
                    label: '计划完成日期',
                    minWidth: 110,
                    align: 'center',
                    editable: true,
                    type: 'date',
                    required: true,
                    disabled: true
                },
                {
                    prop: 'taskType',
                    label: '任务类型',
                    align: 'center',
                    editable: true,
                    type: 'select',
                    options: dict.taskTypeData,
                    required: true,
                    disabled: true
                },
                {
                    prop: 'assignedTo',
                    label: '责任人',
                    align: 'center',
                    editable: true,
                    type: 'people',
                    options: this.groupMembers,
                    required: true,
                    disabled: true
                },
                {
                    prop: 'estimate',
                    label: '预计工时',
                    align: 'center',
                    editable: true,
                    required: true,
                    type: 'number',
                    min: 0,
                    disabled: true
                }
            ];
        }
    },
    watch: {
        saveFlag(newVal) {
            if (newVal) {
                this.$emit('success', this.demands);
            }
        }
    },
    methods: {
        handleRelateDemand() {
            this.relateDemandDialogVisible = true;
        },
        // 验证原因说明字段
        validateReasonDesc(rule, value, callback) {
            // 需要找到当前行的数据来判断是否达成目标
            const fieldPath = rule.field;
            const match = fieldPath.match(/data\.(\d+)\.reasonDesc/);
            if (match) {
                const rowIndex = parseInt(match[1]);
                const currentRow = this.demands[rowIndex];
                if (currentRow && currentRow.isFinished === '否') {
                    if (!value || value.trim() === '') {
                        callback(new Error('是否达成目标为否时，原因说明为必填项'));
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            } else {
                callback();
            }
        },
        // 处理字段变化事件
        handleFieldChange(changeData) {
            const { row, column } = changeData;
            // 当"是否达成目标"字段变化时，重新验证"原因说明"字段
            if (column.prop === 'isFinished') {
                this.$nextTick(() => {
                    // 找到对应行的索引
                    const rowIndex = this.demands.findIndex((item) => item === row);
                    if (rowIndex !== -1) {
                        // 重新验证原因说明字段
                        const fieldName = `data.${rowIndex}.reasonDesc`;
                        this.$refs.nestTable.$refs.form.validateField(fieldName);
                    }
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.relate-demand-button {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 5px;
}
</style>
