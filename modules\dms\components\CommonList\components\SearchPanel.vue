<template>
    <collapsible-search-panel ref="collapsibleSearchPanel" v-bind="$attrs" v-on="$listeners">
        <template #rightNav>
            <div style="display: flex">
                <slot name="rightNav"> </slot>
                <snbc-table-column-manage
                    :columns="tableColumns"
                    :config-module="configModule"
                    :config-section="configSection"
                    :config-key="configKey"
                    @columns-change="handleColumnsChange"
                />
            </div>
        </template>
    </collapsible-search-panel>
</template>

<script>
import CollapsibleSearchPanel from 'dms/components/CollapsibleSearchPanel/index.vue';
import SnbcTableColumnManage from 'snbcCommon/components/snbc-table/SnbcTableColumnManage.vue';

export default {
    name: 'SearchPanel',
    components: {
        CollapsibleSearchPanel,
        SnbcTableColumnManage
    },
    props: {
        // 表格列配置
        tableColumns: {
            type: Array,
            default: () => []
        },
        // 列配置存储参数
        configModule: {
            type: String,
            default: 'datatable'
        },
        configSection: {
            type: String,
            default: 'commonBrowse'
        },
        configKey: {
            type: String,
            default: 'cols'
        }
    },
    methods: {
        // 处理列配置变化
        handleColumnsChange(columns) {
            this.$emit('columns-change', columns);
        }
    }
};
</script>
<style scoped>
.flex {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
