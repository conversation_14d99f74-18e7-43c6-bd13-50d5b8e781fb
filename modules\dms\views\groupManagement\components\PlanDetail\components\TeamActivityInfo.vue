<!-- 团队活动信息展示组件 -->
<template>
    <div class="team-activity-info">
        <el-table
            :data="list"
            class="dms-table"
            style="width: 100%"
            :header-cell-style="{ background: '#5470c6', color: '#fff', textAlign: 'center' }"
            :cell-style="{ textAlign: 'center' }"
        >
            <el-table-column prop="taskName" label="任务名称" min-width="200">
                <template slot-scope="scope">
                    <span>{{ scope.row.taskName || '-' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="taskType" label="任务类型" width="120">
                <template slot-scope="scope">
                    <span>{{ getTaskTypeLabel(scope.row.taskType) || '-' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="assignedTo" label="责任人" width="120">
                <template slot-scope="scope">
                    <span>{{ getAssignee<PERSON>abel(scope.row.assignedTo) || '-' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="startDate" label="计划开始日期" width="140">
                <template slot-scope="scope">
                    <span>{{ scope.row.startDate || '-' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="endDate" label="计划完成日期" width="140">
                <template slot-scope="scope">
                    <span>{{ scope.row.endDate || '-' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="estimate" label="预计工时" width="100">
                <template slot-scope="scope">
                    <span>{{ scope.row.estimate || '-' }}</span>
                </template>
            </el-table-column>
        </el-table>
        <div v-if="!list || list.length === 0" class="empty-data">
            <span>暂无数据</span>
        </div>
    </div>
</template>

<script>
import dict from 'dms/constant/dict.js';

export default {
    name: 'TeamActivityInfo',
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return { dict };
    },
    computed: {
        groupMembers() {
            return this.$store.state.dms.groupMembers;
        }
    },
    methods: {
        // 获取任务类型标签
        getTaskTypeLabel(value) {
            const item = this.dict.taskTypeData.find((item) => item.value === value);
            return item ? item.label : value;
        },

        // 获取责任人标签
        getAssigneeLabel(value) {
            const member = this.groupMembers.find((member) => member.loginName === value);
            return member ? member.employeeName : value;
        }
    }
};
</script>

<style lang="scss" scoped>
.team-activity-info {
    .empty-data {
        text-align: center;
        padding: 40px 0;
        color: #999;
        font-size: 14px;
    }
}
</style>
