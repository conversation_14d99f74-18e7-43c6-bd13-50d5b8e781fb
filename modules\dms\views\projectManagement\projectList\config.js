import CommonItems from 'snbcCommon/common/form-items.js';
import dictData from 'dms/constant/dict';

const { select, input, dateRange, departmentSelector, productLineSelector } = CommonItems;

// 部门
const orgCode = {
    ...departmentSelector,
    name: '部门',
    modelKey: 'orgCode'
};
// 产品线
const productLine = {
    ...productLineSelector,
    name: '产品线',
    modelKey: 'productLine'
};

// 项目名称
const projectName = {
    ...input,
    name: '项目名称',
    modelKey: 'projectName'
};

// 项目经理
const projectManager = {
    ...input,
    name: '项目经理',
    modelKey: 'pmName'
};

// 产品经理
const productManager = {
    ...input,
    name: '产品经理',
    modelKey: 'poName'
};

// 项目ID
const projectId = {
    ...input,
    name: '项目ID',
    modelKey: 'projectId'
};

// 涉及产品
const productName = {
    ...input,
    name: '涉及产品',
    modelKey: 'productName'
};

// 项目状态
const projectStatus = {
    ...select,
    name: '项目状态',
    modelKey: 'projectStatus',
    elOptions: dictData.projectStatusData
};

// 进度状态
const progressStatus = {
    ...select,
    name: '进度状态',
    modelKey: 'processStatus',
    elOptions: dictData.processStatusData
};

// 开始时间
const startTime = {
    ...dateRange,
    name: '开始时间',
    modelKey: 'startDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 计划结束时间
const plannedEndTime = {
    ...dateRange,
    name: '计划结束时间',
    modelKey: 'planEndDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 实际结束时间
const actualEndTime = {
    ...dateRange,
    name: '实际结束时间',
    modelKey: 'endDate',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 公司立项项目
const companyProjectTime = {
    ...input,
    name: '公司立项项目',
    modelKey: 'companyProjectName'
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [
        orgCode,
        productLine,
        projectName,
        projectManager,
        productManager,
        projectId,
        productName,
        projectStatus,
        progressStatus,
        startTime,
        plannedEndTime,
        actualEndTime,
        companyProjectTime
    ]
};

// 查询条件参数
export const queryParams = {
    orgCode: '',
    productLine: '',
    projectName: '',
    pmName: '',
    poName: '',
    projectId: '',
    productName: '',
    projectStatus: '',
    processStatus: '',
    startDate: [],
    planEndDate: [],
    endDate: [],
    companyProjectName: ''
};

// 导航栏配置
export const navItems = [
    { field: '', name: '所有', queryField: 'projectStatus' },
    { field: '待启动', name: '待启动', queryField: 'projectStatus' },
    { field: '进行中', name: '进行中', queryField: 'projectStatus' },
    { field: '暂停', name: '暂停', queryField: 'projectStatus' },
    { field: '结项', name: '结项', queryField: 'projectStatus' },
    { field: '终止', name: '终止', queryField: 'projectStatus' }
];

// 产品列表表格列配置
export const productColumns = [
    {
        prop: 'index',
        label: '序号',
        minWidth: 80,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectId',
        label: 'ID',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'orgName',
        label: '部门',
        minWidth: 140,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productLine',
        label: '产品线',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'poName',
        label: '产品经理',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        showOverflowTooltip: false,
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectName',
        label: '项目名称',
        minWidth: 200,
        showOverflowTooltip: true,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'pmName',
        label: '项目经理',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'productName',
        label: '涉及产品',
        minWidth: 160,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'projectStatus',
        label: '项目状态',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'processStatus',
        label: '进度状态',
        minWidth: 120,
        sortable: 'custom',
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'startDate',
        label: '项目开始时间',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'planEndDate',
        label: '计划结束时间',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'endDate',
        label: '实际结束时间',
        minWidth: 120,
        attrs: {
            align: 'center'
        },
        columnManage: {
            sortableDisabled: true
        }
    },
    {
        prop: 'companyProjectName',
        label: '公司立项项目',
        minWidth: 200,
        columnManage: {
            sortableDisabled: true
        }
    }
];
