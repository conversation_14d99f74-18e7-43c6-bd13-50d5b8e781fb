<template>
    <div class="project-management-list">
        <div class="content query-label-line2">
            <project-list
                v-model="activeNavTab"
                :nav-items="navItems"
                :query-params="queryParams"
                :query-config="queryConfig"
                :columns="columns"
                :data="productData"
                :load-data="query"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                :table-attrs="{ height: 'calc(100vh - 190px)' }"
                :actions-width="160"
                config-section="productChangeList"
                @search="handleSearch"
                @reset="handleReset"
                @nav-change="handleNavChange"
                @pagination="query"
                @sort-change="handleSortChange"
            >
                <!-- 操作列插槽 -->
                <template #actions="{ row }">
                    <el-button
                        type="text"
                        size="small"
                        @click="handleEdit(row)"
                        v-if="row.checkResult !== '待审核' && row.checkResult !== '已通过'"
                        >编辑</el-button
                    >
                    <el-button type="text" size="small" @click="handleDetails(row)">详情</el-button>
                    <el-button type="text" size="small" @click="handleView(row)" v-if="row.checkResult === '待审核'"
                        >撤回</el-button
                    >
                    <el-button
                        type="text"
                        size="small"
                        style="color: #f56c6c"
                        @click="handleDelete(row)"
                        v-if="row.checkResult !== '待审核' && row.checkResult !== '已通过'"
                        >删除</el-button
                    >
                </template>
            </project-list>
        </div>
        <change-content ref="changeContentRef"></change-content>
    </div>
</template>

<script>
import ProjectList from 'dms/views/projectManagement/components/projectList/index.vue';
import { queryConfig, queryParams, navItems, productColumns } from './config.js';
import changeContent from './components/changeContent.vue';
import { saveParams, restoreParams, syncNavFromSearch } from 'dms/mixins/common.js';

export default {
    name: 'ChangeList',
    components: {
        ProjectList,
        changeContent
    },
    data() {
        return {
            // 当前激活的导航标签
            activeNavTab: '',
            // 导航栏配置
            navItems,
            // 查询参数
            queryParams,
            // 查询表单配置
            queryConfig,
            // 表格列配置
            columns: productColumns,
            // 产品数据
            productData: [],
            // 分页配置
            sortOrder: 'descending',
            sortKey: 'applyDate',
            total: 0,
            page: 1,
            limit: 20,
            // 实际使用的查询参数
            params: {}
        };
    },
    created() {
        // 优先使用保存的参数
        restoreParams(this, 'changeList_params', queryParams, {
            defaultSortKey: 'applyDate'
        });
        this.query();
    },
    beforeDestroy() {
        // 页面卸载时保存参数
        saveParams(this, 'changeList_params');
    },
    methods: {
        // 加载产品数据
        async query() {
            try {
                // 日期处理
                if (this.params.applyDate && this.params.applyDate.length > 0) {
                    this.params.createStartDate = this.params.applyDate[0];
                    this.params.createEndDate = this.params.applyDate[1];
                }
                if (this.params.checkDate && this.params.checkDate.length > 0) {
                    this.params.closedStartDate = this.params.checkDate[0];
                    this.params.closedEndDate = this.params.checkDate[1];
                }
                const params = {
                    currentPage: this.page,
                    pageSize: this.limit,
                    ...this.params,
                    type: '变更',
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder
                };
                const res = await this.$service.dms.product.getProductCheckList(params);
                if (res.code === '0000') {
                    this.productData = res.data?.list || [];
                    // 添加序号
                    if (res?.data?.startRow) {
                        this.productData = this.productData.map((item, index) => {
                            item.index = res.data.startRow + index;
                            return item;
                        });
                    }
                    this.total = res.data?.total || 0;
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error('系统异常');
            }
        },
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            this.sortKey = prop;
            this.sortOrder = order;
            this.query();
        },
        // 处理搜索
        handleSearch() {
            this.params = this.$tools.cloneDeep(this.queryParams);
            // 搜索时重置到第一页
            this.page = 1;
            // 搜索时同步导航状态 - 根据搜索条件自动设置对应的导航标签
            syncNavFromSearch(this);
            this.query();
        },
        // 处理重置
        handleReset() {
            this.params = this.$tools.cloneDeep(queryParams);
            // 重置时重置到第一页
            this.page = 1;
            this.query();
        },
        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.queryParams[queryField] = field;
            this.params = this.$tools.cloneDeep(this.queryParams);
            this.query();
        },
        // 编辑产品
        handleEdit(row) {
            this.$refs.changeContentRef.open(row);
        },
        /**
         * 详情
         * @param {Object} row 每行的数据
         */
        handleDetails(row) {
            this.$router.push({
                name: 'CheckDetails',
                query: { checkId: row.checkId, productId: row.productId, productName: row.productName }
            });
        },
        handleView(row) {
            this.$confirm('确定要撤销该条数据?', '撤销', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const params = {
                        checkId: row.checkId,
                        type: '撤回'
                    };
                    this.$service.dms.product.deleteChange(params).then((res) => {
                        if (res.code === '0000') {
                            this.$message.success(res.message);
                            this.query();
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                })
                .catch(() => {
                    this.$message.info('已取消改操作');
                });
        },
        // 删除产品
        async handleDelete(row) {
            this.$confirm('确定要删除该条数据?', '删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    const params = {
                        checkId: row.checkId,
                        type: '删除'
                    };
                    this.$service.dms.product.deleteChange(params).then((res) => {
                        if (res.code === '0000') {
                            this.$message.success(res.message);
                            this.query();
                        } else {
                            this.$message.error(res.message);
                        }
                    });
                })
                .catch(() => {
                    this.$message.info('已取消该操作');
                });
        }
    }
};
</script>

<style lang="scss" scoped>
.project-management-list {
    padding: 20px;
}
</style>
