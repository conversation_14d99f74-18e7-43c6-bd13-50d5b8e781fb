<!--计划管理 -->
<template>
    <div>
        <common-list
            v-model="activeTab"
            :nav-items="navItems"
            :query-config="queryConfig"
            :query-params="queryParams"
            :columns="tableColumns"
            :data="planList"
            :total="total"
            :page.sync="page"
            :limit.sync="size"
            :actions-width="200"
            @sort-change="handleSortChange"
            @search="query"
            @reset="handleReset"
            @nav-change="handleNavChange"
            @pagination="query"
        >
            <template #rightNav>
                <el-button type="text" @click="addPlan" class="add-plan-button"
                    ><i class="el-icon-plus"></i> 新增计划
                </el-button>
            </template>

            <!-- 计划名称列自定义渲染 -->
            <template #planName="{ row }">
                <el-button class="plan-name" @click="viewPlanDetail(row)" type="text">
                    {{ row.planName }}
                </el-button>
            </template>

            <!-- 需求情况列自定义渲染 -->
            <template #storySchedule="{ row }">
                <el-popover
                    v-if="row.storySchedule && row.storySchedule.sumNo"
                    placement="top"
                    width="200"
                    trigger="click"
                >
                    <div class="popover-content">
                        <div class="item">
                            <span>📝</span>
                            <span>需求总数量：{{ row.storySchedule.sumNo }}</span>
                        </div>
                        <div class="item">
                            <span>✅</span>
                            <span>完成率：{{ row.storySchedule.finishRatio }}%</span>
                        </div>
                        <div class="item">
                            <span class="icon" :style="{ background: getSegmentColor(0) }"></span>
                            <span>{{ segmentLabels[0] }}：{{ row.storySchedule.finishOnTime }}</span>
                        </div>
                        <div class="item">
                            <span class="icon" :style="{ background: getSegmentColor(1) }"></span>
                            <span>{{ segmentLabels[1] }}：{{ row.storySchedule.finishDelay }}</span>
                        </div>
                        <div class="item">
                            <span class="icon" :style="{ background: getSegmentColor(2) }"></span>
                            <span>{{ segmentLabels[2] }}：{{ row.storySchedule.underway }}</span>
                        </div>
                        <div class="item">
                            <span class="icon" :style="{ background: getSegmentColor(3) }"></span>
                            <span>{{ segmentLabels[3] }}：{{ row.storySchedule.delay }}</span>
                        </div>
                    </div>
                    <div slot="reference" class="requirement-progress">
                        <div
                            class="progress-segment"
                            v-if="row.storySchedule.finishOnTime > 0"
                            :style="{
                                width: (row.storySchedule.finishOnTime / row.storySchedule.sumNo) * 100 + '%',
                                background: getSegmentColor(0)
                            }"
                        >
                            <span>{{ row.storySchedule.finishOnTime }}</span>
                        </div>
                        <div
                            class="progress-segment"
                            v-if="row.storySchedule.finishDelay > 0"
                            :style="{
                                width: (row.storySchedule.finishDelay / row.storySchedule.sumNo) * 100 + '%',
                                background: getSegmentColor(1)
                            }"
                        >
                            <span>{{ row.storySchedule.finishDelay }}</span>
                        </div>
                        <div
                            class="progress-segment"
                            v-if="row.storySchedule.underway > 0"
                            :style="{
                                width: (row.storySchedule.underway / row.storySchedule.sumNo) * 100 + '%',
                                background: getSegmentColor(2)
                            }"
                        >
                            <span>{{ row.storySchedule.underway }}</span>
                        </div>
                        <div
                            class="progress-segment"
                            v-if="row.storySchedule.delay > 0"
                            :style="{
                                width: (row.storySchedule.delay / row.storySchedule.sumNo) * 100 + '%',
                                background: getSegmentColor(3)
                            }"
                        >
                            <span>{{ row.storySchedule.delay }}</span>
                        </div>
                    </div>
                </el-popover>
                <span v-else>
                    {{ row.storySchedule }}
                </span>
            </template>

            <!-- 任务情况列自定义渲染 -->
            <template #taskSchedule="{ row }">
                <el-popover
                    v-if="row.taskSchedule && row.taskSchedule.sumNo"
                    placement="top"
                    width="200"
                    trigger="click"
                >
                    <div class="popover-content">
                        <div class="item">
                            <span>📝</span>
                            <span>任务总数量：{{ row.taskSchedule.sumNo }}</span>
                        </div>
                        <div class="item">
                            <span>✅</span>
                            <span>完成率：{{ row.taskSchedule.finishRatio }}%</span>
                        </div>
                        <div class="item">
                            <span class="icon" :style="{ background: getSegmentColor(0) }"></span>
                            <span>{{ segmentLabels[0] }}：{{ row.taskSchedule.finishOnTime }}</span>
                        </div>
                        <div class="item">
                            <span class="icon" :style="{ background: getSegmentColor(1) }"></span>
                            <span>{{ segmentLabels[1] }}：{{ row.taskSchedule.finishDelay }}</span>
                        </div>
                        <div class="item">
                            <span class="icon" :style="{ background: getSegmentColor(2) }"></span>
                            <span>{{ segmentLabels[2] }}：{{ row.taskSchedule.underway }}</span>
                        </div>
                        <div class="item">
                            <span class="icon" :style="{ background: getSegmentColor(3) }"></span>
                            <span>{{ segmentLabels[3] }}：{{ row.taskSchedule.delay }}</span>
                        </div>
                    </div>
                    <div slot="reference" class="requirement-progress">
                        <div
                            class="progress-segment"
                            v-if="row.taskSchedule.finishOnTime > 0"
                            :style="{
                                width: (row.taskSchedule.finishOnTime / row.taskSchedule.sumNo) * 100 + '%',
                                background: getSegmentColor(0)
                            }"
                        >
                            <span>{{ row.taskSchedule.finishOnTime }}</span>
                        </div>
                        <div
                            class="progress-segment"
                            v-if="row.taskSchedule.finishDelay > 0"
                            :style="{
                                width: (row.taskSchedule.finishDelay / row.taskSchedule.sumNo) * 100 + '%',
                                background: getSegmentColor(1)
                            }"
                        >
                            <span>{{ row.taskSchedule.finishDelay }}</span>
                        </div>
                        <div
                            class="progress-segment"
                            v-if="row.taskSchedule.underway > 0"
                            :style="{
                                width: (row.taskSchedule.underway / row.taskSchedule.sumNo) * 100 + '%',
                                background: getSegmentColor(2)
                            }"
                        >
                            <span>{{ row.taskSchedule.underway }}</span>
                        </div>
                        <div
                            class="progress-segment"
                            v-if="row.taskSchedule.delay > 0"
                            :style="{
                                width: (row.taskSchedule.delay / row.taskSchedule.sumNo) * 100 + '%',
                                background: getSegmentColor(3)
                            }"
                        >
                            <span>{{ row.taskSchedule.delay }}</span>
                        </div>
                    </div>
                </el-popover>
                <span v-else>
                    {{ row.taskSchedule }}
                </span>
            </template>

            <!-- 操作列 -->
            <template #actions="{ row }">
                <el-button @click="editRow(row)" size="mini" type="text">编辑</el-button>
                <el-button @click="evaluteRow(row)" size="mini" type="text">冲刺评价</el-button>
                <el-button @click="cancelReview(row)" size="mini" type="text">取消审核</el-button>
                <el-button @click="changePlan(row)" size="mini" type="text">变更计划</el-button>
                <el-button @click="deleteRow(row)" size="mini" type="text" style="color: #f00">删除</el-button>
            </template>
        </common-list>
    </div>
</template>

<script>
import CommonList from 'dms/components/CommonList/index.vue';
import {
    dateTypeOptions,
    tableColumns,
    navItems,
    queryParams,
    queryConfig,
    segmentLabels,
    segmentColorMap,
    sortMap
} from './config.js';

export default {
    name: 'PlannedManagement',
    components: { CommonList },
    data() {
        return {
            dateTypeOptions,
            activeTab: '进行中',
            tableColumns,
            navItems,
            planList: [],
            queryParams: this.$tools.cloneDeep(queryParams),
            total: 0,
            // 存储图表实例，用于销毁
            chartInstances: {},
            segmentLabels,
            // 顶部导航栏的查询条件
            navQuery: { status: '进行中' },
            sortKey: '',
            sortOrder: '',
            page: 1,
            size: 20
        };
    },
    computed: {
        queryConfig() {
            const quickQueryItems = queryConfig.items.find((item) => item.modelKey === 'quickQuery');
            quickQueryItems.changeHandler = this.handleRadio;

            return queryConfig;
        },
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        },
        groupOptions() {
            return this.$store.state.dms.groupOption;
        }
    },
    watch: {
        groupValue(newVal) {
            if (newVal) {
                this.query();
            }
        }
    },
    mounted() {
        this.handleRadio('最近一年');
        this.groupValue && this.query();
    },
    beforeDestroy() {
        this.destroyAllCharts();
    },
    deactivated() {
        this.destroyAllCharts();
    },
    methods: {
        addPlan() {
            if (!this.groupValue) {
                this.$message.warning('请选择团队');
                return;
            }
            this.$router.push({
                path: './addPlan'
            });
        },
        /**
         * 查询
         */
        async query() {
            if (!this.groupValue) {
                this.$message.warning('请选择团队');
                return;
            }
            let startDate = '';
            let endDate = '';
            const { dateRange } = this.queryParams;
            if (Array.isArray(dateRange) && dateRange[0]) {
                startDate = dateRange[0];
                endDate = dateRange[1];
            }

            try {
                const params = {
                    ...this.queryParams,
                    ...this.navQuery,
                    startDate,
                    endDate,
                    projectId: this.groupValue,
                    sortKey: this.sortKey,
                    sortOrder: this.sortOrder,
                    currentPage: this.page,
                    pageSize: this.size
                };
                const api = this.$service.dms.plan.getPlanList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.planList = res.data.list;
                this.total = res.data.total || 0;
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },

        // 重置
        handleReset() {},

        handleRadio(dateType) {
            const target = dateTypeOptions.find((item) => item.dateType === dateType);
            this.queryParams.dateRange = target.dateRange;
        },
        
        viewPlanDetail(row) {
            this.$router.push({
                path: './planDetail',
                query: { id: row.id }
            });
        },
        // 需求计划
        getSegmentColor(index) {
            const color = segmentColorMap[index] || 'gray';

            // 如果是渐变对象，转换为CSS线性渐变
            if (typeof color === 'object' && color.type === 'linear') {
                const stops = color.colorStops.map((stop) => `${stop.color} ${stop.offset * 100}%`).join(', ');
                return `linear-gradient(to right, ${stops})`;
            }

            return color;
        },

        /**
         * 处理排序变化
         * @param {Object} sortInfo - 排序信息 { column, prop, order }
         */
        handleSortChange(sortInfo) {
            const { prop, order } = sortInfo;
            const orderMap = {
                descending: 'DESC',
                ascending: 'ASC'
            };
            this.sortKey = sortMap[prop];
            this.sortOrder = orderMap[order];
            this.query();
        },
        // 销毁所有图表
        destroyAllCharts() {
            Object.values(this.chartInstances).forEach((chart) => {
                if (chart) {
                    chart.dispose();
                }
            });
            this.chartInstances = {};
        },
        // 编辑行
        editRow(row) {
            this.$router.push({
                path: './addPlan',
                query: { id: row.id }
            });
        },

        // 删除行
        async deleteRow(row) {
            await this.$confirm('确认删除该项吗?', '提示', { type: 'warning' });
            try {
                const params = {
                    planId: row.id
                };
                const api = this.$service.dms.plan.deletePlan;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$message.success('删除成功');
                this.query();
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },
        // 取消审核
        async cancelReview(row) {
            try {
                const params = {
                    planId: row.id
                };
                const api = this.$service.dms.plan.cancelReview;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.$message.success('删除成功');
                this.query();
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },

        // 冲刺评价
        evaluteRow(row) {
            this.$router.push({
                path: './PlanEvalute',
                query: { id: row.id }
            });
        },
        // 变更计划
        changePlan(row) {
            this.$router.push({
                path: './planChange',
                query: { id: row.id }
            });
        },

        /**
         * 处理导航切换
         * @param {Object} navItem - 导航项数据
         */
        handleNavChange(navItem) {
            this.navQuery = {};
            const { field, queryField } = navItem;
            // 使用queryField的值作为键名，field的值作为键值
            this.navQuery[queryField] = field;
            this.query();
        }
    }
};
</script>

<style lang="scss" scoped>
.add-plan-button {
    margin-right: 20px;
}

::v-deep .query-area {
    margin-top: 10px;
    .area-search {
        height: 50px;
        display: flex;
        justify-content: space-between;
    }
}
// 表格列可点击样式
.requirement-progress {
    display: flex;
    cursor: pointer;
    height: 24px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .progress-segment {
        display: flex;
        justify-content: center;
        align-items: center;

        span {
            font-size: 12px;
            color: white;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        }
    }
}
// 工时图表样式
.work-hour-chart {
    width: 100%;
    height: 50px;
}
// popover 内容样式
.popover-content {
    .item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding: 4px 0;
        font-size: 13px;

        &:last-child {
            margin-bottom: 0;
        }
    }
    .icon {
        width: 12px;
        height: 12px;
        margin-right: 8px;
        border-radius: 2px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
}
</style>
