<template>
    <div style="padding: 10px 20px 15px 20px">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button type="primary" @click="submit('批准')">批准</el-button>
            <el-button type="primary" @click="submit('拒绝')">拒绝</el-button>
            <el-button type="primary" @click="() => this.$router.back()">返回</el-button>
        </div>
        <div>
            <PlanDetail></PlanDetail>
            <formula-title v-if="showChangeReason" title="变更原因"></formula-title>
            <change-request-form
                v-if="showChangeReason"
                ref="changeRequestForm"
                :initial-data="changeForm"
                :disabled="true"
            ></change-request-form>

            <formula-title :title="evaluationTitle"></formula-title>
            <el-form :model="ruleForm" ref="ruleForm" label-width="100px">
                <el-form-item label="审批意见" prop="desc">
                    <el-input
                        type="textarea"
                        v-model="ruleForm.desc"
                        placeholder="请填写您的审批意见或理由"
                        maxlength="500"
                        :rows="3"
                    ></el-input>
                </el-form-item>
            </el-form>
            <formula-title :title="recordTitle"></formula-title>
            <OperationRecord :collapseItems="collapseItems"></OperationRecord>
        </div>
    </div>
</template>

<script>
import formulaTitle from '../components/formulaTitle.vue';
import OperationRecord from 'dms/views/productManagement/components/operationRecord.vue';
import ChangeRequestForm from 'dms/views/groupManagement/schedule/components/ChangeRequestForm.vue';
import PlanDetail from 'dms/views/groupManagement/components/PlanDetail';

export default {
    name: 'PlanReviewAction',
    components: { formulaTitle, OperationRecord, PlanDetail, ChangeRequestForm },
    data() {
        return {
            planId: '',
            collapseItems: [],
            changeForm: {},
            // 标题
            evaluationTitle: '审核',
            recordTitle: '操作纪录',
            informationForm: {
                desc: ''
            },
            ruleForm: {
                desc: ''
            }
        };
    },
    computed: {
        showChangeReason() {
            if (Object.keys(this.changeForm).length === 0) return false;
            return !!this.changeForm.changeCause;
        }
    },
    mounted() {
        // 获取计划ID
        if (this.$route?.query?.id) {
            this.planId = this.$route?.query?.id;
        } else {
            this.$message.error('缺少计划ID参数');
            this.$router.back();
            return;
        }
        this.getActions();
        this.getChangeReason();
    },

    methods: {
        /**
         * 获取操作记录
         */
        async getActions() {
            try {
                const params = {
                    objectId: this.planId
                };
                const res = await this.$service.dms.product.getActions(params);
                if (res.code === '0000') {
                    this.collapseItems = res.data || [];
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        },
        /**
         * 获取变更原因
         */
        async getChangeReason() {
            try {
                const params = {
                    id: this.planId
                };
                const res = await this.$service.dms.plan.getChangeReason(params);
                if (res.code === '0000') {
                    this.changeForm = res.data || [];
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        },
        /**
         * 批准/拒绝
         */
        async submit(type) {
            if (type === '拒绝' && !this.ruleForm.desc) {
                this.$message.warning('请填写审批意见');
                return;
            }
            try {
                const params = {
                    id: this.planId,
                    checkResult: type,
                    checkDesc: this.informationForm.desc
                };
                const res = await this.$service.dms.plan.getChangeReason(params);
                if (res.code === '0000') {
                    this.$message.success('审核成功');
                    this.$router.back();
                } else {
                    this.$message.error(res.message);
                }
            } catch (error) {
                this.$message.error(error.message || '系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
.chart-box {
    width: 100%;
    height: 400px;

    display: flex;
    justify-content: space-between;
}
.chart-left {
    width: 30%;
    height: 100%;
}
.clearfix {
    height: 30px;
}
.chart-right {
    width: 100%;
    height: 100%;
}
.el-card {
    padding: 10px !important;
}
::v-deep .el-card__header {
    height: 30px !important;
}
.load-rate-item {
    border-bottom: 1px solid #dcdfe6;
}
::v-deep .el-tabs__content {
    padding: 3px 0 0 0;
}
</style>
