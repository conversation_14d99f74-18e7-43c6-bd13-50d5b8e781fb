import { getDateRange } from 'snbcCommon/common/picker-options.js';
import CommonItems from 'snbcCommon/common/form-items.js';

const { radio, dateRange } = CommonItems;

// 快捷查询配置
const quickQuery = {
    ...radio,
    name: '快捷查询',
    modelKey: 'quickQuery',
    type: 'button',
    elRadios: [
        { label: '上一年度', value: '上一年度' },
        { label: '本年度', value: '本年度' },
        { label: '最近一年', value: '最近一年' }
    ]
};

// 统计周期配置
const statisticsPeriod = {
    ...dateRange,
    name: '统计周期',
    modelKey: 'dateRange',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd',
        'clearable': false
    }
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [quickQuery, statisticsPeriod]
};

// 查询条件参数
export const queryParams = {
    dateRange: [],
    quickQuery: '最近一年'
};

// 日期区间选择配置
export const dateTypeOptions = [
    { dateType: '上一年度', dateRange: getDateRange('上一年度') },
    { dateType: '本年度', dateRange: getDateRange('本年度') },
    { dateType: '最近一年', dateRange: getDateRange('最近一年') }
];

// 表格列配置
export const tableColumns = [
    {
        label: '序号',
        type: 'index',
        width: 60,
        attrs: { align: 'center' }
    },
    {
        label: '计划名称',
        prop: 'planName',
        width: 260,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '版本',
        prop: 'version',
        width: 80,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '计划开始日期',
        prop: 'startDate',
        width: 130,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '计划完成日期',
        prop: 'endDate',
        width: 120,
        attrs: { align: 'center' }
    },
    {
        label: '版本状态',
        prop: 'status',
        width: 100,
        attrs: { align: 'center' }
    },
    {
        label: '审批人',
        prop: 'auditActor',
        width: 100,
        attrs: { align: 'center' }
    },
    {
        label: '审批日期',
        prop: 'auditTime',
        width: 120,
        attrs: { align: 'center' }
    },
    {
        label: '审核结果',
        prop: 'auditResult',
        width: 120,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '申请人',
        prop: 'applicant',
        width: 100,
        attrs: { align: 'center' }
    },
    {
        label: '申请日期',
        prop: 'applicationDate',
        width: 180,
        attrs: { align: 'center' }
    }
];

// 导航标签配置 - 将原来的tabList转换为navItems格式
export const navItems = [
    { field: '', name: '所有', queryField: 'status' },
    { field: '审核中', name: '审核中', queryField: 'status' },
    { field: '草稿', name: '草稿', queryField: 'status' },
    { field: '进行中', name: '进行中', queryField: 'status' },
    { field: '待修改', name: '待修改', queryField: 'status' },
    { field: '已关闭', name: '已关闭', queryField: 'status' }
];

// 表格字段与对应向后端接口字段的传值
export const sortMap = {
    planName: 'planName',
    version: 'version',
    planStartDate: 'start_date',
    auditResult: 'audit_result'
};
