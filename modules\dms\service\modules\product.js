/**
 * 需求管理相关服务接口
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        product: {
            // 查询产品线
            getProductLine(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'productLine/getProductLine',
                    method: 'get',
                    params
                });
            },
            // 查询产品列表
            getProductList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/product/getProductList',
                    method: 'post',
                    data
                });
            },
            // 查询我的产品
            getMyProductList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/product/getMyProductList',
                    method: 'post',
                    data
                });
            },
            // 查询产品详情
            getProductInfo(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/product/getProductInfo',
                    method: 'get',
                    params
                });
            },
            // 查询产品干系人详情
            getStakeholder(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/product/getStakeholder',
                    method: 'get',
                    params
                });
            },
            // 新增产品
            addProduct(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/product/insertProduct',
                    method: 'post',
                    data
                });
            },
            // 编辑产品
            editProduct(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: 'dms/product/updateProduct',
                    method: 'post',
                    data
                });
            },
            // 导入产品
            importProduct(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `dms/product/importProduct`,
                    method: 'post',
                    data
                });
            },
            // 下载产品导入模板
            toTemplateExcel(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `common/toTemplateExcel`,
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            },
            // 查询产品审核变更列表
            getProductCheckList(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `dms/product/getProductCheckList`,
                    method: 'post',
                    data
                });
            },
            // 撤销删除
            deleteChange(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `dms/product/deleteChange`,
                    method: 'get',
                    params
                });
            },
            // 暂停关闭
            closedProduct(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `dms/product/closedProduct`,
                    method: 'get',
                    params
                });
            },
            // 审核产品
            productCheck(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `dms/product/check`,
                    method: 'post',
                    data
                });
            },
            // 查询操作记录
            getActions(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `action/getActions`,
                    method: 'get',
                    params
                });
            },
            // 查询产品模块信息
            getProductModuleInfo(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `dms/product/getProductModuleInfo`,
                    method: 'get',
                    params: data
                });
            },
            // 产品变更
            changeProduct(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `dms/product/changeProduct`,
                    method: 'post',
                    data
                });
            },
            // 查询变更产品模块
            getProductChangeInfo(params) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `dms/product/getProductChangeInfo`,
                    method: 'get',
                    params
                });
            },
            updateChangeProduct(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `dms/product/updateChangeProduct`,
                    method: 'post',
                    data
                });
            },
            // 同步产品
            syncProduct(data) {
                return http({
                    baseDomain: basePath.systemApi.system,
                    url: `dms/product/syncProductModule`,
                    method: 'get',
                    params: data
                });
            }
        }
    };

    return service;
};
