<template>
    <div>
        <el-dialog
            :title="getTitleByType()"
            :visible.sync="localVisible"
            width="80%"
            class="custom-dialog"
            @close="cancel"
        >
            <div class="box-condition">
                <div v-if="this.row.demandClass === '子原始需求'">原始需求: {{ addForm.demandName }}</div>
                <div v-if="this.row.demandClass !== '子原始需求'">用户需求: {{ addForm.demandName }}</div>
                <div>
                    <el-button type="text" icon="el-icon-plus" @click="batchCreate()">快速创建</el-button>
                    <el-button type="text" icon="el-icon-plus" @click="addInterval()">新增需求</el-button>
                </div>
            </div>
            <el-table :data="intervals" style="width: 100%">
                <el-table-column label="序号" width="80" align="center">
                    <template #default="scope">
                        {{ scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column label="需求名称" align="center" width="240">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.storyName" placeholder="请输入需求名称" maxlength="64"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="需求描述" align="center" min-width="240">
                    <template slot-scope="scope">
                        <el-input
                            v-model="scope.row.description"
                            placeholder="请输入需求描述"
                            type="textarea"
                            :autosize="{ maxRows: 6, minRows: 2 }"
                            maxlength="500"
                        ></el-input>
                    </template>
                </el-table-column>
                <el-table-column
                    label="产品模块"
                    align="center"
                    width="200"
                    v-if="this.row.demandClass !== '子原始需求'"
                >
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.module" placeholder="请选择产品模块" clearable filterable>
                            <el-option v-for="item in moduleData" :key="item.id" :label="item.path" :value="item.id">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="需求等级" align="center" width="140">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.storyLevel" style="width: 100%" placeholder="请选择" clearable>
                            <el-option
                                v-for="item in storyLevelData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="优先级" align="center" width="120">
                    <template slot-scope="scope">
                        <el-select v-model="scope.row.priority" placeholder="请选择" clearable>
                            <el-option
                                v-for="item in priorityData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="期望交付日期" align="center" width="160">
                    <template slot-scope="scope">
                        <el-date-picker
                            v-model="scope.row.deliveryDate"
                            type="date"
                            placeholder="请选择期望交付日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            style="width: 100%"
                        >
                        </el-date-picker>
                    </template>
                </el-table-column>
                <el-table-column label="验收标准" align="center" min-width="240">
                    <template slot-scope="scope">
                        <el-input
                            v-model="scope.row.verify"
                            placeholder="请输入验收标准"
                            type="textarea"
                            :autosize="{ maxRows: 6, minRows: 2 }"
                            maxlength="500"
                        ></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" fixed="right">
                    <template slot-scope="scope">
                        <div class="rotateButton">
                            <el-button
                                type="danger"
                                icon="el-icon-delete-solid"
                                @click="deleteInterval(scope.$index)"
                            ></el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <span slot="footer">
                <el-button @click="cancel">返回</el-button>
                <el-button type="primary" @click="save" :disabled="isSave">保存</el-button>
            </span>
        </el-dialog>
        <BatchRequireDialog :visible.sync="batchDialogVisible" :batchType="batchType" @success="handleParseSuccess" />
    </div>
</template>

<script>
import Constant from 'dms/constant/dict.js';
import BatchRequireDialog from 'dms/views/demandManagement/components/BatchRequireDialog.vue';

const { storyLevelData, priorityData } = Constant;
const defaultOptionForSubDemand = {
    storyName: null,
    description: null,
    storyLevel: null,
    priority: null,
    deliveryDate: null,
    verify: null
};
const defaultOptionForUserDemand = {
    storyName: null,
    description: null,
    storyLevel: null,
    priority: null,
    deliveryDate: null,
    verify: null,
    module: null
};
export default {
    name: 'CreateRequirePop',
    components: { BatchRequireDialog },
    data() {
        return {
            // 弹窗信息
            localVisible: false,
            isSave: false,
            intervals: [],
            // 产品模块
            moduleData: [],
            addForm: {
                demandName: ''
            },
            storyLevelData,
            priorityData,
            row: {},
            batchDialogVisible: false,
            batchType: ''
        };
    },
    methods: {
        // 打开弹窗
        open(row) {
            this.localVisible = true;
            this.row = row;
            this.addForm.demandName = row.demandName;
            if (this.row.demandClass === '子原始需求') {
                this.intervals = Array.from({ length: 3 }, (i) => ({
                    ...defaultOptionForSubDemand,
                    storyLevel: row.demandLevel,
                    priority: row.priority,
                    deliveryDate: row.expectedDate
                }));
            } else {
                this.intervals = Array.from({ length: 3 }, (i) => ({
                    ...defaultOptionForUserDemand,
                    storyLevel: row.demandLevel,
                    priority: row.priority
                }));
            }
            this.getModuleData();
        },
        getTitleByType() {
            if (this.row.demandClass === '子原始需求') {
                return '批量创建用户需求';
            }
            return '批量创建产品需求';
        },
        // 查询产品模块
        getModuleData() {
            const params = {
                productId: this.row.assProductId
            };
            params.status = this.$service.dms.original.getProductModule(params).then((res) => {
                if (res.code === '0000') {
                    this.moduleData = res.data || [];
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        // 创建需求
        addInterval() {
            if (this.row.demandClass === '子原始需求') {
                this.intervals.push({
                    storyName: null,
                    description: null,
                    storyLevel: this.row.demandLevel,
                    priority: this.row.priority,
                    deliveryDate: this.row.expectedDate,
                    verify: null
                });
            } else {
                this.intervals.push({
                    storyName: null,
                    description: null,
                    storyLevel: this.row.demandLevel,
                    priority: this.row.priority,
                    deliveryDate: null,
                    verify: null,
                    module: null
                });
            }
        },
        deleteInterval(index) {
            this.intervals.splice(index, 1);
        },
        save() {
            for (let i = 0; i < this.intervals.length; i++) {
                const item = this.intervals[i];
                if (this.row.demandClass === '子原始需求') {
                    if (
                        !item.storyName ||
                        !item.description ||
                        !item.storyLevel ||
                        !item.priority ||
                        !item.deliveryDate
                    ) {
                        this.$message.error('请填写除验收标准外的所有数据!');
                        return;
                    }
                } else if (
                    !item.storyName ||
                    !item.description ||
                    !item.storyLevel ||
                    !item.priority ||
                    !item.deliveryDate ||
                    !item.module
                ) {
                    this.$message.error('请填写除验收标准外的所有数据!');
                    return;
                }
            }
            // 如果通过了验证，提交数据
            const params = {
                parentId: this.row.demandId
            };
            if (this.row.demandClass === '子原始需求') {
                params.storyClass = '用户需求';
            } else {
                params.storyClass = '产品需求';
            }
            if (this.row.demandClass !== '子原始需求') {
                this.intervals.forEach((item) => {
                    // eslint-disable-next-line no-shadow
                    const mapping = this.moduleData.find((mapping) => mapping.id === item.module);
                    if (mapping) {
                        item.moduleName = mapping.path;
                    }
                });
            }
            params.list = this.intervals || [];
            this.$service.dms.original.batchCreat(params).then((res) => {
                if (res.code === '0000') {
                    this.$message.success(res.message);
                    this.isSave = false;
                    this.localVisible = false;
                    this.$parent.query();
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        // 关闭弹窗
        cancel() {
            this.$nextTick(() => {
                this.localVisible = false;
            });
        },
        batchCreate() {
            this.batchType = this.row.demandClass === '子原始需求' ? 'user' : 'product';
            this.batchDialogVisible = true;
        },
        handleParseSuccess(data) {
            this.intervals = this.intervals.filter((i) => i.storyName);

            if (this.row.demandClass === '子原始需求') {
                this.intervals.push(
                    ...data.map((i) => ({
                        storyName: i.storyName,
                        description: i.description,
                        storyLevel: this.row.demandLevel,
                        priority: i.priority,
                        deliveryDate: this.row.expectedDate,
                        verify: i.verify
                    }))
                );
            } else {
                this.intervals.push(
                    ...data.map((i) => ({
                        storyName: i.storyName,
                        description: i.description,
                        storyLevel: this.row.demandLevel,
                        priority: i.priority,
                        deliveryDate: null,
                        verify: i.verify,
                        module: null
                    }))
                );
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.box-condition {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.rotateButton {
    display: flex;
    justify-content: space-around;
    align-items: center;
    cursor: pointer;
}
</style>
