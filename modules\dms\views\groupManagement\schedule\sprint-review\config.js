import { getDateRange } from 'snbcCommon/common/picker-options.js';
import CommonItems from 'snbcCommon/common/form-items.js';

const { radio, dateRange } = CommonItems;

// 快捷查询配置
const quickQuery = {
    ...radio,
    name: '快捷查询',
    modelKey: 'quickQuery',
    type: 'button',
    elRadios: [
        { label: '上一年度', value: '上一年度' },
        { label: '本年度', value: '本年度' },
        { label: '最近一年', value: '最近一年' }
    ]
};

// 统计周期配置
const statisticsPeriod = {
    ...dateRange,
    name: '统计周期',
    modelKey: 'dateRange',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd',
        'clearable': false
    }
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '120px'
    },
    items: [quickQuery, statisticsPeriod]
};

// 查询条件参数
export const queryParams = {
    dateRange: getDateRange('最近一年'),
    quickQuery: '最近一年'
};

// 日期区间选择配置
export const dateTypeOptions = [
    { dateType: '上一年度', dateRange: getDateRange('上一年度') },
    { dateType: '本年度', dateRange: getDateRange('本年度') },
    { dateType: '最近一年', dateRange: getDateRange('最近一年') }
];

// 表格列配置
export const tableColumns = [
    {
        label: '计划名称',
        prop: 'planName',
        width: 140,
        attrs: { align: 'center' }
    },
    {
        label: '计划开始日期',
        prop: 'startDate',
        width: 140,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '计划完成日期',
        prop: 'endDate',
        width: 140,
        attrs: { align: 'center' }
    },
    {
        label: '提交日期',
        prop: 'createDate',
        width: 140,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '提交人',
        prop: 'createUser',
        width: 100,
        attrs: { align: 'center' }
    },
    {
        label: '计划状态',
        prop: 'planStatus',
        width: 100,
        attrs: { align: 'center' }
    },
    {
        label: '冲刺结果',
        prop: 'sprintResult',
        width: 100,
        sortable: 'custom',
        attrs: { align: 'center' }
    },
    {
        label: '审核人',
        prop: 'auditActor',
        width: 100,
        attrs: { align: 'center' }
    },
    {
        label: '审核日期',
        prop: 'auditTime',
        width: 140,
        attrs: { align: 'center' }
    },
    {
        label: '审核意见',
        prop: 'auditDesc',
        width: 300,
        attrs: { align: 'center' }
    }
];

// 导航标签配置 - 将原来的tabList转换为navItems格式
export const navItems = [
    { field: '', name: '所有', queryField: 'sprintResult' },
    { field: '结论待定', name: '结论待定', queryField: 'sprintResult' },
    { field: '冲刺成功', name: '冲刺成功', queryField: 'sprintResult' },
    { field: '冲刺失败', name: '冲刺失败', queryField: 'sprintResult' }
];

// 表格字段与对应向后端接口字段的传值
export const sortMap = {
    startDate: 'start_date',
    createDate: 'createDate',
    sprintResult: 'sprint_result'
};
