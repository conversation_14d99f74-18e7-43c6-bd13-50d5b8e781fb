<template>
    <div class="project-management-list">
        <el-table class="dms-table" :data="productData" style="width: 100%">
            <el-table-column
                v-for="(column, index) in columns"
                :key="index"
                :prop="column.prop"
                :label="column.label"
                v-bind="column.attrs"
                header-align="center"
                :showOverflowTooltip="true"
            >
            </el-table-column>
            <el-table-column label="操作" width="100" header-align="center" fixed="right">
                <template slot-scope="{ row }">
                    <el-button type="text" size="small" @click="handleView(row)">详情</el-button>
                    <el-button type="text" size="small" @click="handleConfirm(row)">确认</el-button>
                </template>
            </el-table-column>
        </el-table>
        <FormalProjectDialog
            :visible.sync="formalProjectDialogVisible"
            :projectId="currentProjectId"
            :type="viewType"
            @success="query"
        ></FormalProjectDialog>
        <ProjectConfirmDialog
            :visible.sync="projectConfirmDialogVisible"
            :projectId="currentProjectId"
            @success="query"
        ></ProjectConfirmDialog>
    </div>
</template>

<script>
import { productColumns } from './config.js';
import FormalProjectDialog from 'dms/views/projectManagement/projectList/components/FormalProjectDialog.vue';
import ProjectConfirmDialog from './ProjectConfirmDialog.vue';

export default {
    name: 'ProjectManagementList',
    components: { FormalProjectDialog, ProjectConfirmDialog },
    data() {
        return {
            // 表格列配:置
            columns: productColumns,
            // 项目数据
            productData: [],
            // 分页配置
            total: 0,
            page: 1,
            limit: 20,
            // 当前选中的项目ID
            currentProjectId: null,
            // 查看正式项目详情弹窗
            formalProjectDialogVisible: false,
            // 项目确认弹窗
            projectConfirmDialogVisible: false,
            viewType: ''
        };
    },
    mounted() {
        this.query();
    },
    methods: {
        // 加载项目数据
        async query() {
            try {
                const params = {};
                const response = await this.$service.dms.project.getTobeConfirmedTemporaryProjectList(params);

                if (response.code === '0000') {
                    this.productData = response.data || [];
                    this.productData = this.productData.map((i, index) => ({ ...i, index }));
                } else {
                    this.$message.error(response.message || '获取项目列表失败');
                }
            } catch (error) {
                console.error('加载项目数据失败:', error);
                this.$message.error('加载项目数据失败');
            } finally {
                const isEmpty = this.productData.length === 0;

                // 通知父组件查询完成
                this.$emit('query-complete', isEmpty);
            }
        },

        // 查看正式项目详情
        handleView(row) {
            this.currentProjectId = row.projectId;
            this.viewType = 'view';
            this.formalProjectDialogVisible = true;
        },
        handleConfirm(row) {
            this.currentProjectId = row.projectId;
            this.projectConfirmDialogVisible = true;
        }
    }
};
</script>

<style lang="scss" scoped>
.right-nav-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 10px;
}
</style>
